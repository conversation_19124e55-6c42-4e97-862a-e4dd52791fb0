<?php

namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;

class CreateMembersAreaBannerTable extends Migration
{
    private $table = "members_area_banner";
    public function up()
    {
        $this->forge->addField([
			'banner_id' => [
				'type' => 'BIGINT',
				'constraint' => 20,
				'unsigned' => false,
				'auto_increment' => true,
			],
			'banner_title' => [
				'type' => 'VARCHAR',
				'constraint' => 255,
				'null' => true,
			],
			'banner_content' => [
				'type' => 'LONGTEXT',
				'null' => true,
			],
			'usage_counter' => [
				'type' => 'INT',
				'constraint' => 11,
				'default' => 0,
			],
			'active' => [
				'type' => "BIT",
				'default' => true,
			],
			'created_at' => [
				'type' => 'DATETIME',
			],
			'updated_at' => [
				'type' => 'DATETIME',
			],
			'deleted_at' => [
				'type' => 'DATETIME',
				'null' => true,
			],
			'inactived_at' => [
				'type' => 'DATETIME',
				'null' => true,
			],
		]);

		$this->forge->addKey('banner_id', true);
		$this->forge->createTable($this->table);
		$this->db->enableForeignKeyChecks();
    }

    public function down()
    {
        //
    }
}
