<?php

namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;

class AlterUserTable20230816 extends Migration
{
    private $table = "user";
    
    public function up()
    {
        if ($this->db->fieldExists('survey_data', $this->table)) return;
        $this->db->disableForeignKeyChecks();

        $fields = [
            'survey_data' => [
                'type' => 'VARCHAR',
                'constraint' => 250,
                'null' => true,
                'default' => '',
                'after' => 'flags'
            ]
        ];
        $this->forge->addColumn($this->table, $fields);

        $this->db->enableForeignKeyChecks();
    }

    public function down()
    {
        //
    }
}
