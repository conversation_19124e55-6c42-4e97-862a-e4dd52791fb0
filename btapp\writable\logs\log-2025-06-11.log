DEBUG - 2025-06-11 02:09:13 --> REQ ----------->
INFO - 2025-06-11 02:09:13 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 02:09:13 --> REQ ----------->
INFO - 2025-06-11 02:09:13 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 02:09:17 --> REQ ----------->
INFO - 2025-06-11 02:09:17 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2025-06-11 02:09:18 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 02:09:18 --> REQ ----------->
INFO - 2025-06-11 02:09:18 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 02:09:29 --> REQ ----------->testuser061025_@mailinator.com123123123123
DEBUG - 2025-06-11 02:09:29 --> testuser061025_@mailinator.com123123123123
DEBUG - 2025-06-11 02:09:30 --> btdbFindBy ---> 
DEBUG - 2025-06-11 02:09:31 --> btdbUpdate ---> INSERT INTO `start-user` (`user_pid`, `email`, `mode`, `password`, `first_name`, `last_name`, `login_token`, `ip_address`, `country`, `flags`, `cookies`, `created_at`, `updated_at`) VALUES ('d4ddd9d715e2b532', '<EMAIL>', 'test', '$P$B98H00HAPP542IiR8RA56jG.vFbaO.1', '', '', '$2y$09$3QsSF3JLjaVBjIJdhymLxePSqtwqwzFFZIX41PP3RnDuxatb8jurS', '::1', '', '{\"aiwp_logged_in\":null,\"mode\":\"test\",\"ppg\":\"14\",\"pp_ctaclr\":\"1559ED\",\"pmt\":\"rec\",\"vpay\":\"\",\"navmenu\":\"show\",\"splash\":\"off\",\"ailplogo\":\"off\",\"emailid\":\"\",\"theme_flag_name\":\"kt8typtb\",\"theme_flag_value\":\"arcana\"}', '{\"mode\":\"test\",\"_ga\":\"GA1.1.1137630434.1749607752\",\"locales\":\"en\",\"_gcl_au\":\"1.1.568387395.1749607752\",\"ci_session\":\"evjp2rucm5hi3envkkgo77b7mrhcrrrm\",\"landingpage\":\"socket.io\",\"__hstc\":\"*********.5f1f7b15f7b5234e27efd01064a93c3b.*************.*************.*************.1\",\"hubspotutk\":\"5f1f7b15f7b5234e27efd01064a93c3b\",\"__hssrc\":\"1\",\"mp_510eae1e2d2a79bceee18c49bece1c6a_mixpanel\":\"{\\u0022distinct_id\\u0022:\\u0022$device:23425872-2012-43a2-af3d-396ea119ff8f\\u0022,\\u0022$device_id\\u0022:\\u002223425872-2012-43a2-af3d-396ea119ff8f\\u0022,\\u0022$initial_referrer\\u0022:\\u0022$direct\\u0022,\\u0022$initial_referring_domain\\u0022:\\u0022$direct\\u0022,\\u0022__mps\\u0022:{},\\u0022__mpso\\u0022:{\\u0022$initial_referrer\\u0022:\\u0022$direct\\u0022,\\u0022$initial_referring_domain\\u0022:\\u0022$direct\\u0022},\\u0022__mpus\\u0022:{},\\u0022__mpa\\u0022:{},\\u0022__mpu\\u0022:{},\\u0022__mpr\\u0022:[],\\u0022__mpap\\u0022:[]}\",\"_ga_70TZ628CHH\":\"GS2.1.s1749607751$o1$g1$t1749607758$j53$l0$h0\",\"_tt_enable_cookie\":\"1\",\"_ttp\":\"01JXEBZER9P8MJ0XVAPCZE626P_.tt.0\",\"_fbp\":\"fb.0.*************.*****************\",\"__hssc\":\"*********.2.*************\"}', '2025-06-11 02:09:31', '2025-06-11 02:09:31')
DEBUG - 2025-06-11 02:09:31 --> btdbFindBy ---> INSERT INTO `start-user` (`user_pid`, `email`, `mode`, `password`, `first_name`, `last_name`, `login_token`, `ip_address`, `country`, `flags`, `cookies`, `created_at`, `updated_at`) VALUES ('d4ddd9d715e2b532', '<EMAIL>', 'test', '$P$B98H00HAPP542IiR8RA56jG.vFbaO.1', '', '', '$2y$09$3QsSF3JLjaVBjIJdhymLxePSqtwqwzFFZIX41PP3RnDuxatb8jurS', '::1', '', '{\"aiwp_logged_in\":null,\"mode\":\"test\",\"ppg\":\"14\",\"pp_ctaclr\":\"1559ED\",\"pmt\":\"rec\",\"vpay\":\"\",\"navmenu\":\"show\",\"splash\":\"off\",\"ailplogo\":\"off\",\"emailid\":\"\",\"theme_flag_name\":\"kt8typtb\",\"theme_flag_value\":\"arcana\"}', '{\"mode\":\"test\",\"_ga\":\"GA1.1.1137630434.1749607752\",\"locales\":\"en\",\"_gcl_au\":\"1.1.568387395.1749607752\",\"ci_session\":\"evjp2rucm5hi3envkkgo77b7mrhcrrrm\",\"landingpage\":\"socket.io\",\"__hstc\":\"*********.5f1f7b15f7b5234e27efd01064a93c3b.*************.*************.*************.1\",\"hubspotutk\":\"5f1f7b15f7b5234e27efd01064a93c3b\",\"__hssrc\":\"1\",\"mp_510eae1e2d2a79bceee18c49bece1c6a_mixpanel\":\"{\\u0022distinct_id\\u0022:\\u0022$device:23425872-2012-43a2-af3d-396ea119ff8f\\u0022,\\u0022$device_id\\u0022:\\u002223425872-2012-43a2-af3d-396ea119ff8f\\u0022,\\u0022$initial_referrer\\u0022:\\u0022$direct\\u0022,\\u0022$initial_referring_domain\\u0022:\\u0022$direct\\u0022,\\u0022__mps\\u0022:{},\\u0022__mpso\\u0022:{\\u0022$initial_referrer\\u0022:\\u0022$direct\\u0022,\\u0022$initial_referring_domain\\u0022:\\u0022$direct\\u0022},\\u0022__mpus\\u0022:{},\\u0022__mpa\\u0022:{},\\u0022__mpu\\u0022:{},\\u0022__mpr\\u0022:[],\\u0022__mpap\\u0022:[]}\",\"_ga_70TZ628CHH\":\"GS2.1.s1749607751$o1$g1$t1749607758$j53$l0$h0\",\"_tt_enable_cookie\":\"1\",\"_ttp\":\"01JXEBZER9P8MJ0XVAPCZE626P_.tt.0\",\"_fbp\":\"fb.0.*************.*****************\",\"__hssc\":\"*********.2.*************\"}', '2025-06-11 02:09:31', '2025-06-11 02:09:31')
INFO - 2025-06-11 02:09:31 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 02:09:31 --> btdbFindBy ---> SELECT `start-plan`.*, `start-account`.*, DATE_FORMAT(resumed_at , "%M %e, %Y") as "resumed_at_formatted", `start-user`.`mode`, case when DATE_ADD(end_date, INTERVAL 1 DAY) < now() then 'yes' else 'no' end as 'expired'
FROM `start-account`
JOIN `start-user` ON `start-user`.`user_id` = `start-account`.`user_id`
JOIN `start-plan` ON `start-plan`.`plan_id` = `start-account`.`plan_id`
WHERE `start-account`.`user_id` = '25'
ORDER BY `start-account`.`end_date` DESC
DEBUG - 2025-06-11 02:09:31 --> btdbFindBy ---> SELECT *
FROM `start-user_app`
WHERE `email` = '<EMAIL>'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-11 02:09:31 --> btdbUpdate ---> INSERT INTO `start-user_app` (`email`, `userapp_pid`, `created_at`, `updated_at`) VALUES ('<EMAIL>', 'b38b406f0ec9bf9a8ed43bcaaa9c7092', '2025-06-11 02:09:31', '2025-06-11 02:09:31')
DEBUG - 2025-06-11 02:09:31 --> btdbFindBy ---> INSERT INTO `start-user_app` (`email`, `userapp_pid`, `created_at`, `updated_at`) VALUES ('<EMAIL>', 'b38b406f0ec9bf9a8ed43bcaaa9c7092', '2025-06-11 02:09:31', '2025-06-11 02:09:31')
INFO - 2025-06-11 02:09:31 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 02:09:31 --> btdbFindBy ---> 
DEBUG - 2025-06-11 02:09:31 --> btdbFindBy ---> SELECT `start-plan`.*, `start-account`.*, DATE_FORMAT(resumed_at , "%M %e, %Y") as "resumed_at_formatted", `start-user`.`mode`, case when DATE_ADD(end_date, INTERVAL 1 DAY) < now() then 'yes' else 'no' end as 'expired'
FROM `start-account`
JOIN `start-user` ON `start-user`.`user_id` = `start-account`.`user_id`
JOIN `start-plan` ON `start-plan`.`plan_id` = `start-account`.`plan_id`
WHERE `start-account`.`user_id` = '25'
ORDER BY `start-account`.`end_date` DESC
DEBUG - 2025-06-11 02:09:33 --> btdbFindBy ---> SELECT *
FROM `start-user_app`
WHERE `email` = '<EMAIL>'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
WARNING - 2025-06-11 02:09:33 --> [DEPRECATED] explode(): Passing null to parameter #2 ($string) of type string is deprecated in APPPATH\Models\BTModel.php on line 52.
 1 APPPATH\Models\BTModel.php(52): explode(',', null)
 2 APPPATH\Helpers\btdb_helper.php(180): App\Models\BTModel->findBy([...], null)
 3 APPPATH\Controllers\FlowPages\Payment.php(64): btdbFindBy('PlanModel', 'plan_id', null)
 4 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\FlowPages\Payment->index()
 5 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\FlowPages\Payment))
 6 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 7 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 8 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
INFO - 2025-06-11 02:09:33 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 02:09:33 --> REQ ----------->14
INFO - 2025-06-11 02:09:35 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 02:09:35 --> btdbFindBy ---> 
DEBUG - 2025-06-11 02:09:35 --> btdbFindBy ---> SELECT `start-plan`.*, `start-account`.*, DATE_FORMAT(resumed_at , "%M %e, %Y") as "resumed_at_formatted", `start-user`.`mode`, case when DATE_ADD(end_date, INTERVAL 1 DAY) < now() then 'yes' else 'no' end as 'expired'
FROM `start-account`
JOIN `start-user` ON `start-user`.`user_id` = `start-account`.`user_id`
JOIN `start-plan` ON `start-plan`.`plan_id` = `start-account`.`plan_id`
WHERE `start-account`.`user_id` = '25'
ORDER BY `start-account`.`end_date` DESC
DEBUG - 2025-06-11 02:09:36 --> btdbFindBy ---> SELECT *
FROM `start-user_app`
WHERE `email` = '<EMAIL>'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
INFO - 2025-06-11 02:09:55 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 02:09:55 --> btdbFindBy ---> 
DEBUG - 2025-06-11 02:09:55 --> btdbFindBy ---> SELECT *
FROM `start-user`
WHERE `user_pid` = 'd4ddd9d715e2b532'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
WARNING - 2025-06-11 02:09:55 --> [DEPRECATED] Optional parameter $_api_key declared before required parameter $_mode is implicitly treated as a required parameter in VENDORPATH\btapp\btcore\src\Payment\Stripe.php on line 15.
 1 SYSTEMPATH\Autoloader\Autoloader.php(314): include_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\vendor\\codeigniter4\\framework\\system\\Debug\\Exceptions.php')
 2 SYSTEMPATH\Autoloader\Autoloader.php(250): CodeIgniter\Autoloader\Autoloader->includeFile('C:\\laragon\\www\\app.ai-pro.org\\btapp\\vendor\\composer/../btapp/btcore/src/Payment/Stripe.php')
 3 APPPATH\Controllers\Api.php(2227): CodeIgniter\Autoloader\Autoloader->loadClassmap('BTCore\\Payment\\Stripe')
 4 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->executeStripeDirectLink('4706362bf9b557c6b591993cb4f6a393')
 5 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 6 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 7 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 8 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-06-11 02:10:01 --> btdbUpdate ---> INSERT INTO `start-account` (`account_pid`, `user_id`, `plan_id`, `merchant`, `merchant_customer_id`, `merchant_subscription_id`, `trial_end`, `start_date`, `end_date`, `cancelled_at`, `max_tokens`, `created_at`, `updated_at`) VALUES ('9d3334c85f0e9553', '25', '16', 'Stripe', 'cus_STbJRvbiTGIgMH', 'sub_1RYe6sLtUaKDxQEZDpl2tvVK', 0, '2025-06-11 02:09:50', '2025-07-11 02:09:50', 0, '1000000', '2025-06-11 02:10:01', '2025-06-11 02:10:01')
DEBUG - 2025-06-11 02:10:01 --> btdbUpdate ---> INSERT INTO `start-payment` (`acctpmt_pid`, `account_id`, `user_id`, `amount`, `currency`, `charge_id`, `merchant`, `mode`, `created_at`, `updated_at`) VALUES ('468c70d1cd2f5966', 14, '25', '23.85', 'USD', 'ch_3RYe6tLtUaKDxQEZ1jhqflra', 'Stripe', 'test', '2025-06-11 02:10:01', '2025-06-11 02:10:01')
DEBUG - 2025-06-11 02:10:01 --> btdbFindBy ---> INSERT INTO `start-payment` (`acctpmt_pid`, `account_id`, `user_id`, `amount`, `currency`, `charge_id`, `merchant`, `mode`, `created_at`, `updated_at`) VALUES ('468c70d1cd2f5966', 14, '25', '23.85', 'USD', 'ch_3RYe6tLtUaKDxQEZ1jhqflra', 'Stripe', 'test', '2025-06-11 02:10:01', '2025-06-11 02:10:01')
DEBUG - 2025-06-11 02:10:01 --> btdbUpdate ---> UPDATE `start-user` SET `orig_email` = '<EMAIL>', `is_fraud` = '', `status` = 'active', `updated_at` = '2025-06-11 02:10:01'
WHERE `start-user`.`user_id` IN ('25')
DEBUG - 2025-06-11 02:10:01 --> btdbFindBy ---> SELECT `start-plan`.*, `start-account`.*, DATE_FORMAT(resumed_at , "%M %e, %Y") as "resumed_at_formatted", `start-user`.`mode`, case when DATE_ADD(end_date, INTERVAL 1 DAY) < now() then 'yes' else 'no' end as 'expired'
FROM `start-account`
JOIN `start-user` ON `start-user`.`user_id` = `start-account`.`user_id`
JOIN `start-plan` ON `start-plan`.`plan_id` = `start-account`.`plan_id`
WHERE `start-account`.`user_id` = '25'
ORDER BY `start-account`.`end_date` DESC
DEBUG - 2025-06-11 02:10:02 --> btdbFindBy ---> SELECT *
FROM `start-user_app`
WHERE `email` = '<EMAIL>'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
WARNING - 2025-06-11 02:10:02 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 113.
 1 APPPATH\Helpers\btflag_helper.php(113): setcookie('pricing', null, **********, '', '')
 2 APPPATH\Controllers\Api.php(5567): btflag_remove('pricing')
 3 APPPATH\Controllers\Api.php(2341): App\Controllers\Api->doAfterPayment([...])
 4 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->executeStripeDirectLink('4706362bf9b557c6b591993cb4f6a393')
 5 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 6 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 7 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 8 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-11 02:10:02 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 114.
 1 APPPATH\Helpers\btflag_helper.php(114): setcookie('pricing', null, **********, '/', '')
 2 APPPATH\Controllers\Api.php(5567): btflag_remove('pricing')
 3 APPPATH\Controllers\Api.php(2341): App\Controllers\Api->doAfterPayment([...])
 4 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->executeStripeDirectLink('4706362bf9b557c6b591993cb4f6a393')
 5 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 6 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 7 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 8 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-11 02:10:02 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 113.
 1 APPPATH\Helpers\btflag_helper.php(113): setcookie('daily', null, **********, '', '')
 2 APPPATH\Controllers\Api.php(5575): btflag_remove('daily')
 3 APPPATH\Controllers\Api.php(2341): App\Controllers\Api->doAfterPayment([...])
 4 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->executeStripeDirectLink('4706362bf9b557c6b591993cb4f6a393')
 5 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 6 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 7 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 8 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-11 02:10:02 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 114.
 1 APPPATH\Helpers\btflag_helper.php(114): setcookie('daily', null, **********, '/', '')
 2 APPPATH\Controllers\Api.php(5575): btflag_remove('daily')
 3 APPPATH\Controllers\Api.php(2341): App\Controllers\Api->doAfterPayment([...])
 4 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->executeStripeDirectLink('4706362bf9b557c6b591993cb4f6a393')
 5 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 6 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 7 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 8 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
INFO - 2025-06-11 02:10:02 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
WARNING - 2025-06-11 02:10:02 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 113.
 1 APPPATH\Helpers\btflag_helper.php(113): setcookie('pricing', null, **********, '', '')
 2 APPPATH\Controllers\FlowPages\ThankYou.php(68): btflag_remove('pricing')
 3 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\FlowPages\ThankYou->index()
 4 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\FlowPages\ThankYou))
 5 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 6 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 7 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-11 02:10:02 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 114.
 1 APPPATH\Helpers\btflag_helper.php(114): setcookie('pricing', null, **********, '/', '')
 2 APPPATH\Controllers\FlowPages\ThankYou.php(68): btflag_remove('pricing')
 3 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\FlowPages\ThankYou->index()
 4 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\FlowPages\ThankYou))
 5 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 6 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 7 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-11 02:10:02 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 113.
 1 APPPATH\Helpers\btflag_helper.php(113): setcookie('pricing', null, **********, '', '.ai-pro.org')
 2 APPPATH\Controllers\FlowPages\ThankYou.php(69): btflag_remove('pricing', null, [...])
 3 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\FlowPages\ThankYou->index()
 4 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\FlowPages\ThankYou))
 5 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 6 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 7 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-11 02:10:02 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 114.
 1 APPPATH\Helpers\btflag_helper.php(114): setcookie('pricing', null, **********, '/', '.ai-pro.org')
 2 APPPATH\Controllers\FlowPages\ThankYou.php(69): btflag_remove('pricing', null, [...])
 3 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\FlowPages\ThankYou->index()
 4 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\FlowPages\ThankYou))
 5 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 6 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 7 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-06-11 02:10:02 --> REQ ----------->$2y$09$3QsSF3JLjaVBjIJdhymLxePSq
DEBUG - 2025-06-11 02:10:02 --> btdbFindBy ---> 
INFO - 2025-06-11 02:10:03 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 02:10:03 --> REQ ----------->$2y$09$3QsSF3JLjaVBjIJdhymLxePSq
INFO - 2025-06-11 02:10:03 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
WARNING - 2025-06-11 02:10:03 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('aiproStart', null, 1752199803, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(389): btflag_set('aiproStart', null, [...])
 3 APPPATH\Controllers\Api.php(792): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-11 02:10:03 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('user_email', null, 1752199803, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(390): btflag_set('user_email', null, [...])
 3 APPPATH\Controllers\Api.php(792): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-11 02:10:03 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('chatpro_KJLF4XgSL8wjlGm', null, 1752199803, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(391): btflag_set('chatpro_KJLF4XgSL8wjlGm', null, [...])
 3 APPPATH\Controllers\Api.php(792): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
INFO - 2025-06-11 02:10:05 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 02:10:05 --> btdbFindBy ---> 
DEBUG - 2025-06-11 02:10:05 --> btdbFindBy ---> SELECT `start-plan`.*, `start-account`.*, DATE_FORMAT(resumed_at , "%M %e, %Y") as "resumed_at_formatted", `start-user`.`mode`, case when DATE_ADD(end_date, INTERVAL 1 DAY) < now() then 'yes' else 'no' end as 'expired'
FROM `start-account`
JOIN `start-user` ON `start-user`.`user_id` = `start-account`.`user_id`
JOIN `start-plan` ON `start-plan`.`plan_id` = `start-account`.`plan_id`
WHERE `start-account`.`user_id` = '25'
ORDER BY `start-account`.`end_date` DESC
DEBUG - 2025-06-11 02:10:06 --> btdbFindBy ---> SELECT *
FROM `start-user_app`
WHERE `email` = '<EMAIL>'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-11 02:10:06 --> btdbFindBy ---> SELECT *
FROM `start-plan`
WHERE `plan_id` = '16'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-11 02:10:06 --> REQ ----------->$2y$09$3QsSF3JLjaVBjIJdhymLxePSq
DEBUG - 2025-06-11 02:10:06 --> btdbFindBy ---> 
INFO - 2025-06-11 02:10:06 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 02:10:07 --> REQ ----------->$2y$09$3QsSF3JLjaVBjIJdhymLxePSq
INFO - 2025-06-11 02:10:07 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
WARNING - 2025-06-11 02:10:07 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('aiproStart', null, 1752199807, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(389): btflag_set('aiproStart', null, [...])
 3 APPPATH\Controllers\Api.php(792): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-11 02:10:07 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('user_email', null, 1752199807, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(390): btflag_set('user_email', null, [...])
 3 APPPATH\Controllers\Api.php(792): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-11 02:10:07 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('chatpro_KJLF4XgSL8wjlGm', null, 1752199807, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(391): btflag_set('chatpro_KJLF4XgSL8wjlGm', null, [...])
 3 APPPATH\Controllers\Api.php(792): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-06-11 02:10:07 --> REQ ----------->
DEBUG - 2025-06-11 02:10:07 --> REQ ----------->
INFO - 2025-06-11 02:10:07 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 02:10:14 --> REQ ----------->$2y$09$3QsSF3JLjaVBjIJdhymLxePSq
INFO - 2025-06-11 02:10:14 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
WARNING - 2025-06-11 02:10:14 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('aiproStart', null, 1752199814, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(389): btflag_set('aiproStart', null, [...])
 3 APPPATH\Controllers\Api.php(792): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-11 02:10:14 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('user_email', null, 1752199814, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(390): btflag_set('user_email', null, [...])
 3 APPPATH\Controllers\Api.php(792): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-11 02:10:14 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('chatpro_KJLF4XgSL8wjlGm', null, 1752199814, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(391): btflag_set('chatpro_KJLF4XgSL8wjlGm', null, [...])
 3 APPPATH\Controllers\Api.php(792): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
INFO - 2025-06-11 02:10:15 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 02:10:15 --> btdbFindBy ---> 
DEBUG - 2025-06-11 02:10:15 --> btdbFindBy ---> SELECT `start-plan`.*, `start-account`.*, DATE_FORMAT(resumed_at , "%M %e, %Y") as "resumed_at_formatted", `start-user`.`mode`, case when DATE_ADD(end_date, INTERVAL 1 DAY) < now() then 'yes' else 'no' end as 'expired'
FROM `start-account`
JOIN `start-user` ON `start-user`.`user_id` = `start-account`.`user_id`
JOIN `start-plan` ON `start-plan`.`plan_id` = `start-account`.`plan_id`
WHERE `start-account`.`user_id` = '25'
ORDER BY `start-account`.`end_date` DESC
DEBUG - 2025-06-11 02:10:16 --> btdbFindBy ---> SELECT *
FROM `start-user_app`
WHERE `email` = '<EMAIL>'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-11 02:10:17 --> REQ ----------->$2y$09$3QsSF3JLjaVBjIJdhymLxePSq
INFO - 2025-06-11 02:10:17 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
WARNING - 2025-06-11 02:10:17 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('aiproStart', null, **********, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(389): btflag_set('aiproStart', null, [...])
 3 APPPATH\Controllers\Api.php(792): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-11 02:10:17 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('user_email', null, **********, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(390): btflag_set('user_email', null, [...])
 3 APPPATH\Controllers\Api.php(792): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-11 02:10:17 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('chatpro_KJLF4XgSL8wjlGm', null, **********, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(391): btflag_set('chatpro_KJLF4XgSL8wjlGm', null, [...])
 3 APPPATH\Controllers\Api.php(792): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-06-11 02:10:17 --> REQ ----------->
INFO - 2025-06-11 02:10:17 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 02:10:22 --> REQ ----------->$2y$09$3QsSF3JLjaVBjIJdhymLxePSq
DEBUG - 2025-06-11 02:10:22 --> btdbFindBy ---> 
INFO - 2025-06-11 02:10:22 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2025-06-11 02:10:22 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 02:10:22 --> btdbFindBy ---> 
DEBUG - 2025-06-11 02:10:22 --> btdbFindBy ---> SELECT `start-plan`.*, `start-account`.*, DATE_FORMAT(resumed_at , "%M %e, %Y") as "resumed_at_formatted", `start-user`.`mode`, case when DATE_ADD(end_date, INTERVAL 1 DAY) < now() then 'yes' else 'no' end as 'expired'
FROM `start-account`
JOIN `start-user` ON `start-user`.`user_id` = `start-account`.`user_id`
JOIN `start-plan` ON `start-plan`.`plan_id` = `start-account`.`plan_id`
WHERE `start-account`.`user_id` = '25'
ORDER BY `start-account`.`end_date` DESC
DEBUG - 2025-06-11 02:10:23 --> btdbFindBy ---> SELECT *
FROM `start-user_app`
WHERE `email` = '<EMAIL>'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-11 02:10:23 --> btdbFindBy ---> SELECT *
FROM `start-plan`
WHERE `plan_id` = '16'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-11 02:10:24 --> REQ ----------->$2y$09$3QsSF3JLjaVBjIJdhymLxePSq
INFO - 2025-06-11 02:10:24 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
WARNING - 2025-06-11 02:10:24 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('aiproStart', null, **********, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(389): btflag_set('aiproStart', null, [...])
 3 APPPATH\Controllers\Api.php(792): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-11 02:10:24 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('user_email', null, **********, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(390): btflag_set('user_email', null, [...])
 3 APPPATH\Controllers\Api.php(792): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-11 02:10:24 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('chatpro_KJLF4XgSL8wjlGm', null, **********, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(391): btflag_set('chatpro_KJLF4XgSL8wjlGm', null, [...])
 3 APPPATH\Controllers\Api.php(792): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-06-11 02:10:24 --> REQ ----------->$2y$09$3QsSF3JLjaVBjIJdhymLxePSq
DEBUG - 2025-06-11 02:10:24 --> btdbFindBy ---> 
INFO - 2025-06-11 02:10:24 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 02:10:24 --> REQ ----------->$2y$09$3QsSF3JLjaVBjIJdhymLxePSq
INFO - 2025-06-11 02:10:24 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
WARNING - 2025-06-11 02:10:24 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('aiproStart', null, **********, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(389): btflag_set('aiproStart', null, [...])
 3 APPPATH\Controllers\Api.php(792): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-11 02:10:24 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('user_email', null, **********, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(390): btflag_set('user_email', null, [...])
 3 APPPATH\Controllers\Api.php(792): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-11 02:10:24 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('chatpro_KJLF4XgSL8wjlGm', null, **********, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(391): btflag_set('chatpro_KJLF4XgSL8wjlGm', null, [...])
 3 APPPATH\Controllers\Api.php(792): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-06-11 02:10:24 --> REQ ----------->
DEBUG - 2025-06-11 02:10:25 --> REQ ----------->
INFO - 2025-06-11 02:10:25 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 02:10:35 --> REQ ----------->$2y$09$3QsSF3JLjaVBjIJdhymLxePSq
INFO - 2025-06-11 02:10:35 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
WARNING - 2025-06-11 02:10:35 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('aiproStart', null, 1752199835, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(389): btflag_set('aiproStart', null, [...])
 3 APPPATH\Controllers\Api.php(792): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-11 02:10:35 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('user_email', null, 1752199835, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(390): btflag_set('user_email', null, [...])
 3 APPPATH\Controllers\Api.php(792): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-11 02:10:35 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('chatpro_KJLF4XgSL8wjlGm', null, 1752199835, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(391): btflag_set('chatpro_KJLF4XgSL8wjlGm', null, [...])
 3 APPPATH\Controllers\Api.php(792): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-06-11 02:10:36 --> REQ ----------->$2y$09$3QsSF3JLjaVBjIJdhymLxePSq
INFO - 2025-06-11 02:10:36 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 02:10:38 --> REQ ----------->$2y$09$3QsSF3JLjaVBjIJdhymLxePSq
DEBUG - 2025-06-11 02:10:38 --> btdbFindBy ---> 
INFO - 2025-06-11 02:10:38 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 02:10:42 --> REQ ----------->$2y$09$3QsSF3JLjaVBjIJdhymLxePSq
DEBUG - 2025-06-11 02:10:42 --> btdbFindBy ---> 
INFO - 2025-06-11 02:10:42 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 02:10:42 --> REQ ----------->$2y$09$3QsSF3JLjaVBjIJdhymLxePSq
INFO - 2025-06-11 02:10:42 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 02:10:45 --> REQ ----------->$2y$09$3QsSF3JLjaVBjIJdhymLxePSq
INFO - 2025-06-11 02:10:45 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
WARNING - 2025-06-11 02:10:45 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('aiproStart', null, 1752199845, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(389): btflag_set('aiproStart', null, [...])
 3 APPPATH\Controllers\Api.php(792): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-11 02:10:45 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('user_email', null, 1752199845, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(390): btflag_set('user_email', null, [...])
 3 APPPATH\Controllers\Api.php(792): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-11 02:10:45 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('chatpro_KJLF4XgSL8wjlGm', null, 1752199845, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(391): btflag_set('chatpro_KJLF4XgSL8wjlGm', null, [...])
 3 APPPATH\Controllers\Api.php(792): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-06-11 02:10:51 --> REQ ----------->$2y$09$3QsSF3JLjaVBjIJdhymLxePSq
DEBUG - 2025-06-11 02:10:51 --> btdbFindBy ---> 
INFO - 2025-06-11 02:10:51 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 02:10:52 --> REQ ----------->$2y$09$3QsSF3JLjaVBjIJdhymLxePSq
INFO - 2025-06-11 02:10:52 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 02:11:01 --> REQ ----------->$2y$09$3QsSF3JLjaVBjIJdhymLxePSq
INFO - 2025-06-11 02:11:01 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
WARNING - 2025-06-11 02:11:01 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('aiproStart', null, 1752199861, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(389): btflag_set('aiproStart', null, [...])
 3 APPPATH\Controllers\Api.php(792): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-11 02:11:01 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('user_email', null, 1752199861, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(390): btflag_set('user_email', null, [...])
 3 APPPATH\Controllers\Api.php(792): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-11 02:11:01 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('chatpro_KJLF4XgSL8wjlGm', null, 1752199861, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(391): btflag_set('chatpro_KJLF4XgSL8wjlGm', null, [...])
 3 APPPATH\Controllers\Api.php(792): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
INFO - 2025-06-11 02:11:03 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 02:11:03 --> btdbFindBy ---> 
DEBUG - 2025-06-11 02:11:04 --> btdbFindBy ---> SELECT `start-plan`.*, `start-account`.*, DATE_FORMAT(resumed_at , "%M %e, %Y") as "resumed_at_formatted", `start-user`.`mode`, case when DATE_ADD(end_date, INTERVAL 1 DAY) < now() then 'yes' else 'no' end as 'expired'
FROM `start-account`
JOIN `start-user` ON `start-user`.`user_id` = `start-account`.`user_id`
JOIN `start-plan` ON `start-plan`.`plan_id` = `start-account`.`plan_id`
WHERE `start-account`.`user_id` = '25'
ORDER BY `start-account`.`end_date` DESC
DEBUG - 2025-06-11 02:11:04 --> btdbFindBy ---> SELECT *
FROM `start-user_app`
WHERE `email` = '<EMAIL>'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-11 02:11:05 --> REQ ----------->$2y$09$3QsSF3JLjaVBjIJdhymLxePSq
INFO - 2025-06-11 02:11:05 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
WARNING - 2025-06-11 02:11:05 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('aiproStart', null, **********, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(389): btflag_set('aiproStart', null, [...])
 3 APPPATH\Controllers\Api.php(792): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-11 02:11:05 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('user_email', null, **********, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(390): btflag_set('user_email', null, [...])
 3 APPPATH\Controllers\Api.php(792): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-11 02:11:05 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('chatpro_KJLF4XgSL8wjlGm', null, **********, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(391): btflag_set('chatpro_KJLF4XgSL8wjlGm', null, [...])
 3 APPPATH\Controllers\Api.php(792): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-06-11 02:11:05 --> REQ ----------->$2y$09$3QsSF3JLjaVBjIJdhymLxePSq
DEBUG - 2025-06-11 02:11:05 --> btdbFindBy ---> 
INFO - 2025-06-11 02:11:05 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 02:11:31 --> REQ ----------->$2y$09$3QsSF3JLjaVBjIJdhymLxePSq
INFO - 2025-06-11 02:11:31 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
WARNING - 2025-06-11 02:11:31 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('aiproStart', null, 1752199891, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(389): btflag_set('aiproStart', null, [...])
 3 APPPATH\Controllers\Api.php(792): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-11 02:11:31 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('user_email', null, 1752199891, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(390): btflag_set('user_email', null, [...])
 3 APPPATH\Controllers\Api.php(792): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-11 02:11:31 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('chatpro_KJLF4XgSL8wjlGm', null, 1752199891, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(391): btflag_set('chatpro_KJLF4XgSL8wjlGm', null, [...])
 3 APPPATH\Controllers\Api.php(792): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
INFO - 2025-06-11 02:11:32 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 02:11:32 --> btdbFindBy ---> 
DEBUG - 2025-06-11 02:11:32 --> btdbFindBy ---> SELECT `start-plan`.*, `start-account`.*, DATE_FORMAT(resumed_at , "%M %e, %Y") as "resumed_at_formatted", `start-user`.`mode`, case when DATE_ADD(end_date, INTERVAL 1 DAY) < now() then 'yes' else 'no' end as 'expired'
FROM `start-account`
JOIN `start-user` ON `start-user`.`user_id` = `start-account`.`user_id`
JOIN `start-plan` ON `start-plan`.`plan_id` = `start-account`.`plan_id`
WHERE `start-account`.`user_id` = '25'
ORDER BY `start-account`.`end_date` DESC
DEBUG - 2025-06-11 02:11:33 --> btdbFindBy ---> SELECT *
FROM `start-user_app`
WHERE `email` = '<EMAIL>'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-11 02:11:33 --> REQ ----------->$2y$09$3QsSF3JLjaVBjIJdhymLxePSq
INFO - 2025-06-11 02:11:33 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
WARNING - 2025-06-11 02:11:33 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('aiproStart', null, **********, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(389): btflag_set('aiproStart', null, [...])
 3 APPPATH\Controllers\Api.php(792): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-11 02:11:33 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('user_email', null, **********, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(390): btflag_set('user_email', null, [...])
 3 APPPATH\Controllers\Api.php(792): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-11 02:11:33 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('chatpro_KJLF4XgSL8wjlGm', null, **********, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(391): btflag_set('chatpro_KJLF4XgSL8wjlGm', null, [...])
 3 APPPATH\Controllers\Api.php(792): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-06-11 02:11:33 --> REQ ----------->
INFO - 2025-06-11 02:11:33 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 02:11:36 --> REQ ----------->$2y$09$3QsSF3JLjaVBjIJdhymLxePSq
DEBUG - 2025-06-11 02:11:36 --> btdbFindBy ---> 
INFO - 2025-06-11 02:11:36 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 02:11:36 --> REQ ----------->$2y$09$3QsSF3JLjaVBjIJdhymLxePSq
INFO - 2025-06-11 02:11:36 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
WARNING - 2025-06-11 02:11:36 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('aiproStart', null, 1752199896, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(389): btflag_set('aiproStart', null, [...])
 3 APPPATH\Controllers\Api.php(792): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-11 02:11:36 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('user_email', null, 1752199896, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(390): btflag_set('user_email', null, [...])
 3 APPPATH\Controllers\Api.php(792): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-11 02:11:36 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('chatpro_KJLF4XgSL8wjlGm', null, 1752199896, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(391): btflag_set('chatpro_KJLF4XgSL8wjlGm', null, [...])
 3 APPPATH\Controllers\Api.php(792): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
INFO - 2025-06-11 02:11:36 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 02:11:36 --> btdbFindBy ---> 
DEBUG - 2025-06-11 02:11:36 --> btdbFindBy ---> SELECT `start-plan`.*, `start-account`.*, DATE_FORMAT(resumed_at , "%M %e, %Y") as "resumed_at_formatted", `start-user`.`mode`, case when DATE_ADD(end_date, INTERVAL 1 DAY) < now() then 'yes' else 'no' end as 'expired'
FROM `start-account`
JOIN `start-user` ON `start-user`.`user_id` = `start-account`.`user_id`
JOIN `start-plan` ON `start-plan`.`plan_id` = `start-account`.`plan_id`
WHERE `start-account`.`user_id` = '25'
ORDER BY `start-account`.`end_date` DESC
DEBUG - 2025-06-11 02:11:37 --> btdbFindBy ---> SELECT *
FROM `start-user_app`
WHERE `email` = '<EMAIL>'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-11 02:11:37 --> REQ ----------->$2y$09$3QsSF3JLjaVBjIJdhymLxePSq
INFO - 2025-06-11 02:11:37 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
WARNING - 2025-06-11 02:11:37 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('aiproStart', null, **********, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(389): btflag_set('aiproStart', null, [...])
 3 APPPATH\Controllers\Api.php(792): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-11 02:11:37 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('user_email', null, **********, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(390): btflag_set('user_email', null, [...])
 3 APPPATH\Controllers\Api.php(792): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-11 02:11:37 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('chatpro_KJLF4XgSL8wjlGm', null, **********, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(391): btflag_set('chatpro_KJLF4XgSL8wjlGm', null, [...])
 3 APPPATH\Controllers\Api.php(792): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-06-11 02:11:38 --> REQ ----------->
INFO - 2025-06-11 02:11:38 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 02:11:40 --> REQ ----------->$2y$09$3QsSF3JLjaVBjIJdhymLxePSq
DEBUG - 2025-06-11 02:11:40 --> btdbFindBy ---> 
INFO - 2025-06-11 02:11:40 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 02:11:40 --> REQ ----------->$2y$09$3QsSF3JLjaVBjIJdhymLxePSq
INFO - 2025-06-11 02:11:40 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
WARNING - 2025-06-11 02:11:40 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('aiproStart', null, 1752199900, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(389): btflag_set('aiproStart', null, [...])
 3 APPPATH\Controllers\Api.php(792): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-11 02:11:40 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('user_email', null, 1752199900, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(390): btflag_set('user_email', null, [...])
 3 APPPATH\Controllers\Api.php(792): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-11 02:11:40 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('chatpro_KJLF4XgSL8wjlGm', null, 1752199900, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(391): btflag_set('chatpro_KJLF4XgSL8wjlGm', null, [...])
 3 APPPATH\Controllers\Api.php(792): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-06-11 02:12:39 --> REQ ----------->$2y$09$3QsSF3JLjaVBjIJdhymLxePSq
INFO - 2025-06-11 02:12:39 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
WARNING - 2025-06-11 02:12:39 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('aiproStart', null, 1752199959, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(389): btflag_set('aiproStart', null, [...])
 3 APPPATH\Controllers\Api.php(792): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-11 02:12:39 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('user_email', null, 1752199959, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(390): btflag_set('user_email', null, [...])
 3 APPPATH\Controllers\Api.php(792): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-11 02:12:39 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('chatpro_KJLF4XgSL8wjlGm', null, 1752199959, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(391): btflag_set('chatpro_KJLF4XgSL8wjlGm', null, [...])
 3 APPPATH\Controllers\Api.php(792): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
INFO - 2025-06-11 02:12:39 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 02:12:39 --> btdbFindBy ---> 
DEBUG - 2025-06-11 02:12:39 --> btdbFindBy ---> SELECT `start-plan`.*, `start-account`.*, DATE_FORMAT(resumed_at , "%M %e, %Y") as "resumed_at_formatted", `start-user`.`mode`, case when DATE_ADD(end_date, INTERVAL 1 DAY) < now() then 'yes' else 'no' end as 'expired'
FROM `start-account`
JOIN `start-user` ON `start-user`.`user_id` = `start-account`.`user_id`
JOIN `start-plan` ON `start-plan`.`plan_id` = `start-account`.`plan_id`
WHERE `start-account`.`user_id` = '25'
ORDER BY `start-account`.`end_date` DESC
DEBUG - 2025-06-11 02:12:40 --> btdbFindBy ---> SELECT *
FROM `start-user_app`
WHERE `email` = '<EMAIL>'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-11 02:12:41 --> REQ ----------->$2y$09$3QsSF3JLjaVBjIJdhymLxePSq
INFO - 2025-06-11 02:12:41 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
WARNING - 2025-06-11 02:12:41 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('aiproStart', null, **********, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(389): btflag_set('aiproStart', null, [...])
 3 APPPATH\Controllers\Api.php(792): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-11 02:12:41 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('user_email', null, **********, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(390): btflag_set('user_email', null, [...])
 3 APPPATH\Controllers\Api.php(792): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-11 02:12:41 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('chatpro_KJLF4XgSL8wjlGm', null, **********, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(391): btflag_set('chatpro_KJLF4XgSL8wjlGm', null, [...])
 3 APPPATH\Controllers\Api.php(792): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-06-11 02:12:41 --> REQ ----------->
INFO - 2025-06-11 02:12:41 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 02:12:43 --> REQ ----------->$2y$09$3QsSF3JLjaVBjIJdhymLxePSq
DEBUG - 2025-06-11 02:12:43 --> btdbFindBy ---> 
INFO - 2025-06-11 02:12:43 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 02:12:43 --> REQ ----------->$2y$09$3QsSF3JLjaVBjIJdhymLxePSq
INFO - 2025-06-11 02:12:43 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
WARNING - 2025-06-11 02:12:43 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('aiproStart', null, 1752199963, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(389): btflag_set('aiproStart', null, [...])
 3 APPPATH\Controllers\Api.php(792): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-11 02:12:43 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('user_email', null, 1752199963, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(390): btflag_set('user_email', null, [...])
 3 APPPATH\Controllers\Api.php(792): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-11 02:12:43 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('chatpro_KJLF4XgSL8wjlGm', null, 1752199963, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(391): btflag_set('chatpro_KJLF4XgSL8wjlGm', null, [...])
 3 APPPATH\Controllers\Api.php(792): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-06-11 02:12:47 --> REQ ----------->$2y$09$3QsSF3JLjaVBjIJdhymLxePSq
INFO - 2025-06-11 02:12:47 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
WARNING - 2025-06-11 02:12:47 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('aiproStart', null, 1752199967, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(389): btflag_set('aiproStart', null, [...])
 3 APPPATH\Controllers\Api.php(792): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-11 02:12:47 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('user_email', null, 1752199967, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(390): btflag_set('user_email', null, [...])
 3 APPPATH\Controllers\Api.php(792): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-11 02:12:47 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('chatpro_KJLF4XgSL8wjlGm', null, 1752199967, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(391): btflag_set('chatpro_KJLF4XgSL8wjlGm', null, [...])
 3 APPPATH\Controllers\Api.php(792): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-06-11 02:12:47 --> REQ ----------->$2y$09$3QsSF3JLjaVBjIJdhymLxePSq
INFO - 2025-06-11 02:12:47 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2025-06-11 02:12:49 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 02:12:49 --> btdbFindBy ---> 
DEBUG - 2025-06-11 02:12:49 --> btdbFindBy ---> SELECT `start-plan`.*, `start-account`.*, DATE_FORMAT(resumed_at , "%M %e, %Y") as "resumed_at_formatted", `start-user`.`mode`, case when DATE_ADD(end_date, INTERVAL 1 DAY) < now() then 'yes' else 'no' end as 'expired'
FROM `start-account`
JOIN `start-user` ON `start-user`.`user_id` = `start-account`.`user_id`
JOIN `start-plan` ON `start-plan`.`plan_id` = `start-account`.`plan_id`
WHERE `start-account`.`user_id` = '25'
ORDER BY `start-account`.`end_date` DESC
DEBUG - 2025-06-11 02:12:50 --> btdbFindBy ---> SELECT *
FROM `start-user_app`
WHERE `email` = '<EMAIL>'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-11 02:12:50 --> REQ ----------->$2y$09$3QsSF3JLjaVBjIJdhymLxePSq
INFO - 2025-06-11 02:12:50 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
WARNING - 2025-06-11 02:12:50 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('aiproStart', null, **********, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(389): btflag_set('aiproStart', null, [...])
 3 APPPATH\Controllers\Api.php(792): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-11 02:12:50 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('user_email', null, **********, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(390): btflag_set('user_email', null, [...])
 3 APPPATH\Controllers\Api.php(792): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-11 02:12:50 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('chatpro_KJLF4XgSL8wjlGm', null, **********, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(391): btflag_set('chatpro_KJLF4XgSL8wjlGm', null, [...])
 3 APPPATH\Controllers\Api.php(792): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-06-11 02:12:50 --> REQ ----------->
INFO - 2025-06-11 02:12:50 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 02:12:53 --> REQ ----------->$2y$09$3QsSF3JLjaVBjIJdhymLxePSq
DEBUG - 2025-06-11 02:12:53 --> btdbFindBy ---> 
INFO - 2025-06-11 02:12:53 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 02:12:53 --> REQ ----------->$2y$09$3QsSF3JLjaVBjIJdhymLxePSq
INFO - 2025-06-11 02:12:53 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
WARNING - 2025-06-11 02:12:53 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('aiproStart', null, 1752199973, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(389): btflag_set('aiproStart', null, [...])
 3 APPPATH\Controllers\Api.php(792): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-11 02:12:53 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('user_email', null, 1752199973, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(390): btflag_set('user_email', null, [...])
 3 APPPATH\Controllers\Api.php(792): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-11 02:12:53 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('chatpro_KJLF4XgSL8wjlGm', null, 1752199973, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(391): btflag_set('chatpro_KJLF4XgSL8wjlGm', null, [...])
 3 APPPATH\Controllers\Api.php(792): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-06-11 02:12:54 --> REQ ----------->$2y$09$3QsSF3JLjaVBjIJdhymLxePSq
INFO - 2025-06-11 02:12:54 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
WARNING - 2025-06-11 02:12:54 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('aiproStart', null, 1752199974, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(389): btflag_set('aiproStart', null, [...])
 3 APPPATH\Controllers\Api.php(792): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-11 02:12:54 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('user_email', null, 1752199974, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(390): btflag_set('user_email', null, [...])
 3 APPPATH\Controllers\Api.php(792): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-11 02:12:54 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('chatpro_KJLF4XgSL8wjlGm', null, 1752199974, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(391): btflag_set('chatpro_KJLF4XgSL8wjlGm', null, [...])
 3 APPPATH\Controllers\Api.php(792): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
INFO - 2025-06-11 02:12:58 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2025-06-11 02:12:58 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 02:12:58 --> REQ ----------->591SkRKNUpEQTVKRE5SYzFOR00wcE1hbUZXUW1wSlNtUm9lVzFNZUdWUVUzRT06ZFc1a1pXWnBibVZr
INFO - 2025-06-11 02:13:00 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 02:13:00 --> btdbFindBy ---> 
DEBUG - 2025-06-11 02:13:00 --> btdbFindBy ---> SELECT `start-plan`.*, `start-account`.*, DATE_FORMAT(resumed_at , "%M %e, %Y") as "resumed_at_formatted", `start-user`.`mode`, case when DATE_ADD(end_date, INTERVAL 1 DAY) < now() then 'yes' else 'no' end as 'expired'
FROM `start-account`
JOIN `start-user` ON `start-user`.`user_id` = `start-account`.`user_id`
JOIN `start-plan` ON `start-plan`.`plan_id` = `start-account`.`plan_id`
WHERE `start-account`.`user_id` = '25'
ORDER BY `start-account`.`end_date` DESC
DEBUG - 2025-06-11 02:13:01 --> btdbFindBy ---> SELECT *
FROM `start-user_app`
WHERE `email` = '<EMAIL>'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-11 02:13:01 --> REQ ----------->$2y$09$3QsSF3JLjaVBjIJdhymLxePSq
INFO - 2025-06-11 02:13:01 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
WARNING - 2025-06-11 02:13:01 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('aiproStart', null, **********, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(389): btflag_set('aiproStart', null, [...])
 3 APPPATH\Controllers\Api.php(792): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-11 02:13:01 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('user_email', null, **********, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(390): btflag_set('user_email', null, [...])
 3 APPPATH\Controllers\Api.php(792): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-11 02:13:01 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('chatpro_KJLF4XgSL8wjlGm', null, **********, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(391): btflag_set('chatpro_KJLF4XgSL8wjlGm', null, [...])
 3 APPPATH\Controllers\Api.php(792): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-06-11 02:13:01 --> REQ ----------->
INFO - 2025-06-11 02:13:01 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 02:13:04 --> REQ ----------->$2y$09$3QsSF3JLjaVBjIJdhymLxePSq
DEBUG - 2025-06-11 02:13:04 --> btdbFindBy ---> 
INFO - 2025-06-11 02:13:04 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 02:13:04 --> REQ ----------->$2y$09$3QsSF3JLjaVBjIJdhymLxePSq
INFO - 2025-06-11 02:13:04 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
WARNING - 2025-06-11 02:13:04 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('aiproStart', null, 1752199984, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(389): btflag_set('aiproStart', null, [...])
 3 APPPATH\Controllers\Api.php(792): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-11 02:13:04 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('user_email', null, 1752199984, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(390): btflag_set('user_email', null, [...])
 3 APPPATH\Controllers\Api.php(792): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-11 02:13:04 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('chatpro_KJLF4XgSL8wjlGm', null, 1752199984, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(391): btflag_set('chatpro_KJLF4XgSL8wjlGm', null, [...])
 3 APPPATH\Controllers\Api.php(792): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-06-11 02:13:05 --> REQ ----------->$2y$09$3QsSF3JLjaVBjIJdhymLxePSq
INFO - 2025-06-11 02:13:05 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
WARNING - 2025-06-11 02:13:05 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('aiproStart', null, 1752199985, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(389): btflag_set('aiproStart', null, [...])
 3 APPPATH\Controllers\Api.php(792): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-11 02:13:05 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('user_email', null, 1752199985, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(390): btflag_set('user_email', null, [...])
 3 APPPATH\Controllers\Api.php(792): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-11 02:13:05 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('chatpro_KJLF4XgSL8wjlGm', null, 1752199985, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(391): btflag_set('chatpro_KJLF4XgSL8wjlGm', null, [...])
 3 APPPATH\Controllers\Api.php(792): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
INFO - 2025-06-11 02:13:07 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 02:13:07 --> btdbFindBy ---> 
DEBUG - 2025-06-11 02:13:07 --> btdbFindBy ---> SELECT `start-plan`.*, `start-account`.*, DATE_FORMAT(resumed_at , "%M %e, %Y") as "resumed_at_formatted", `start-user`.`mode`, case when DATE_ADD(end_date, INTERVAL 1 DAY) < now() then 'yes' else 'no' end as 'expired'
FROM `start-account`
JOIN `start-user` ON `start-user`.`user_id` = `start-account`.`user_id`
JOIN `start-plan` ON `start-plan`.`plan_id` = `start-account`.`plan_id`
WHERE `start-account`.`user_id` = '25'
ORDER BY `start-account`.`end_date` DESC
DEBUG - 2025-06-11 02:13:08 --> btdbFindBy ---> SELECT *
FROM `start-user_app`
WHERE `email` = '<EMAIL>'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-11 02:13:08 --> REQ ----------->$2y$09$3QsSF3JLjaVBjIJdhymLxePSq
INFO - 2025-06-11 02:13:08 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
WARNING - 2025-06-11 02:13:08 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('aiproStart', null, **********, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(389): btflag_set('aiproStart', null, [...])
 3 APPPATH\Controllers\Api.php(792): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-11 02:13:08 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('user_email', null, **********, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(390): btflag_set('user_email', null, [...])
 3 APPPATH\Controllers\Api.php(792): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-11 02:13:08 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('chatpro_KJLF4XgSL8wjlGm', null, **********, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(391): btflag_set('chatpro_KJLF4XgSL8wjlGm', null, [...])
 3 APPPATH\Controllers\Api.php(792): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-06-11 02:13:08 --> REQ ----------->$2y$09$3QsSF3JLjaVBjIJdhymLxePSq
DEBUG - 2025-06-11 02:13:08 --> btdbFindBy ---> 
INFO - 2025-06-11 02:13:08 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 02:13:14 --> REQ ----------->{"Question1":{"Answer":"Too expensive"},"Question2":{"Answer":"Chat prompts"}}0
INFO - 2025-06-11 02:13:14 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 02:13:14 --> btdbUpdate ---> UPDATE `start-user` SET `survey_data` = '{\"Question1\":{\"Answer\":\"Too expensive\"},\"Question2\":{\"Answer\":\"Chat prompts\"}}', `updated_at` = '2025-06-11 02:13:14'
WHERE `start-user`.`user_id` IN ('25')
DEBUG - 2025-06-11 02:13:14 --> REQ ----------->
INFO - 2025-06-11 02:13:14 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 02:13:14 --> btdbFindBy ---> 
WARNING - 2025-06-11 02:13:14 --> [DEPRECATED] Optional parameter $_api_key declared before required parameter $_mode is implicitly treated as a required parameter in VENDORPATH\btapp\btcore\src\Payment\Stripe.php on line 15.
 1 SYSTEMPATH\Autoloader\Autoloader.php(314): include_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\vendor\\codeigniter4\\framework\\system\\Debug\\Exceptions.php')
 2 SYSTEMPATH\Autoloader\Autoloader.php(250): CodeIgniter\Autoloader\Autoloader->includeFile('C:\\laragon\\www\\app.ai-pro.org\\btapp\\vendor\\composer/../btapp/btcore/src/Payment/Stripe.php')
 3 APPPATH\Controllers\Api.php(4178): CodeIgniter\Autoloader\Autoloader->loadClassmap('BTCore\\Payment\\Stripe')
 4 APPPATH\Controllers\Api.php(195): App\Controllers\Api->cancelSubscription()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('cancel-subscription')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-06-11 02:13:15 --> btdbFindBy ---> SELECT *
FROM `start-account`
WHERE `account_pid` = '9d3334c85f0e9553'
AND `user_id` = '25'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-11 02:13:15 --> btdbUpdate ---> UPDATE `start-account` SET `status` = 'inactive', `cancelled_at` = '2025-06-11 02:13:14', `updated_at` = '2025-06-11 02:13:15'
WHERE `start-account`.`account_id` IN ('14')
DEBUG - 2025-06-11 02:13:15 --> btdbFindBy ---> UPDATE `start-account` SET `status` = 'inactive', `cancelled_at` = '2025-06-11 02:13:14', `updated_at` = '2025-06-11 02:13:15'
WHERE `start-account`.`account_id` IN ('14')
DEBUG - 2025-06-11 02:13:15 --> btdbUpdate ---> UPDATE `start-user` SET `status` = 'inactive', `updated_at` = '2025-06-11 02:13:15'
WHERE `start-user`.`user_id` IN ('25')
DEBUG - 2025-06-11 02:13:15 --> btdbFindBy ---> SELECT `start-plan`.*, `start-account`.*, DATE_FORMAT(resumed_at , "%M %e, %Y") as "resumed_at_formatted", `start-user`.`mode`, case when DATE_ADD(end_date, INTERVAL 1 DAY) < now() then 'yes' else 'no' end as 'expired'
FROM `start-account`
JOIN `start-user` ON `start-user`.`user_id` = `start-account`.`user_id`
JOIN `start-plan` ON `start-plan`.`plan_id` = `start-account`.`plan_id`
WHERE `start-account`.`user_id` = '25'
ORDER BY `start-account`.`end_date` DESC
DEBUG - 2025-06-11 02:13:16 --> btdbFindBy ---> SELECT *
FROM `start-user_app`
WHERE `email` = '<EMAIL>'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
INFO - 2025-06-11 02:13:19 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 02:13:19 --> btdbFindBy ---> 
DEBUG - 2025-06-11 02:13:19 --> btdbFindBy ---> SELECT `start-plan`.*, `start-account`.*, DATE_FORMAT(resumed_at , "%M %e, %Y") as "resumed_at_formatted", `start-user`.`mode`, case when DATE_ADD(end_date, INTERVAL 1 DAY) < now() then 'yes' else 'no' end as 'expired'
FROM `start-account`
JOIN `start-user` ON `start-user`.`user_id` = `start-account`.`user_id`
JOIN `start-plan` ON `start-plan`.`plan_id` = `start-account`.`plan_id`
WHERE `start-account`.`user_id` = '25'
ORDER BY `start-account`.`end_date` DESC
DEBUG - 2025-06-11 02:13:20 --> btdbFindBy ---> SELECT *
FROM `start-user_app`
WHERE `email` = '<EMAIL>'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-11 02:13:20 --> btdbFindBy ---> SELECT *
FROM `start-plan`
WHERE `plan_id` = '16'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-11 02:13:21 --> REQ ----------->$2y$09$3QsSF3JLjaVBjIJdhymLxePSq
DEBUG - 2025-06-11 02:13:21 --> btdbFindBy ---> 
INFO - 2025-06-11 02:13:21 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 02:13:21 --> REQ ----------->$2y$09$3QsSF3JLjaVBjIJdhymLxePSq
INFO - 2025-06-11 02:13:21 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
WARNING - 2025-06-11 02:13:21 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('aiproStart', null, 1752200001, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(389): btflag_set('aiproStart', null, [...])
 3 APPPATH\Controllers\Api.php(792): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-11 02:13:21 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('user_email', null, 1752200001, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(390): btflag_set('user_email', null, [...])
 3 APPPATH\Controllers\Api.php(792): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-11 02:13:21 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('chatpro_KJLF4XgSL8wjlGm', null, 1752200001, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(391): btflag_set('chatpro_KJLF4XgSL8wjlGm', null, [...])
 3 APPPATH\Controllers\Api.php(792): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-06-11 02:13:21 --> REQ ----------->
DEBUG - 2025-06-11 02:13:21 --> REQ ----------->
INFO - 2025-06-11 02:13:21 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2025-06-11 02:13:24 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 02:13:24 --> btdbFindBy ---> 
DEBUG - 2025-06-11 02:13:24 --> btdbFindBy ---> SELECT `start-plan`.*, `start-account`.*, DATE_FORMAT(resumed_at , "%M %e, %Y") as "resumed_at_formatted", `start-user`.`mode`, case when DATE_ADD(end_date, INTERVAL 1 DAY) < now() then 'yes' else 'no' end as 'expired'
FROM `start-account`
JOIN `start-user` ON `start-user`.`user_id` = `start-account`.`user_id`
JOIN `start-plan` ON `start-plan`.`plan_id` = `start-account`.`plan_id`
WHERE `start-account`.`user_id` = '25'
ORDER BY `start-account`.`end_date` DESC
DEBUG - 2025-06-11 02:13:24 --> btdbFindBy ---> SELECT *
FROM `start-user_app`
WHERE `email` = '<EMAIL>'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-11 02:13:25 --> REQ ----------->$2y$09$3QsSF3JLjaVBjIJdhymLxePSq
INFO - 2025-06-11 02:13:25 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
WARNING - 2025-06-11 02:13:25 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('aiproStart', null, **********, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(389): btflag_set('aiproStart', null, [...])
 3 APPPATH\Controllers\Api.php(792): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-11 02:13:25 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('user_email', null, **********, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(390): btflag_set('user_email', null, [...])
 3 APPPATH\Controllers\Api.php(792): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-11 02:13:25 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('chatpro_KJLF4XgSL8wjlGm', null, **********, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(391): btflag_set('chatpro_KJLF4XgSL8wjlGm', null, [...])
 3 APPPATH\Controllers\Api.php(792): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-06-11 02:13:25 --> REQ ----------->
INFO - 2025-06-11 02:13:25 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 02:13:27 --> REQ ----------->$2y$09$3QsSF3JLjaVBjIJdhymLxePSq
DEBUG - 2025-06-11 02:13:27 --> btdbFindBy ---> 
INFO - 2025-06-11 02:13:27 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 02:13:27 --> REQ ----------->$2y$09$3QsSF3JLjaVBjIJdhymLxePSq
INFO - 2025-06-11 02:13:28 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
WARNING - 2025-06-11 02:13:28 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('aiproStart', null, 1752200008, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(389): btflag_set('aiproStart', null, [...])
 3 APPPATH\Controllers\Api.php(792): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-11 02:13:28 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('user_email', null, 1752200008, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(390): btflag_set('user_email', null, [...])
 3 APPPATH\Controllers\Api.php(792): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-11 02:13:28 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('chatpro_KJLF4XgSL8wjlGm', null, 1752200008, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(391): btflag_set('chatpro_KJLF4XgSL8wjlGm', null, [...])
 3 APPPATH\Controllers\Api.php(792): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-06-11 02:13:30 --> REQ ----------->$2y$09$3QsSF3JLjaVBjIJdhymLxePSq
DEBUG - 2025-06-11 02:13:30 --> btdbFindBy ---> 
INFO - 2025-06-11 02:13:30 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 02:13:31 --> REQ ----------->$2y$09$3QsSF3JLjaVBjIJdhymLxePSq
INFO - 2025-06-11 02:13:31 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 02:13:42 --> REQ ----------->$2y$09$3QsSF3JLjaVBjIJdhymLxePSq
INFO - 2025-06-11 02:13:42 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 02:13:49 --> REQ ----------->$2y$09$3QsSF3JLjaVBjIJdhymLxePSq
INFO - 2025-06-11 02:13:49 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 02:13:53 --> REQ ----------->$2y$09$3QsSF3JLjaVBjIJdhymLxePSq
INFO - 2025-06-11 02:13:53 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
WARNING - 2025-06-11 02:13:53 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('aiproStart', null, 1752200033, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(389): btflag_set('aiproStart', null, [...])
 3 APPPATH\Controllers\Api.php(792): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-11 02:13:53 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('user_email', null, 1752200033, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(390): btflag_set('user_email', null, [...])
 3 APPPATH\Controllers\Api.php(792): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-11 02:13:53 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('chatpro_KJLF4XgSL8wjlGm', null, 1752200033, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(391): btflag_set('chatpro_KJLF4XgSL8wjlGm', null, [...])
 3 APPPATH\Controllers\Api.php(792): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-06-11 02:39:47 --> REQ ----------->$2y$09$3QsSF3JLjaVBjIJdhymLxePSq
INFO - 2025-06-11 02:39:47 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
WARNING - 2025-06-11 02:39:47 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('aiproStart', null, 1752201587, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(401): btflag_set('aiproStart', null, [...])
 3 APPPATH\Controllers\Api.php(768): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-11 02:39:47 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('user_email', null, 1752201587, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(402): btflag_set('user_email', null, [...])
 3 APPPATH\Controllers\Api.php(768): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-11 02:39:47 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('chatpro_KJLF4XgSL8wjlGm', null, 1752201587, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(403): btflag_set('chatpro_KJLF4XgSL8wjlGm', null, [...])
 3 APPPATH\Controllers\Api.php(768): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-06-11 02:49:26 --> REQ ----------->$2y$09$3QsSF3JLjaVBjIJdhymLxePSq
INFO - 2025-06-11 02:49:26 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
WARNING - 2025-06-11 02:49:26 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('aiproStart', null, 1752202166, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(401): btflag_set('aiproStart', null, [...])
 3 APPPATH\Controllers\Api.php(768): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-11 02:49:26 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('user_email', null, 1752202166, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(402): btflag_set('user_email', null, [...])
 3 APPPATH\Controllers\Api.php(768): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-11 02:49:26 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('chatpro_KJLF4XgSL8wjlGm', null, 1752202166, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(403): btflag_set('chatpro_KJLF4XgSL8wjlGm', null, [...])
 3 APPPATH\Controllers\Api.php(768): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-06-11 02:49:52 --> REQ ----------->$2y$09$3QsSF3JLjaVBjIJdhymLxePSq
INFO - 2025-06-11 02:49:52 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
WARNING - 2025-06-11 02:49:52 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('aiproStart', null, 1752202192, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(401): btflag_set('aiproStart', null, [...])
 3 APPPATH\Controllers\Api.php(768): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-11 02:49:52 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('user_email', null, 1752202192, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(402): btflag_set('user_email', null, [...])
 3 APPPATH\Controllers\Api.php(768): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-11 02:49:52 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('chatpro_KJLF4XgSL8wjlGm', null, 1752202192, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(403): btflag_set('chatpro_KJLF4XgSL8wjlGm', null, [...])
 3 APPPATH\Controllers\Api.php(768): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-06-11 02:53:13 --> REQ ----------->
INFO - 2025-06-11 02:53:13 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 02:53:13 --> REQ ----------->
INFO - 2025-06-11 02:53:13 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 02:53:29 --> REQ ----------->
INFO - 2025-06-11 02:53:29 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 02:53:29 --> REQ ----------->
INFO - 2025-06-11 02:53:29 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 02:53:41 --> REQ ----------->
INFO - 2025-06-11 02:53:41 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 02:55:23 --> REQ ----------->
INFO - 2025-06-11 02:55:23 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 02:55:31 --> REQ ----------->
INFO - 2025-06-11 02:55:31 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 02:55:31 --> REQ ----------->
INFO - 2025-06-11 02:55:31 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 02:55:31 --> REQ ----------->
INFO - 2025-06-11 02:55:31 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 02:55:32 --> REQ ----------->
INFO - 2025-06-11 02:55:32 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 02:55:32 --> REQ ----------->
INFO - 2025-06-11 02:55:32 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2025-06-11 02:55:35 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 02:55:35 --> REQ ----------->
INFO - 2025-06-11 02:55:35 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 02:55:42 --> REQ ----------->
INFO - 2025-06-11 02:55:42 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 02:56:01 --> REQ ----------->
INFO - 2025-06-11 02:56:01 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 02:57:17 --> REQ ----------->testuser061125_00@mailinator.com123123123123
DEBUG - 2025-06-11 02:57:17 --> testuser061125_00@mailinator.com123123123123
DEBUG - 2025-06-11 02:57:18 --> btdbFindBy ---> 
DEBUG - 2025-06-11 02:57:18 --> btdbUpdate ---> INSERT INTO `start-user` (`user_pid`, `email`, `mode`, `password`, `first_name`, `last_name`, `login_token`, `ip_address`, `country`, `flags`, `created_at`, `updated_at`) VALUES ('f7071d1411b865fd', '<EMAIL>', 'test', '$P$BSYEUSAHwkwVYvCKMivGw.pLndEzpT/', '', '', '$2y$09$v32tDHuBQgmAB3J5DhJdkudHMqioMpG/Zsuk18yp77DRaE41EZJqW', '::1', '', '{\"aiwp_logged_in\":null,\"mode\":\"test\",\"ppg\":\"14\",\"pp_ctaclr\":\"1559ED\",\"pmt\":\"rec\",\"vpay\":\"\",\"navmenu\":\"show\",\"splash\":\"off\",\"ailplogo\":\"off\",\"emailid\":\"\",\"theme_flag_name\":\"kt8typtb\",\"theme_flag_value\":\"arcana\"}', '2025-06-11 02:57:18', '2025-06-11 02:57:18')
DEBUG - 2025-06-11 02:57:18 --> btdbFindBy ---> INSERT INTO `start-user` (`user_pid`, `email`, `mode`, `password`, `first_name`, `last_name`, `login_token`, `ip_address`, `country`, `flags`, `created_at`, `updated_at`) VALUES ('f7071d1411b865fd', '<EMAIL>', 'test', '$P$BSYEUSAHwkwVYvCKMivGw.pLndEzpT/', '', '', '$2y$09$v32tDHuBQgmAB3J5DhJdkudHMqioMpG/Zsuk18yp77DRaE41EZJqW', '::1', '', '{\"aiwp_logged_in\":null,\"mode\":\"test\",\"ppg\":\"14\",\"pp_ctaclr\":\"1559ED\",\"pmt\":\"rec\",\"vpay\":\"\",\"navmenu\":\"show\",\"splash\":\"off\",\"ailplogo\":\"off\",\"emailid\":\"\",\"theme_flag_name\":\"kt8typtb\",\"theme_flag_value\":\"arcana\"}', '2025-06-11 02:57:18', '2025-06-11 02:57:18')
INFO - 2025-06-11 02:57:18 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 02:57:18 --> btdbFindBy ---> SELECT `start-plan`.*, `start-account`.*, DATE_FORMAT(resumed_at , "%M %e, %Y") as "resumed_at_formatted", `start-user`.`mode`, case when DATE_ADD(end_date, INTERVAL 1 DAY) < now() then 'yes' else 'no' end as 'expired'
FROM `start-account`
JOIN `start-user` ON `start-user`.`user_id` = `start-account`.`user_id`
JOIN `start-plan` ON `start-plan`.`plan_id` = `start-account`.`plan_id`
WHERE `start-account`.`user_id` = '1'
ORDER BY `start-account`.`end_date` DESC
DEBUG - 2025-06-11 02:57:18 --> btdbUpdate ---> INSERT INTO `start-user_app` (`email`, `userapp_pid`, `created_at`, `updated_at`) VALUES ('<EMAIL>', '70a623710d1a2e4ede98ba54689f028a', '2025-06-11 02:57:18', '2025-06-11 02:57:18')
DEBUG - 2025-06-11 02:57:18 --> btdbFindBy ---> INSERT INTO `start-user_app` (`email`, `userapp_pid`, `created_at`, `updated_at`) VALUES ('<EMAIL>', '70a623710d1a2e4ede98ba54689f028a', '2025-06-11 02:57:18', '2025-06-11 02:57:18')
INFO - 2025-06-11 02:57:19 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 02:57:19 --> btdbFindBy ---> 
DEBUG - 2025-06-11 02:57:19 --> btdbFindBy ---> SELECT `start-plan`.*, `start-account`.*, DATE_FORMAT(resumed_at , "%M %e, %Y") as "resumed_at_formatted", `start-user`.`mode`, case when DATE_ADD(end_date, INTERVAL 1 DAY) < now() then 'yes' else 'no' end as 'expired'
FROM `start-account`
JOIN `start-user` ON `start-user`.`user_id` = `start-account`.`user_id`
JOIN `start-plan` ON `start-plan`.`plan_id` = `start-account`.`plan_id`
WHERE `start-account`.`user_id` = '1'
ORDER BY `start-account`.`end_date` DESC
WARNING - 2025-06-11 02:57:19 --> [DEPRECATED] explode(): Passing null to parameter #2 ($string) of type string is deprecated in APPPATH\Models\BTModel.php on line 52.
 1 APPPATH\Models\BTModel.php(52): explode(',', null)
 2 APPPATH\Helpers\btdb_helper.php(180): App\Models\BTModel->findBy([...], null)
 3 APPPATH\Controllers\FlowPages\Payment.php(64): btdbFindBy('PlanModel', 'plan_id', null)
 4 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\FlowPages\Payment->index()
 5 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\FlowPages\Payment))
 6 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 7 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 8 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
INFO - 2025-06-11 02:57:19 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 02:57:20 --> REQ ----------->14
INFO - 2025-06-11 02:57:25 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 02:57:25 --> btdbFindBy ---> 
DEBUG - 2025-06-11 02:57:26 --> btdbFindBy ---> SELECT `start-plan`.*, `start-account`.*, DATE_FORMAT(resumed_at , "%M %e, %Y") as "resumed_at_formatted", `start-user`.`mode`, case when DATE_ADD(end_date, INTERVAL 1 DAY) < now() then 'yes' else 'no' end as 'expired'
FROM `start-account`
JOIN `start-user` ON `start-user`.`user_id` = `start-account`.`user_id`
JOIN `start-plan` ON `start-plan`.`plan_id` = `start-account`.`plan_id`
WHERE `start-account`.`user_id` = '1'
ORDER BY `start-account`.`end_date` DESC
DEBUG - 2025-06-11 02:57:26 --> REQ ----------->$2y$09$v32tDHuBQgmAB3J5DhJdkudHM
INFO - 2025-06-11 02:57:26 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 02:57:27 --> REQ ----------->
INFO - 2025-06-11 02:57:27 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 02:57:27 --> REQ ----------->
DEBUG - 2025-06-11 02:57:28 --> REQ ----------->$2y$09$v32tDHuBQgmAB3J5DhJdkudHM
DEBUG - 2025-06-11 02:57:28 --> btdbFindBy ---> 
INFO - 2025-06-11 02:57:28 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 02:57:28 --> REQ ----------->$2y$09$v32tDHuBQgmAB3J5DhJdkudHM
INFO - 2025-06-11 02:57:28 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 02:57:30 --> REQ ----------->$2y$09$v32tDHuBQgmAB3J5DhJdkudHM
INFO - 2025-06-11 02:57:30 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 02:57:30 --> REQ ----------->
INFO - 2025-06-11 02:57:30 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2025-06-11 02:57:30 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 02:57:30 --> btdbFindBy ---> 
DEBUG - 2025-06-11 02:57:31 --> REQ ----------->$2y$09$v32tDHuBQgmAB3J5DhJdkudHM
INFO - 2025-06-11 02:57:31 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 02:57:31 --> REQ ----------->
INFO - 2025-06-11 02:57:31 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 02:57:31 --> REQ ----------->$2y$09$v32tDHuBQgmAB3J5DhJdkudHM
DEBUG - 2025-06-11 02:57:31 --> btdbFindBy ---> 
INFO - 2025-06-11 02:57:31 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 02:57:31 --> REQ ----------->$2y$09$v32tDHuBQgmAB3J5DhJdkudHM
DEBUG - 2025-06-11 02:57:31 --> btdbFindBy ---> 
INFO - 2025-06-11 02:57:31 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 02:57:32 --> REQ ----------->$2y$09$v32tDHuBQgmAB3J5DhJdkudHM
INFO - 2025-06-11 02:57:32 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 02:57:45 --> REQ ----------->$2y$09$v32tDHuBQgmAB3J5DhJdkudHM
INFO - 2025-06-11 02:57:45 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 02:57:45 --> REQ ----------->
INFO - 2025-06-11 02:57:45 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 02:58:19 --> REQ ----------->
INFO - 2025-06-11 02:58:19 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 02:58:19 --> REQ ----------->
INFO - 2025-06-11 02:58:19 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 02:58:24 --> REQ ----------->
INFO - 2025-06-11 02:58:24 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 02:58:24 --> REQ ----------->
INFO - 2025-06-11 02:58:24 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 02:58:24 --> REQ ----------->
INFO - 2025-06-11 02:58:24 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2025-06-11 02:58:25 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 02:58:26 --> REQ ----------->97
DEBUG - 2025-06-11 02:58:26 --> REQ ----------->
INFO - 2025-06-11 02:58:26 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 02:58:27 --> REQ ----------->
INFO - 2025-06-11 02:58:27 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 02:58:31 --> REQ ----------->
INFO - 2025-06-11 02:58:31 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 02:58:31 --> REQ ----------->
INFO - 2025-06-11 02:58:31 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 02:58:31 --> REQ ----------->
INFO - 2025-06-11 02:58:31 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 02:58:31 --> REQ ----------->
INFO - 2025-06-11 02:58:31 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2025-06-11 02:58:31 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 02:58:32 --> REQ ----------->59
DEBUG - 2025-06-11 02:58:32 --> REQ ----------->
INFO - 2025-06-11 02:58:32 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 02:58:33 --> REQ ----------->
INFO - 2025-06-11 02:58:33 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 02:58:36 --> REQ ----------->
INFO - 2025-06-11 02:58:36 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 02:58:36 --> REQ ----------->
INFO - 2025-06-11 02:58:36 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 02:58:36 --> REQ ----------->
INFO - 2025-06-11 02:58:36 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 02:58:37 --> REQ ----------->
INFO - 2025-06-11 02:58:37 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2025-06-11 02:58:37 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 02:58:38 --> REQ ----------->97
DEBUG - 2025-06-11 02:58:38 --> REQ ----------->
INFO - 2025-06-11 02:58:38 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 02:58:40 --> REQ ----------->
INFO - 2025-06-11 02:58:40 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2025-06-11 02:58:42 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 02:58:43 --> REQ ----------->
INFO - 2025-06-11 02:58:43 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 02:58:48 --> REQ ----------->
INFO - 2025-06-11 02:58:48 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 02:58:53 --> REQ ----------->testuser061125_01@mailinator.com123123123123
DEBUG - 2025-06-11 02:58:53 --> testuser061125_01@mailinator.com123123123123
DEBUG - 2025-06-11 02:58:53 --> btdbFindBy ---> 
DEBUG - 2025-06-11 02:58:53 --> btdbUpdate ---> INSERT INTO `start-user` (`user_pid`, `email`, `mode`, `password`, `first_name`, `last_name`, `login_token`, `ip_address`, `country`, `flags`, `created_at`, `updated_at`) VALUES ('221d18fba7e0e803', '<EMAIL>', 'test', '$P$BDzRWSm.HoCqLqJ6U/OYfZfzS5l.zg0', '', '', '$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqDXO1.sgikSngo9/02ebPV9VXJH.fy', '::1', '', '{\"aiwp_logged_in\":null,\"mode\":\"test\",\"ppg\":\"97\",\"pp_ctaclr\":\"1559ED\",\"pmt\":\"rec\",\"vpay\":\"\",\"navmenu\":\"show\",\"splash\":\"off\",\"ailplogo\":\"off\",\"emailid\":\"\",\"theme_flag_name\":\"kt8typtb\",\"theme_flag_value\":\"arcana\"}', '2025-06-11 02:58:53', '2025-06-11 02:58:53')
DEBUG - 2025-06-11 02:58:53 --> btdbFindBy ---> INSERT INTO `start-user` (`user_pid`, `email`, `mode`, `password`, `first_name`, `last_name`, `login_token`, `ip_address`, `country`, `flags`, `created_at`, `updated_at`) VALUES ('221d18fba7e0e803', '<EMAIL>', 'test', '$P$BDzRWSm.HoCqLqJ6U/OYfZfzS5l.zg0', '', '', '$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqDXO1.sgikSngo9/02ebPV9VXJH.fy', '::1', '', '{\"aiwp_logged_in\":null,\"mode\":\"test\",\"ppg\":\"97\",\"pp_ctaclr\":\"1559ED\",\"pmt\":\"rec\",\"vpay\":\"\",\"navmenu\":\"show\",\"splash\":\"off\",\"ailplogo\":\"off\",\"emailid\":\"\",\"theme_flag_name\":\"kt8typtb\",\"theme_flag_value\":\"arcana\"}', '2025-06-11 02:58:53', '2025-06-11 02:58:53')
INFO - 2025-06-11 02:58:53 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 02:58:53 --> btdbFindBy ---> SELECT `start-plan`.*, `start-account`.*, DATE_FORMAT(resumed_at , "%M %e, %Y") as "resumed_at_formatted", `start-user`.`mode`, case when DATE_ADD(end_date, INTERVAL 1 DAY) < now() then 'yes' else 'no' end as 'expired'
FROM `start-account`
JOIN `start-user` ON `start-user`.`user_id` = `start-account`.`user_id`
JOIN `start-plan` ON `start-plan`.`plan_id` = `start-account`.`plan_id`
WHERE `start-account`.`user_id` = '2'
ORDER BY `start-account`.`end_date` DESC
DEBUG - 2025-06-11 02:58:53 --> btdbUpdate ---> INSERT INTO `start-user_app` (`email`, `userapp_pid`, `created_at`, `updated_at`) VALUES ('<EMAIL>', '2c1627e1ebb4b35cc5b5de7aa1efe963', '2025-06-11 02:58:53', '2025-06-11 02:58:53')
DEBUG - 2025-06-11 02:58:53 --> btdbFindBy ---> INSERT INTO `start-user_app` (`email`, `userapp_pid`, `created_at`, `updated_at`) VALUES ('<EMAIL>', '2c1627e1ebb4b35cc5b5de7aa1efe963', '2025-06-11 02:58:53', '2025-06-11 02:58:53')
INFO - 2025-06-11 02:58:53 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 02:58:53 --> btdbFindBy ---> 
DEBUG - 2025-06-11 02:58:53 --> btdbFindBy ---> SELECT `start-plan`.*, `start-account`.*, DATE_FORMAT(resumed_at , "%M %e, %Y") as "resumed_at_formatted", `start-user`.`mode`, case when DATE_ADD(end_date, INTERVAL 1 DAY) < now() then 'yes' else 'no' end as 'expired'
FROM `start-account`
JOIN `start-user` ON `start-user`.`user_id` = `start-account`.`user_id`
JOIN `start-plan` ON `start-plan`.`plan_id` = `start-account`.`plan_id`
WHERE `start-account`.`user_id` = '2'
ORDER BY `start-account`.`end_date` DESC
WARNING - 2025-06-11 02:58:53 --> [DEPRECATED] explode(): Passing null to parameter #2 ($string) of type string is deprecated in APPPATH\Models\BTModel.php on line 52.
 1 APPPATH\Models\BTModel.php(52): explode(',', null)
 2 APPPATH\Helpers\btdb_helper.php(180): App\Models\BTModel->findBy([...], null)
 3 APPPATH\Controllers\FlowPages\Payment.php(64): btdbFindBy('PlanModel', 'plan_id', null)
 4 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\FlowPages\Payment->index()
 5 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\FlowPages\Payment))
 6 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 7 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 8 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
INFO - 2025-06-11 02:58:53 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 02:58:53 --> REQ ----------->97
INFO - 2025-06-11 02:58:58 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 02:58:58 --> btdbFindBy ---> 
DEBUG - 2025-06-11 02:58:58 --> btdbFindBy ---> SELECT `start-plan`.*, `start-account`.*, DATE_FORMAT(resumed_at , "%M %e, %Y") as "resumed_at_formatted", `start-user`.`mode`, case when DATE_ADD(end_date, INTERVAL 1 DAY) < now() then 'yes' else 'no' end as 'expired'
FROM `start-account`
JOIN `start-user` ON `start-user`.`user_id` = `start-account`.`user_id`
JOIN `start-plan` ON `start-plan`.`plan_id` = `start-account`.`plan_id`
WHERE `start-account`.`user_id` = '2'
ORDER BY `start-account`.`end_date` DESC
INFO - 2025-06-11 02:59:15 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 02:59:15 --> btdbFindBy ---> 
DEBUG - 2025-06-11 02:59:15 --> btdbFindBy ---> SELECT *
FROM `start-user`
WHERE `user_pid` = '221d18fba7e0e803'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
WARNING - 2025-06-11 02:59:15 --> [DEPRECATED] Optional parameter $_api_key declared before required parameter $_mode is implicitly treated as a required parameter in VENDORPATH\btapp\btcore\src\Payment\Stripe.php on line 15.
 1 SYSTEMPATH\Autoloader\Autoloader.php(314): include_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\vendor\\codeigniter4\\framework\\system\\Debug\\Exceptions.php')
 2 SYSTEMPATH\Autoloader\Autoloader.php(250): CodeIgniter\Autoloader\Autoloader->includeFile('C:\\laragon\\www\\app.ai-pro.org\\btapp\\vendor\\composer/../btapp/btcore/src/Payment/Stripe.php')
 3 APPPATH\Controllers\Api.php(2147): CodeIgniter\Autoloader\Autoloader->loadClassmap('BTCore\\Payment\\Stripe')
 4 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->executeStripeDirectLink('da425fb04f41c96f9d254366dedd4f32')
 5 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 6 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 7 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 8 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-06-11 02:59:21 --> btdbUpdate ---> INSERT INTO `start-account` (`account_pid`, `user_id`, `plan_id`, `merchant`, `merchant_customer_id`, `merchant_subscription_id`, `trial_end`, `start_date`, `end_date`, `cancelled_at`, `max_tokens`, `created_at`, `updated_at`) VALUES ('74ba0fe3238bba82', '2', '143', 'Stripe', 'cus_STc6rinh5moq1K', 'sub_1RYesdLtUaKDxQEZ7OUw2ckc', 0, '2025-06-11 02:59:11', '2025-07-11 02:59:11', 0, '500000', '2025-06-11 02:59:21', '2025-06-11 02:59:21')
DEBUG - 2025-06-11 02:59:21 --> btdbUpdate ---> INSERT INTO `start-payment` (`acctpmt_pid`, `account_id`, `user_id`, `amount`, `currency`, `charge_id`, `merchant`, `mode`, `created_at`, `updated_at`) VALUES ('2f9abb9fe9cf9a31', 1, '2', '10', 'USD', 'ch_3RYesdLtUaKDxQEZ1cnI0IUB', 'Stripe', 'test', '2025-06-11 02:59:21', '2025-06-11 02:59:21')
DEBUG - 2025-06-11 02:59:21 --> btdbFindBy ---> INSERT INTO `start-payment` (`acctpmt_pid`, `account_id`, `user_id`, `amount`, `currency`, `charge_id`, `merchant`, `mode`, `created_at`, `updated_at`) VALUES ('2f9abb9fe9cf9a31', 1, '2', '10', 'USD', 'ch_3RYesdLtUaKDxQEZ1cnI0IUB', 'Stripe', 'test', '2025-06-11 02:59:21', '2025-06-11 02:59:21')
DEBUG - 2025-06-11 02:59:21 --> btdbUpdate ---> UPDATE `start-user` SET `orig_email` = '<EMAIL>', `is_fraud` = '', `status` = 'active', `updated_at` = '2025-06-11 02:59:21'
WHERE `start-user`.`user_id` IN ('2')
DEBUG - 2025-06-11 02:59:21 --> btdbFindBy ---> SELECT `start-plan`.*, `start-account`.*, DATE_FORMAT(resumed_at , "%M %e, %Y") as "resumed_at_formatted", `start-user`.`mode`, case when DATE_ADD(end_date, INTERVAL 1 DAY) < now() then 'yes' else 'no' end as 'expired'
FROM `start-account`
JOIN `start-user` ON `start-user`.`user_id` = `start-account`.`user_id`
JOIN `start-plan` ON `start-plan`.`plan_id` = `start-account`.`plan_id`
WHERE `start-account`.`user_id` = '2'
ORDER BY `start-account`.`end_date` DESC
WARNING - 2025-06-11 02:59:21 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 113.
 1 APPPATH\Helpers\btflag_helper.php(113): setcookie('pricing', null, **********, '', '')
 2 APPPATH\Controllers\Api.php(5487): btflag_remove('pricing')
 3 APPPATH\Controllers\Api.php(2261): App\Controllers\Api->doAfterPayment([...])
 4 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->executeStripeDirectLink('da425fb04f41c96f9d254366dedd4f32')
 5 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 6 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 7 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 8 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-11 02:59:21 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 114.
 1 APPPATH\Helpers\btflag_helper.php(114): setcookie('pricing', null, **********, '/', '')
 2 APPPATH\Controllers\Api.php(5487): btflag_remove('pricing')
 3 APPPATH\Controllers\Api.php(2261): App\Controllers\Api->doAfterPayment([...])
 4 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->executeStripeDirectLink('da425fb04f41c96f9d254366dedd4f32')
 5 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 6 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 7 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 8 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-11 02:59:21 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 113.
 1 APPPATH\Helpers\btflag_helper.php(113): setcookie('daily', null, **********, '', '')
 2 APPPATH\Controllers\Api.php(5495): btflag_remove('daily')
 3 APPPATH\Controllers\Api.php(2261): App\Controllers\Api->doAfterPayment([...])
 4 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->executeStripeDirectLink('da425fb04f41c96f9d254366dedd4f32')
 5 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 6 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 7 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 8 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-11 02:59:21 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 114.
 1 APPPATH\Helpers\btflag_helper.php(114): setcookie('daily', null, **********, '/', '')
 2 APPPATH\Controllers\Api.php(5495): btflag_remove('daily')
 3 APPPATH\Controllers\Api.php(2261): App\Controllers\Api->doAfterPayment([...])
 4 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->executeStripeDirectLink('da425fb04f41c96f9d254366dedd4f32')
 5 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 6 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 7 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 8 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
INFO - 2025-06-11 02:59:21 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
WARNING - 2025-06-11 02:59:21 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 113.
 1 APPPATH\Helpers\btflag_helper.php(113): setcookie('pricing', null, **********, '', '')
 2 APPPATH\Controllers\FlowPages\ThankYou.php(68): btflag_remove('pricing')
 3 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\FlowPages\ThankYou->index()
 4 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\FlowPages\ThankYou))
 5 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 6 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 7 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-11 02:59:21 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 114.
 1 APPPATH\Helpers\btflag_helper.php(114): setcookie('pricing', null, **********, '/', '')
 2 APPPATH\Controllers\FlowPages\ThankYou.php(68): btflag_remove('pricing')
 3 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\FlowPages\ThankYou->index()
 4 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\FlowPages\ThankYou))
 5 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 6 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 7 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-11 02:59:21 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 113.
 1 APPPATH\Helpers\btflag_helper.php(113): setcookie('pricing', null, **********, '', '.ai-pro.org')
 2 APPPATH\Controllers\FlowPages\ThankYou.php(69): btflag_remove('pricing', null, [...])
 3 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\FlowPages\ThankYou->index()
 4 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\FlowPages\ThankYou))
 5 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 6 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 7 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-11 02:59:21 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 114.
 1 APPPATH\Helpers\btflag_helper.php(114): setcookie('pricing', null, **********, '/', '.ai-pro.org')
 2 APPPATH\Controllers\FlowPages\ThankYou.php(69): btflag_remove('pricing', null, [...])
 3 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\FlowPages\ThankYou->index()
 4 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\FlowPages\ThankYou))
 5 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 6 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 7 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-06-11 02:59:22 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
DEBUG - 2025-06-11 02:59:22 --> btdbFindBy ---> 
INFO - 2025-06-11 02:59:22 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 02:59:22 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-11 02:59:22 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 02:59:30 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-11 02:59:30 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2025-06-11 02:59:30 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 02:59:30 --> btdbFindBy ---> 
DEBUG - 2025-06-11 02:59:30 --> btdbFindBy ---> SELECT `start-plan`.*, `start-account`.*, DATE_FORMAT(resumed_at , "%M %e, %Y") as "resumed_at_formatted", `start-user`.`mode`, case when DATE_ADD(end_date, INTERVAL 1 DAY) < now() then 'yes' else 'no' end as 'expired'
FROM `start-account`
JOIN `start-user` ON `start-user`.`user_id` = `start-account`.`user_id`
JOIN `start-plan` ON `start-plan`.`plan_id` = `start-account`.`plan_id`
WHERE `start-account`.`user_id` = '2'
ORDER BY `start-account`.`end_date` DESC
DEBUG - 2025-06-11 02:59:30 --> btdbFindBy ---> SELECT *
FROM `start-plan`
WHERE `plan_id` = '143'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-11 02:59:30 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-11 02:59:30 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 02:59:31 --> REQ ----------->
INFO - 2025-06-11 02:59:31 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 02:59:33 --> REQ ----------->
INFO - 2025-06-11 02:59:33 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 02:59:33 --> btdbFindBy ---> 
DEBUG - 2025-06-11 02:59:33 --> btdbFindBy ---> SELECT `start-plan`.*, `start-account`.*, DATE_FORMAT(resumed_at , "%M %e, %Y") as "resumed_at_formatted", `start-user`.`mode`, case when DATE_ADD(end_date, INTERVAL 1 DAY) < now() then 'yes' else 'no' end as 'expired'
FROM `start-account`
JOIN `start-user` ON `start-user`.`user_id` = `start-account`.`user_id`
JOIN `start-plan` ON `start-plan`.`plan_id` = `start-account`.`plan_id`
WHERE `start-account`.`user_id` = '2'
ORDER BY `start-account`.`end_date` DESC
DEBUG - 2025-06-11 02:59:33 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
DEBUG - 2025-06-11 02:59:33 --> btdbFindBy ---> 
INFO - 2025-06-11 02:59:33 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 02:59:34 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-11 02:59:34 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 02:59:34 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-11 02:59:34 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 02:59:34 --> REQ ----------->
INFO - 2025-06-11 02:59:34 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 02:59:36 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
DEBUG - 2025-06-11 02:59:36 --> btdbFindBy ---> 
INFO - 2025-06-11 02:59:36 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 02:59:36 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
DEBUG - 2025-06-11 02:59:36 --> btdbFindBy ---> 
INFO - 2025-06-11 02:59:36 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 02:59:36 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-11 02:59:36 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 02:59:36 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-11 02:59:36 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2025-06-11 02:59:38 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 02:59:38 --> btdbFindBy ---> 
DEBUG - 2025-06-11 02:59:38 --> btdbFindBy ---> SELECT `start-plan`.*, `start-account`.*, DATE_FORMAT(resumed_at , "%M %e, %Y") as "resumed_at_formatted", `start-user`.`mode`, case when DATE_ADD(end_date, INTERVAL 1 DAY) < now() then 'yes' else 'no' end as 'expired'
FROM `start-account`
JOIN `start-user` ON `start-user`.`user_id` = `start-account`.`user_id`
JOIN `start-plan` ON `start-plan`.`plan_id` = `start-account`.`plan_id`
WHERE `start-account`.`user_id` = '2'
ORDER BY `start-account`.`end_date` DESC
DEBUG - 2025-06-11 02:59:38 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-11 02:59:38 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 02:59:39 --> REQ ----------->
INFO - 2025-06-11 02:59:39 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 02:59:41 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
DEBUG - 2025-06-11 02:59:41 --> btdbFindBy ---> 
INFO - 2025-06-11 02:59:41 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 02:59:41 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
DEBUG - 2025-06-11 02:59:41 --> btdbFindBy ---> 
INFO - 2025-06-11 02:59:41 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 02:59:41 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-11 02:59:41 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 02:59:41 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-11 02:59:41 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 02:59:58 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-11 02:59:58 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 02:59:58 --> REQ ----------->
INFO - 2025-06-11 02:59:58 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2025-06-11 03:00:02 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 03:00:02 --> btdbFindBy ---> 
DEBUG - 2025-06-11 03:00:03 --> REQ ----------->
INFO - 2025-06-11 03:00:03 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 03:00:03 --> REQ ----------->
INFO - 2025-06-11 03:00:03 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 03:00:06 --> REQ ----------->
INFO - 2025-06-11 03:00:06 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2025-06-11 03:00:15 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 03:00:16 --> REQ ----------->
INFO - 2025-06-11 03:00:16 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 03:00:20 --> REQ ----------->testuser061125_02@mailinator.com123123123123
DEBUG - 2025-06-11 03:00:20 --> testuser061125_02@mailinator.com123123123123
DEBUG - 2025-06-11 03:00:20 --> btdbFindBy ---> 
DEBUG - 2025-06-11 03:00:20 --> btdbUpdate ---> INSERT INTO `start-user` (`user_pid`, `email`, `mode`, `password`, `first_name`, `last_name`, `login_token`, `ip_address`, `country`, `flags`, `created_at`, `updated_at`) VALUES ('5b9e26ccbf426db9', '<EMAIL>', 'test', '$P$B7XQauxxlCDPpyfOBQ67FYkkTnzmJL1', '', '', '$2y$09$5NQl9xDYdIbEUIpx946W9uSLzEVpfo.u5LeaZXGIUMrvQfT/ozRfi', '::1', '', '{\"aiwp_logged_in\":null,\"mode\":\"test\",\"ppg\":\"97\",\"pp_ctaclr\":\"1559ED\",\"pmt\":\"paydl\",\"vpay\":\"\",\"navmenu\":\"show\",\"splash\":\"off\",\"ailplogo\":\"off\",\"emailid\":\"\",\"theme_flag_name\":\"kt8typtb\",\"theme_flag_value\":\"arcana\"}', '2025-06-11 03:00:20', '2025-06-11 03:00:20')
DEBUG - 2025-06-11 03:00:20 --> btdbFindBy ---> INSERT INTO `start-user` (`user_pid`, `email`, `mode`, `password`, `first_name`, `last_name`, `login_token`, `ip_address`, `country`, `flags`, `created_at`, `updated_at`) VALUES ('5b9e26ccbf426db9', '<EMAIL>', 'test', '$P$B7XQauxxlCDPpyfOBQ67FYkkTnzmJL1', '', '', '$2y$09$5NQl9xDYdIbEUIpx946W9uSLzEVpfo.u5LeaZXGIUMrvQfT/ozRfi', '::1', '', '{\"aiwp_logged_in\":null,\"mode\":\"test\",\"ppg\":\"97\",\"pp_ctaclr\":\"1559ED\",\"pmt\":\"paydl\",\"vpay\":\"\",\"navmenu\":\"show\",\"splash\":\"off\",\"ailplogo\":\"off\",\"emailid\":\"\",\"theme_flag_name\":\"kt8typtb\",\"theme_flag_value\":\"arcana\"}', '2025-06-11 03:00:20', '2025-06-11 03:00:20')
INFO - 2025-06-11 03:00:20 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 03:00:20 --> btdbFindBy ---> SELECT `start-plan`.*, `start-account`.*, DATE_FORMAT(resumed_at , "%M %e, %Y") as "resumed_at_formatted", `start-user`.`mode`, case when DATE_ADD(end_date, INTERVAL 1 DAY) < now() then 'yes' else 'no' end as 'expired'
FROM `start-account`
JOIN `start-user` ON `start-user`.`user_id` = `start-account`.`user_id`
JOIN `start-plan` ON `start-plan`.`plan_id` = `start-account`.`plan_id`
WHERE `start-account`.`user_id` = '3'
ORDER BY `start-account`.`end_date` DESC
DEBUG - 2025-06-11 03:00:20 --> btdbUpdate ---> INSERT INTO `start-user_app` (`email`, `userapp_pid`, `created_at`, `updated_at`) VALUES ('<EMAIL>', '7bb3a4a3f69880ae4a0cb00e9072384a', '2025-06-11 03:00:20', '2025-06-11 03:00:20')
DEBUG - 2025-06-11 03:00:20 --> btdbFindBy ---> INSERT INTO `start-user_app` (`email`, `userapp_pid`, `created_at`, `updated_at`) VALUES ('<EMAIL>', '7bb3a4a3f69880ae4a0cb00e9072384a', '2025-06-11 03:00:20', '2025-06-11 03:00:20')
INFO - 2025-06-11 03:00:20 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 03:00:20 --> btdbFindBy ---> 
DEBUG - 2025-06-11 03:00:20 --> btdbFindBy ---> SELECT `start-plan`.*, `start-account`.*, DATE_FORMAT(resumed_at , "%M %e, %Y") as "resumed_at_formatted", `start-user`.`mode`, case when DATE_ADD(end_date, INTERVAL 1 DAY) < now() then 'yes' else 'no' end as 'expired'
FROM `start-account`
JOIN `start-user` ON `start-user`.`user_id` = `start-account`.`user_id`
JOIN `start-plan` ON `start-plan`.`plan_id` = `start-account`.`plan_id`
WHERE `start-account`.`user_id` = '3'
ORDER BY `start-account`.`end_date` DESC
WARNING - 2025-06-11 03:00:20 --> [DEPRECATED] explode(): Passing null to parameter #2 ($string) of type string is deprecated in APPPATH\Models\BTModel.php on line 52.
 1 APPPATH\Models\BTModel.php(52): explode(',', null)
 2 APPPATH\Helpers\btdb_helper.php(180): App\Models\BTModel->findBy([...], null)
 3 APPPATH\Controllers\FlowPages\Payment.php(64): btdbFindBy('PlanModel', 'plan_id', null)
 4 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\FlowPages\Payment->index()
 5 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\FlowPages\Payment))
 6 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 7 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 8 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
INFO - 2025-06-11 03:00:20 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 03:00:21 --> REQ ----------->97
INFO - 2025-06-11 03:00:22 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 03:00:22 --> btdbFindBy ---> 
DEBUG - 2025-06-11 03:00:22 --> btdbFindBy ---> SELECT `start-plan`.*, `start-account`.*, DATE_FORMAT(resumed_at , "%M %e, %Y") as "resumed_at_formatted", `start-user`.`mode`, case when DATE_ADD(end_date, INTERVAL 1 DAY) < now() then 'yes' else 'no' end as 'expired'
FROM `start-account`
JOIN `start-user` ON `start-user`.`user_id` = `start-account`.`user_id`
JOIN `start-plan` ON `start-plan`.`plan_id` = `start-account`.`plan_id`
WHERE `start-account`.`user_id` = '3'
ORDER BY `start-account`.`end_date` DESC
INFO - 2025-06-11 03:00:38 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 03:00:38 --> btdbFindBy ---> 
DEBUG - 2025-06-11 03:00:38 --> btdbFindBy ---> SELECT *
FROM `start-user`
WHERE `user_pid` = '5b9e26ccbf426db9'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
WARNING - 2025-06-11 03:00:38 --> [DEPRECATED] Optional parameter $_api_key declared before required parameter $_mode is implicitly treated as a required parameter in VENDORPATH\btapp\btcore\src\Payment\Stripe.php on line 15.
 1 SYSTEMPATH\Autoloader\Autoloader.php(314): include_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\vendor\\codeigniter4\\framework\\system\\Debug\\Exceptions.php')
 2 SYSTEMPATH\Autoloader\Autoloader.php(250): CodeIgniter\Autoloader\Autoloader->includeFile('C:\\laragon\\www\\app.ai-pro.org\\btapp\\vendor\\composer/../btapp/btcore/src/Payment/Stripe.php')
 3 APPPATH\Controllers\Api.php(2147): CodeIgniter\Autoloader\Autoloader->loadClassmap('BTCore\\Payment\\Stripe')
 4 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->executeStripeDirectLink('1b39eeebaab677510826d1b8b3d83eeb')
 5 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 6 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 7 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 8 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-06-11 03:00:39 --> btdbUpdate ---> INSERT INTO `start-account` (`account_pid`, `user_id`, `plan_id`, `merchant`, `merchant_customer_id`, `merchant_subscription_id`, `trial_end`, `start_date`, `end_date`, `cancelled_at`, `max_tokens`, `created_at`, `updated_at`) VALUES ('798566be3640a561', '3', '144', 'Stripe', 'cus_STc8gq1r28833d', 'sub_1RYetxLtUaKDxQEZ9e9wgRYd', 0, '2025-06-11 03:00:33', '2025-07-11 03:00:33', 0, '1000000', '2025-06-11 03:00:39', '2025-06-11 03:00:39')
DEBUG - 2025-06-11 03:00:39 --> btdbUpdate ---> INSERT INTO `start-payment` (`acctpmt_pid`, `account_id`, `user_id`, `amount`, `currency`, `charge_id`, `merchant`, `mode`, `created_at`, `updated_at`) VALUES ('4af5000e23429721', 2, '3', '20', 'USD', 'ch_3RYetxLtUaKDxQEZ1lEl79po', 'Stripe', 'test', '2025-06-11 03:00:39', '2025-06-11 03:00:39')
DEBUG - 2025-06-11 03:00:39 --> btdbFindBy ---> INSERT INTO `start-payment` (`acctpmt_pid`, `account_id`, `user_id`, `amount`, `currency`, `charge_id`, `merchant`, `mode`, `created_at`, `updated_at`) VALUES ('4af5000e23429721', 2, '3', '20', 'USD', 'ch_3RYetxLtUaKDxQEZ1lEl79po', 'Stripe', 'test', '2025-06-11 03:00:39', '2025-06-11 03:00:39')
DEBUG - 2025-06-11 03:00:39 --> btdbUpdate ---> UPDATE `start-user` SET `orig_email` = '<EMAIL>', `is_fraud` = '', `status` = 'active', `updated_at` = '2025-06-11 03:00:39'
WHERE `start-user`.`user_id` IN ('3')
DEBUG - 2025-06-11 03:00:39 --> btdbFindBy ---> SELECT `start-plan`.*, `start-account`.*, DATE_FORMAT(resumed_at , "%M %e, %Y") as "resumed_at_formatted", `start-user`.`mode`, case when DATE_ADD(end_date, INTERVAL 1 DAY) < now() then 'yes' else 'no' end as 'expired'
FROM `start-account`
JOIN `start-user` ON `start-user`.`user_id` = `start-account`.`user_id`
JOIN `start-plan` ON `start-plan`.`plan_id` = `start-account`.`plan_id`
WHERE `start-account`.`user_id` = '3'
ORDER BY `start-account`.`end_date` DESC
WARNING - 2025-06-11 03:00:39 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 113.
 1 APPPATH\Helpers\btflag_helper.php(113): setcookie('pricing', null, **********, '', '')
 2 APPPATH\Controllers\Api.php(5487): btflag_remove('pricing')
 3 APPPATH\Controllers\Api.php(2261): App\Controllers\Api->doAfterPayment([...])
 4 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->executeStripeDirectLink('1b39eeebaab677510826d1b8b3d83eeb')
 5 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 6 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 7 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 8 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-11 03:00:39 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 114.
 1 APPPATH\Helpers\btflag_helper.php(114): setcookie('pricing', null, **********, '/', '')
 2 APPPATH\Controllers\Api.php(5487): btflag_remove('pricing')
 3 APPPATH\Controllers\Api.php(2261): App\Controllers\Api->doAfterPayment([...])
 4 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->executeStripeDirectLink('1b39eeebaab677510826d1b8b3d83eeb')
 5 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 6 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 7 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 8 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-11 03:00:39 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 113.
 1 APPPATH\Helpers\btflag_helper.php(113): setcookie('daily', null, **********, '', '')
 2 APPPATH\Controllers\Api.php(5495): btflag_remove('daily')
 3 APPPATH\Controllers\Api.php(2261): App\Controllers\Api->doAfterPayment([...])
 4 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->executeStripeDirectLink('1b39eeebaab677510826d1b8b3d83eeb')
 5 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 6 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 7 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 8 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-11 03:00:39 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 114.
 1 APPPATH\Helpers\btflag_helper.php(114): setcookie('daily', null, **********, '/', '')
 2 APPPATH\Controllers\Api.php(5495): btflag_remove('daily')
 3 APPPATH\Controllers\Api.php(2261): App\Controllers\Api->doAfterPayment([...])
 4 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->executeStripeDirectLink('1b39eeebaab677510826d1b8b3d83eeb')
 5 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 6 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 7 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 8 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
INFO - 2025-06-11 03:00:39 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
WARNING - 2025-06-11 03:00:39 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 113.
 1 APPPATH\Helpers\btflag_helper.php(113): setcookie('pricing', null, **********, '', '')
 2 APPPATH\Controllers\FlowPages\ThankYou.php(68): btflag_remove('pricing')
 3 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\FlowPages\ThankYou->index()
 4 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\FlowPages\ThankYou))
 5 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 6 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 7 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-11 03:00:39 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 114.
 1 APPPATH\Helpers\btflag_helper.php(114): setcookie('pricing', null, **********, '/', '')
 2 APPPATH\Controllers\FlowPages\ThankYou.php(68): btflag_remove('pricing')
 3 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\FlowPages\ThankYou->index()
 4 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\FlowPages\ThankYou))
 5 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 6 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 7 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-11 03:00:39 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 113.
 1 APPPATH\Helpers\btflag_helper.php(113): setcookie('pricing', null, **********, '', '.ai-pro.org')
 2 APPPATH\Controllers\FlowPages\ThankYou.php(69): btflag_remove('pricing', null, [...])
 3 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\FlowPages\ThankYou->index()
 4 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\FlowPages\ThankYou))
 5 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 6 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 7 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-11 03:00:39 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 114.
 1 APPPATH\Helpers\btflag_helper.php(114): setcookie('pricing', null, **********, '/', '.ai-pro.org')
 2 APPPATH\Controllers\FlowPages\ThankYou.php(69): btflag_remove('pricing', null, [...])
 3 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\FlowPages\ThankYou->index()
 4 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\FlowPages\ThankYou))
 5 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 6 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 7 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-06-11 03:00:40 --> REQ ----------->$2y$09$5NQl9xDYdIbEUIpx946W9uSLz
DEBUG - 2025-06-11 03:00:40 --> btdbFindBy ---> 
INFO - 2025-06-11 03:00:40 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 03:00:40 --> REQ ----------->$2y$09$5NQl9xDYdIbEUIpx946W9uSLz
INFO - 2025-06-11 03:00:40 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2025-06-11 03:00:45 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 03:00:45 --> btdbFindBy ---> 
DEBUG - 2025-06-11 03:00:45 --> btdbFindBy ---> SELECT `start-plan`.*, `start-account`.*, DATE_FORMAT(resumed_at , "%M %e, %Y") as "resumed_at_formatted", `start-user`.`mode`, case when DATE_ADD(end_date, INTERVAL 1 DAY) < now() then 'yes' else 'no' end as 'expired'
FROM `start-account`
JOIN `start-user` ON `start-user`.`user_id` = `start-account`.`user_id`
JOIN `start-plan` ON `start-plan`.`plan_id` = `start-account`.`plan_id`
WHERE `start-account`.`user_id` = '3'
ORDER BY `start-account`.`end_date` DESC
DEBUG - 2025-06-11 03:00:45 --> btdbFindBy ---> SELECT *
FROM `start-plan`
WHERE `plan_id` = '144'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-11 03:00:46 --> REQ ----------->$2y$09$5NQl9xDYdIbEUIpx946W9uSLz
INFO - 2025-06-11 03:00:46 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 03:00:46 --> REQ ----------->
INFO - 2025-06-11 03:00:46 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 03:00:48 --> REQ ----------->
DEBUG - 2025-06-11 03:00:48 --> REQ ----------->$2y$09$5NQl9xDYdIbEUIpx946W9uSLz
DEBUG - 2025-06-11 03:00:48 --> btdbFindBy ---> 
INFO - 2025-06-11 03:00:48 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 03:00:48 --> REQ ----------->$2y$09$5NQl9xDYdIbEUIpx946W9uSLz
INFO - 2025-06-11 03:00:48 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 03:00:53 --> REQ ----------->$2y$09$5NQl9xDYdIbEUIpx946W9uSLz
INFO - 2025-06-11 03:00:53 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 03:00:53 --> REQ ----------->
INFO - 2025-06-11 03:00:53 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2025-06-11 03:00:56 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 03:00:56 --> btdbFindBy ---> 
DEBUG - 2025-06-11 03:00:56 --> btdbFindBy ---> SELECT `start-plan`.*, `start-account`.*, DATE_FORMAT(resumed_at , "%M %e, %Y") as "resumed_at_formatted", `start-user`.`mode`, case when DATE_ADD(end_date, INTERVAL 1 DAY) < now() then 'yes' else 'no' end as 'expired'
FROM `start-account`
JOIN `start-user` ON `start-user`.`user_id` = `start-account`.`user_id`
JOIN `start-plan` ON `start-plan`.`plan_id` = `start-account`.`plan_id`
WHERE `start-account`.`user_id` = '3'
ORDER BY `start-account`.`end_date` DESC
DEBUG - 2025-06-11 03:00:57 --> REQ ----------->$2y$09$5NQl9xDYdIbEUIpx946W9uSLz
INFO - 2025-06-11 03:00:57 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 03:00:57 --> REQ ----------->
INFO - 2025-06-11 03:00:57 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 03:00:59 --> REQ ----------->$2y$09$5NQl9xDYdIbEUIpx946W9uSLz
DEBUG - 2025-06-11 03:00:59 --> btdbFindBy ---> 
INFO - 2025-06-11 03:00:59 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 03:00:59 --> REQ ----------->$2y$09$5NQl9xDYdIbEUIpx946W9uSLz
DEBUG - 2025-06-11 03:00:59 --> btdbFindBy ---> 
INFO - 2025-06-11 03:00:59 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 03:00:59 --> REQ ----------->$2y$09$5NQl9xDYdIbEUIpx946W9uSLz
INFO - 2025-06-11 03:00:59 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 03:00:59 --> REQ ----------->$2y$09$5NQl9xDYdIbEUIpx946W9uSLz
INFO - 2025-06-11 03:00:59 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 03:03:30 --> REQ ----------->$2y$09$5NQl9xDYdIbEUIpx946W9uSLz
INFO - 2025-06-11 03:03:30 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 03:03:30 --> REQ ----------->
INFO - 2025-06-11 03:03:30 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 03:05:11 --> REQ ----------->$2y$09$5NQl9xDYdIbEUIpx946W9uSLz
INFO - 2025-06-11 03:05:11 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 03:05:11 --> REQ ----------->
INFO - 2025-06-11 03:05:11 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 03:05:14 --> REQ ----------->$2y$09$5NQl9xDYdIbEUIpx946W9uSLz
INFO - 2025-06-11 03:05:14 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 03:06:40 --> REQ ----------->$2y$09$5NQl9xDYdIbEUIpx946W9uSLz
INFO - 2025-06-11 03:06:40 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 03:06:40 --> REQ ----------->
INFO - 2025-06-11 03:06:40 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 03:06:57 --> REQ ----------->
INFO - 2025-06-11 03:06:57 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 03:06:57 --> REQ ----------->
INFO - 2025-06-11 03:06:57 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 03:06:59 --> REQ ----------->
INFO - 2025-06-11 03:06:59 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 03:07:01 --> REQ ----------->
INFO - 2025-06-11 03:07:01 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 03:07:07 --> REQ ----------->
INFO - 2025-06-11 03:07:07 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 03:07:13 --> REQ ----------->$2y$09$5NQl9xDYdIbEUIpx946W9uSLz
INFO - 2025-06-11 03:07:13 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 03:07:13 --> REQ ----------->
INFO - 2025-06-11 03:07:13 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2025-06-11 03:08:43 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 03:08:44 --> REQ ----------->$2y$09$v32tDHuBQgmAB3J5DhJdkudHM
INFO - 2025-06-11 03:08:44 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 03:08:44 --> btdbFindBy ---> 
WARNING - 2025-06-11 03:08:44 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('aiwp_logged_in', null, 1752203324, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(400): btflag_set('aiwp_logged_in', null, [...])
 3 APPPATH\Controllers\Api.php(799): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-11 03:08:44 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('aiproStart', null, 1752203324, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(401): btflag_set('aiproStart', null, [...])
 3 APPPATH\Controllers\Api.php(799): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-11 03:08:44 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('user_email', null, 1752203324, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(402): btflag_set('user_email', null, [...])
 3 APPPATH\Controllers\Api.php(799): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-11 03:08:44 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('chatpro_KJLF4XgSL8wjlGm', null, 1752203324, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(403): btflag_set('chatpro_KJLF4XgSL8wjlGm', null, [...])
 3 APPPATH\Controllers\Api.php(799): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-06-11 03:08:44 --> btdbFindBy ---> SELECT *
FROM `start-user`
WHERE `login_token` = '$2y$09$v32tDHuBQgmAB3J5DhJdkudHM'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
INFO - 2025-06-11 03:08:44 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 03:08:44 --> btdbFindBy ---> 
DEBUG - 2025-06-11 03:08:44 --> btdbFindBy ---> SELECT `start-plan`.*, `start-account`.*, DATE_FORMAT(resumed_at , "%M %e, %Y") as "resumed_at_formatted", `start-user`.`mode`, case when DATE_ADD(end_date, INTERVAL 1 DAY) < now() then 'yes' else 'no' end as 'expired'
FROM `start-account`
JOIN `start-user` ON `start-user`.`user_id` = `start-account`.`user_id`
JOIN `start-plan` ON `start-plan`.`plan_id` = `start-account`.`plan_id`
WHERE `start-account`.`user_id` = '1'
ORDER BY `start-account`.`end_date` DESC
DEBUG - 2025-06-11 03:08:45 --> REQ ----------->$2y$09$v32tDHuBQgmAB3J5DhJdkudHM
INFO - 2025-06-11 03:08:45 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 03:08:45 --> REQ ----------->
INFO - 2025-06-11 03:08:45 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 03:08:45 --> REQ ----------->
DEBUG - 2025-06-11 03:08:45 --> REQ ----------->$2y$09$v32tDHuBQgmAB3J5DhJdkudHM
DEBUG - 2025-06-11 03:08:45 --> btdbFindBy ---> 
INFO - 2025-06-11 03:08:45 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 03:08:45 --> REQ ----------->$2y$09$v32tDHuBQgmAB3J5DhJdkudHM
INFO - 2025-06-11 03:08:45 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 03:08:57 --> REQ ----------->$2y$09$v32tDHuBQgmAB3J5DhJdkudHM
INFO - 2025-06-11 03:08:57 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 03:08:57 --> REQ ----------->
INFO - 2025-06-11 03:08:57 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 03:09:01 --> REQ ----------->$2y$09$5NQl9xDYdIbEUIpx946W9uSLz
INFO - 2025-06-11 03:09:01 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 03:09:01 --> REQ ----------->
INFO - 2025-06-11 03:09:01 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 03:09:25 --> REQ ----------->$2y$09$5NQl9xDYdIbEUIpx946W9uSLz
INFO - 2025-06-11 03:09:25 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 03:09:25 --> REQ ----------->
INFO - 2025-06-11 03:09:25 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 03:11:29 --> REQ ----------->
INFO - 2025-06-11 03:11:29 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 03:11:29 --> REQ ----------->
INFO - 2025-06-11 03:11:29 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 03:11:58 --> REQ ----------->
INFO - 2025-06-11 03:11:58 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2025-06-11 03:11:58 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 03:11:59 --> REQ ----------->
INFO - 2025-06-11 03:11:59 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 03:12:12 --> REQ ----------->
INFO - 2025-06-11 03:12:12 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 03:13:12 --> REQ ----------->
INFO - 2025-06-11 03:13:12 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 03:13:12 --> REQ ----------->
INFO - 2025-06-11 03:13:12 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 03:14:37 --> REQ ----------->testuser061125_03@mailinator.com123123123123
DEBUG - 2025-06-11 03:14:37 --> testuser061125_03@mailinator.com123123123123
DEBUG - 2025-06-11 03:14:37 --> btdbFindBy ---> 
DEBUG - 2025-06-11 03:14:37 --> btdbUpdate ---> INSERT INTO `start-user` (`user_pid`, `email`, `mode`, `password`, `first_name`, `last_name`, `login_token`, `ip_address`, `country`, `flags`, `created_at`, `updated_at`) VALUES ('eb0de510efe653d7', '<EMAIL>', 'test', '$P$BAnG8D2172cim6xIxPn9/y5iz9rLCf0', '', '', '$2y$09$RUg.KgNGWxhQXqKrl1BXruhseGhsY6DrjASF6cgv43odGlCGPeNsu', '::1', '', '{\"aiwp_logged_in\":null,\"mode\":\"test\",\"ppg\":\"97\",\"pp_ctaclr\":\"1559ED\",\"pmt\":\"rec\",\"vpay\":\"\",\"navmenu\":\"show\",\"splash\":\"off\",\"ailplogo\":\"off\",\"emailid\":\"\",\"theme_flag_name\":\"kt8typtb\",\"theme_flag_value\":\"arcana\"}', '2025-06-11 03:14:37', '2025-06-11 03:14:37')
DEBUG - 2025-06-11 03:14:37 --> btdbFindBy ---> INSERT INTO `start-user` (`user_pid`, `email`, `mode`, `password`, `first_name`, `last_name`, `login_token`, `ip_address`, `country`, `flags`, `created_at`, `updated_at`) VALUES ('eb0de510efe653d7', '<EMAIL>', 'test', '$P$BAnG8D2172cim6xIxPn9/y5iz9rLCf0', '', '', '$2y$09$RUg.KgNGWxhQXqKrl1BXruhseGhsY6DrjASF6cgv43odGlCGPeNsu', '::1', '', '{\"aiwp_logged_in\":null,\"mode\":\"test\",\"ppg\":\"97\",\"pp_ctaclr\":\"1559ED\",\"pmt\":\"rec\",\"vpay\":\"\",\"navmenu\":\"show\",\"splash\":\"off\",\"ailplogo\":\"off\",\"emailid\":\"\",\"theme_flag_name\":\"kt8typtb\",\"theme_flag_value\":\"arcana\"}', '2025-06-11 03:14:37', '2025-06-11 03:14:37')
INFO - 2025-06-11 03:14:37 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 03:14:37 --> btdbFindBy ---> SELECT `start-plan`.*, `start-account`.*, DATE_FORMAT(resumed_at , "%M %e, %Y") as "resumed_at_formatted", `start-user`.`mode`, case when DATE_ADD(end_date, INTERVAL 1 DAY) < now() then 'yes' else 'no' end as 'expired'
FROM `start-account`
JOIN `start-user` ON `start-user`.`user_id` = `start-account`.`user_id`
JOIN `start-plan` ON `start-plan`.`plan_id` = `start-account`.`plan_id`
WHERE `start-account`.`user_id` = '4'
ORDER BY `start-account`.`end_date` DESC
DEBUG - 2025-06-11 03:14:37 --> btdbUpdate ---> INSERT INTO `start-user_app` (`email`, `userapp_pid`, `created_at`, `updated_at`) VALUES ('<EMAIL>', '3f1c0cc25bc9f2cf9dc30ba912b2d454', '2025-06-11 03:14:37', '2025-06-11 03:14:37')
DEBUG - 2025-06-11 03:14:37 --> btdbFindBy ---> INSERT INTO `start-user_app` (`email`, `userapp_pid`, `created_at`, `updated_at`) VALUES ('<EMAIL>', '3f1c0cc25bc9f2cf9dc30ba912b2d454', '2025-06-11 03:14:37', '2025-06-11 03:14:37')
INFO - 2025-06-11 03:14:38 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 03:14:38 --> btdbFindBy ---> 
DEBUG - 2025-06-11 03:14:38 --> btdbFindBy ---> SELECT `start-plan`.*, `start-account`.*, DATE_FORMAT(resumed_at , "%M %e, %Y") as "resumed_at_formatted", `start-user`.`mode`, case when DATE_ADD(end_date, INTERVAL 1 DAY) < now() then 'yes' else 'no' end as 'expired'
FROM `start-account`
JOIN `start-user` ON `start-user`.`user_id` = `start-account`.`user_id`
JOIN `start-plan` ON `start-plan`.`plan_id` = `start-account`.`plan_id`
WHERE `start-account`.`user_id` = '4'
ORDER BY `start-account`.`end_date` DESC
WARNING - 2025-06-11 03:14:38 --> [DEPRECATED] explode(): Passing null to parameter #2 ($string) of type string is deprecated in APPPATH\Models\BTModel.php on line 52.
 1 APPPATH\Models\BTModel.php(52): explode(',', null)
 2 APPPATH\Helpers\btdb_helper.php(180): App\Models\BTModel->findBy([...], null)
 3 APPPATH\Controllers\FlowPages\Payment.php(64): btdbFindBy('PlanModel', 'plan_id', null)
 4 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\FlowPages\Payment->index()
 5 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\FlowPages\Payment))
 6 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 7 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 8 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
INFO - 2025-06-11 03:14:38 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 03:14:38 --> REQ ----------->97
INFO - 2025-06-11 03:14:40 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 03:14:41 --> btdbFindBy ---> 
DEBUG - 2025-06-11 03:14:41 --> btdbFindBy ---> SELECT `start-plan`.*, `start-account`.*, DATE_FORMAT(resumed_at , "%M %e, %Y") as "resumed_at_formatted", `start-user`.`mode`, case when DATE_ADD(end_date, INTERVAL 1 DAY) < now() then 'yes' else 'no' end as 'expired'
FROM `start-account`
JOIN `start-user` ON `start-user`.`user_id` = `start-account`.`user_id`
JOIN `start-plan` ON `start-plan`.`plan_id` = `start-account`.`plan_id`
WHERE `start-account`.`user_id` = '4'
ORDER BY `start-account`.`end_date` DESC
INFO - 2025-06-11 03:14:56 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 03:14:56 --> btdbFindBy ---> 
DEBUG - 2025-06-11 03:14:56 --> btdbFindBy ---> SELECT *
FROM `start-user`
WHERE `user_pid` = 'eb0de510efe653d7'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
WARNING - 2025-06-11 03:14:56 --> [DEPRECATED] Optional parameter $_api_key declared before required parameter $_mode is implicitly treated as a required parameter in VENDORPATH\btapp\btcore\src\Payment\Stripe.php on line 15.
 1 SYSTEMPATH\Autoloader\Autoloader.php(314): include_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\vendor\\codeigniter4\\framework\\system\\Debug\\Exceptions.php')
 2 SYSTEMPATH\Autoloader\Autoloader.php(250): CodeIgniter\Autoloader\Autoloader->includeFile('C:\\laragon\\www\\app.ai-pro.org\\btapp\\vendor\\composer/../btapp/btcore/src/Payment/Stripe.php')
 3 APPPATH\Controllers\Api.php(2147): CodeIgniter\Autoloader\Autoloader->loadClassmap('BTCore\\Payment\\Stripe')
 4 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->executeStripeDirectLink('b593625605f74f2c742dd1fdeabb1a84')
 5 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 6 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 7 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 8 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-06-11 03:15:05 --> btdbUpdate ---> INSERT INTO `start-account` (`account_pid`, `user_id`, `plan_id`, `merchant`, `merchant_customer_id`, `merchant_subscription_id`, `trial_end`, `start_date`, `end_date`, `cancelled_at`, `max_tokens`, `created_at`, `updated_at`) VALUES ('995039ff63fea40b', '4', '145', 'Stripe', 'cus_STcMvBgZZJlGv8', 'sub_1RYf7nLtUaKDxQEZegTOOCIp', 0, '2025-06-11 03:14:51', '2025-07-11 03:14:51', 0, '********', '2025-06-11 03:15:05', '2025-06-11 03:15:05')
DEBUG - 2025-06-11 03:15:05 --> btdbUpdate ---> INSERT INTO `start-payment` (`acctpmt_pid`, `account_id`, `user_id`, `amount`, `currency`, `charge_id`, `merchant`, `mode`, `created_at`, `updated_at`) VALUES ('a0095e62b38a568e', 3, '4', '97', 'USD', 'ch_3RYf7oLtUaKDxQEZ0giPbcRz', 'Stripe', 'test', '2025-06-11 03:15:05', '2025-06-11 03:15:05')
DEBUG - 2025-06-11 03:15:05 --> btdbFindBy ---> INSERT INTO `start-payment` (`acctpmt_pid`, `account_id`, `user_id`, `amount`, `currency`, `charge_id`, `merchant`, `mode`, `created_at`, `updated_at`) VALUES ('a0095e62b38a568e', 3, '4', '97', 'USD', 'ch_3RYf7oLtUaKDxQEZ0giPbcRz', 'Stripe', 'test', '2025-06-11 03:15:05', '2025-06-11 03:15:05')
DEBUG - 2025-06-11 03:15:05 --> btdbUpdate ---> UPDATE `start-user` SET `orig_email` = '<EMAIL>', `is_fraud` = '', `status` = 'active', `updated_at` = '2025-06-11 03:15:05'
WHERE `start-user`.`user_id` IN ('4')
DEBUG - 2025-06-11 03:15:05 --> btdbFindBy ---> SELECT `start-plan`.*, `start-account`.*, DATE_FORMAT(resumed_at , "%M %e, %Y") as "resumed_at_formatted", `start-user`.`mode`, case when DATE_ADD(end_date, INTERVAL 1 DAY) < now() then 'yes' else 'no' end as 'expired'
FROM `start-account`
JOIN `start-user` ON `start-user`.`user_id` = `start-account`.`user_id`
JOIN `start-plan` ON `start-plan`.`plan_id` = `start-account`.`plan_id`
WHERE `start-account`.`user_id` = '4'
ORDER BY `start-account`.`end_date` DESC
WARNING - 2025-06-11 03:15:05 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 113.
 1 APPPATH\Helpers\btflag_helper.php(113): setcookie('pricing', null, **********, '', '')
 2 APPPATH\Controllers\Api.php(5487): btflag_remove('pricing')
 3 APPPATH\Controllers\Api.php(2261): App\Controllers\Api->doAfterPayment([...])
 4 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->executeStripeDirectLink('b593625605f74f2c742dd1fdeabb1a84')
 5 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 6 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 7 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 8 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-11 03:15:05 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 114.
 1 APPPATH\Helpers\btflag_helper.php(114): setcookie('pricing', null, **********, '/', '')
 2 APPPATH\Controllers\Api.php(5487): btflag_remove('pricing')
 3 APPPATH\Controllers\Api.php(2261): App\Controllers\Api->doAfterPayment([...])
 4 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->executeStripeDirectLink('b593625605f74f2c742dd1fdeabb1a84')
 5 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 6 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 7 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 8 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-11 03:15:05 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 113.
 1 APPPATH\Helpers\btflag_helper.php(113): setcookie('daily', null, **********, '', '')
 2 APPPATH\Controllers\Api.php(5495): btflag_remove('daily')
 3 APPPATH\Controllers\Api.php(2261): App\Controllers\Api->doAfterPayment([...])
 4 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->executeStripeDirectLink('b593625605f74f2c742dd1fdeabb1a84')
 5 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 6 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 7 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 8 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-11 03:15:05 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 114.
 1 APPPATH\Helpers\btflag_helper.php(114): setcookie('daily', null, **********, '/', '')
 2 APPPATH\Controllers\Api.php(5495): btflag_remove('daily')
 3 APPPATH\Controllers\Api.php(2261): App\Controllers\Api->doAfterPayment([...])
 4 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->executeStripeDirectLink('b593625605f74f2c742dd1fdeabb1a84')
 5 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 6 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 7 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 8 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
INFO - 2025-06-11 03:15:05 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
WARNING - 2025-06-11 03:15:05 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 113.
 1 APPPATH\Helpers\btflag_helper.php(113): setcookie('pricing', null, **********, '', '')
 2 APPPATH\Controllers\FlowPages\ThankYou.php(68): btflag_remove('pricing')
 3 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\FlowPages\ThankYou->index()
 4 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\FlowPages\ThankYou))
 5 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 6 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 7 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-11 03:15:05 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 114.
 1 APPPATH\Helpers\btflag_helper.php(114): setcookie('pricing', null, **********, '/', '')
 2 APPPATH\Controllers\FlowPages\ThankYou.php(68): btflag_remove('pricing')
 3 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\FlowPages\ThankYou->index()
 4 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\FlowPages\ThankYou))
 5 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 6 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 7 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-11 03:15:05 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 113.
 1 APPPATH\Helpers\btflag_helper.php(113): setcookie('pricing', null, **********, '', '.ai-pro.org')
 2 APPPATH\Controllers\FlowPages\ThankYou.php(69): btflag_remove('pricing', null, [...])
 3 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\FlowPages\ThankYou->index()
 4 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\FlowPages\ThankYou))
 5 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 6 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 7 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-11 03:15:05 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 114.
 1 APPPATH\Helpers\btflag_helper.php(114): setcookie('pricing', null, **********, '/', '.ai-pro.org')
 2 APPPATH\Controllers\FlowPages\ThankYou.php(69): btflag_remove('pricing', null, [...])
 3 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\FlowPages\ThankYou->index()
 4 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\FlowPages\ThankYou))
 5 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 6 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 7 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-06-11 03:15:06 --> REQ ----------->$2y$09$RUg.KgNGWxhQXqKrl1BXruhse
DEBUG - 2025-06-11 03:15:06 --> btdbFindBy ---> 
INFO - 2025-06-11 03:15:06 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 03:15:06 --> REQ ----------->$2y$09$RUg.KgNGWxhQXqKrl1BXruhse
INFO - 2025-06-11 03:15:06 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 03:15:21 --> REQ ----------->$2y$09$RUg.KgNGWxhQXqKrl1BXruhse
INFO - 2025-06-11 03:15:21 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2025-06-11 03:15:22 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 03:15:22 --> btdbFindBy ---> 
DEBUG - 2025-06-11 03:15:22 --> btdbFindBy ---> SELECT `start-plan`.*, `start-account`.*, DATE_FORMAT(resumed_at , "%M %e, %Y") as "resumed_at_formatted", `start-user`.`mode`, case when DATE_ADD(end_date, INTERVAL 1 DAY) < now() then 'yes' else 'no' end as 'expired'
FROM `start-account`
JOIN `start-user` ON `start-user`.`user_id` = `start-account`.`user_id`
JOIN `start-plan` ON `start-plan`.`plan_id` = `start-account`.`plan_id`
WHERE `start-account`.`user_id` = '4'
ORDER BY `start-account`.`end_date` DESC
DEBUG - 2025-06-11 03:15:22 --> btdbFindBy ---> SELECT *
FROM `start-plan`
WHERE `plan_id` = '145'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-11 03:15:22 --> REQ ----------->$2y$09$RUg.KgNGWxhQXqKrl1BXruhse
INFO - 2025-06-11 03:15:22 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 03:15:22 --> REQ ----------->
INFO - 2025-06-11 03:15:22 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 03:15:25 --> REQ ----------->
DEBUG - 2025-06-11 03:15:25 --> REQ ----------->$2y$09$RUg.KgNGWxhQXqKrl1BXruhse
DEBUG - 2025-06-11 03:15:25 --> btdbFindBy ---> 
INFO - 2025-06-11 03:15:25 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2025-06-11 03:15:25 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 03:15:25 --> btdbFindBy ---> 
DEBUG - 2025-06-11 03:15:25 --> btdbFindBy ---> SELECT `start-plan`.*, `start-account`.*, DATE_FORMAT(resumed_at , "%M %e, %Y") as "resumed_at_formatted", `start-user`.`mode`, case when DATE_ADD(end_date, INTERVAL 1 DAY) < now() then 'yes' else 'no' end as 'expired'
FROM `start-account`
JOIN `start-user` ON `start-user`.`user_id` = `start-account`.`user_id`
JOIN `start-plan` ON `start-plan`.`plan_id` = `start-account`.`plan_id`
WHERE `start-account`.`user_id` = '4'
ORDER BY `start-account`.`end_date` DESC
DEBUG - 2025-06-11 03:15:26 --> REQ ----------->$2y$09$RUg.KgNGWxhQXqKrl1BXruhse
INFO - 2025-06-11 03:15:26 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 03:15:26 --> REQ ----------->
INFO - 2025-06-11 03:15:26 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 03:15:27 --> REQ ----------->$2y$09$RUg.KgNGWxhQXqKrl1BXruhse
DEBUG - 2025-06-11 03:15:27 --> btdbFindBy ---> 
INFO - 2025-06-11 03:15:27 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 03:15:27 --> REQ ----------->$2y$09$RUg.KgNGWxhQXqKrl1BXruhse
DEBUG - 2025-06-11 03:15:27 --> btdbFindBy ---> 
INFO - 2025-06-11 03:15:28 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 03:15:28 --> REQ ----------->$2y$09$RUg.KgNGWxhQXqKrl1BXruhse
INFO - 2025-06-11 03:15:28 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 03:15:28 --> REQ ----------->$2y$09$RUg.KgNGWxhQXqKrl1BXruhse
INFO - 2025-06-11 03:15:28 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 03:16:34 --> REQ ----------->
INFO - 2025-06-11 03:16:34 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 03:16:34 --> REQ ----------->
INFO - 2025-06-11 03:16:34 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2025-06-11 03:16:39 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 03:16:40 --> REQ ----------->
INFO - 2025-06-11 03:16:40 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 03:16:46 --> REQ ----------->
INFO - 2025-06-11 03:16:46 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 03:16:48 --> REQ ----------->testuser061125_04@mailinator.com123123123123
DEBUG - 2025-06-11 03:16:48 --> testuser061125_04@mailinator.com123123123123
DEBUG - 2025-06-11 03:16:49 --> btdbFindBy ---> 
DEBUG - 2025-06-11 03:16:49 --> btdbUpdate ---> INSERT INTO `start-user` (`user_pid`, `email`, `mode`, `password`, `first_name`, `last_name`, `login_token`, `ip_address`, `country`, `flags`, `created_at`, `updated_at`) VALUES ('94473b45e0c12d30', '<EMAIL>', 'test', '$P$BQVtynbe9DbAizzUYsucKJtinvFnvv.', '', '', '$2y$09$eksRuVXOXUgoEpP7NyQ67u/U.xPxA2adKw9APfrrA8MvkU/LDQgFq', '::1', '', '{\"aiwp_logged_in\":null,\"mode\":\"test\",\"ppg\":\"119\",\"pp_ctaclr\":\"1559ED\",\"pmt\":\"rec\",\"vpay\":\"\",\"navmenu\":\"show\",\"splash\":\"off\",\"ailplogo\":\"off\",\"emailid\":\"\",\"theme_flag_name\":\"kt8typtb\",\"theme_flag_value\":\"arcana\"}', '2025-06-11 03:16:49', '2025-06-11 03:16:49')
DEBUG - 2025-06-11 03:16:49 --> btdbFindBy ---> INSERT INTO `start-user` (`user_pid`, `email`, `mode`, `password`, `first_name`, `last_name`, `login_token`, `ip_address`, `country`, `flags`, `created_at`, `updated_at`) VALUES ('94473b45e0c12d30', '<EMAIL>', 'test', '$P$BQVtynbe9DbAizzUYsucKJtinvFnvv.', '', '', '$2y$09$eksRuVXOXUgoEpP7NyQ67u/U.xPxA2adKw9APfrrA8MvkU/LDQgFq', '::1', '', '{\"aiwp_logged_in\":null,\"mode\":\"test\",\"ppg\":\"119\",\"pp_ctaclr\":\"1559ED\",\"pmt\":\"rec\",\"vpay\":\"\",\"navmenu\":\"show\",\"splash\":\"off\",\"ailplogo\":\"off\",\"emailid\":\"\",\"theme_flag_name\":\"kt8typtb\",\"theme_flag_value\":\"arcana\"}', '2025-06-11 03:16:49', '2025-06-11 03:16:49')
INFO - 2025-06-11 03:16:49 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 03:16:49 --> btdbFindBy ---> SELECT `start-plan`.*, `start-account`.*, DATE_FORMAT(resumed_at , "%M %e, %Y") as "resumed_at_formatted", `start-user`.`mode`, case when DATE_ADD(end_date, INTERVAL 1 DAY) < now() then 'yes' else 'no' end as 'expired'
FROM `start-account`
JOIN `start-user` ON `start-user`.`user_id` = `start-account`.`user_id`
JOIN `start-plan` ON `start-plan`.`plan_id` = `start-account`.`plan_id`
WHERE `start-account`.`user_id` = '5'
ORDER BY `start-account`.`end_date` DESC
DEBUG - 2025-06-11 03:16:49 --> btdbUpdate ---> INSERT INTO `start-user_app` (`email`, `userapp_pid`, `created_at`, `updated_at`) VALUES ('<EMAIL>', 'db202e9d8b4c84c38f2f4b7ef9c740ce', '2025-06-11 03:16:49', '2025-06-11 03:16:49')
DEBUG - 2025-06-11 03:16:49 --> btdbFindBy ---> INSERT INTO `start-user_app` (`email`, `userapp_pid`, `created_at`, `updated_at`) VALUES ('<EMAIL>', 'db202e9d8b4c84c38f2f4b7ef9c740ce', '2025-06-11 03:16:49', '2025-06-11 03:16:49')
INFO - 2025-06-11 03:16:49 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 03:16:49 --> btdbFindBy ---> 
DEBUG - 2025-06-11 03:16:49 --> btdbFindBy ---> SELECT `start-plan`.*, `start-account`.*, DATE_FORMAT(resumed_at , "%M %e, %Y") as "resumed_at_formatted", `start-user`.`mode`, case when DATE_ADD(end_date, INTERVAL 1 DAY) < now() then 'yes' else 'no' end as 'expired'
FROM `start-account`
JOIN `start-user` ON `start-user`.`user_id` = `start-account`.`user_id`
JOIN `start-plan` ON `start-plan`.`plan_id` = `start-account`.`plan_id`
WHERE `start-account`.`user_id` = '5'
ORDER BY `start-account`.`end_date` DESC
WARNING - 2025-06-11 03:16:49 --> [DEPRECATED] explode(): Passing null to parameter #2 ($string) of type string is deprecated in APPPATH\Models\BTModel.php on line 52.
 1 APPPATH\Models\BTModel.php(52): explode(',', null)
 2 APPPATH\Helpers\btdb_helper.php(180): App\Models\BTModel->findBy([...], null)
 3 APPPATH\Controllers\FlowPages\Payment.php(64): btdbFindBy('PlanModel', 'plan_id', null)
 4 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\FlowPages\Payment->index()
 5 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\FlowPages\Payment))
 6 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 7 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 8 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
INFO - 2025-06-11 03:16:49 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 03:16:49 --> REQ ----------->119
INFO - 2025-06-11 03:16:59 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 03:16:59 --> btdbFindBy ---> 
DEBUG - 2025-06-11 03:16:59 --> btdbFindBy ---> SELECT `start-plan`.*, `start-account`.*, DATE_FORMAT(resumed_at , "%M %e, %Y") as "resumed_at_formatted", `start-user`.`mode`, case when DATE_ADD(end_date, INTERVAL 1 DAY) < now() then 'yes' else 'no' end as 'expired'
FROM `start-account`
JOIN `start-user` ON `start-user`.`user_id` = `start-account`.`user_id`
JOIN `start-plan` ON `start-plan`.`plan_id` = `start-account`.`plan_id`
WHERE `start-account`.`user_id` = '5'
ORDER BY `start-account`.`end_date` DESC
INFO - 2025-06-11 03:17:17 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 03:17:17 --> btdbFindBy ---> 
DEBUG - 2025-06-11 03:17:17 --> btdbFindBy ---> SELECT *
FROM `start-user`
WHERE `user_pid` = '94473b45e0c12d30'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
WARNING - 2025-06-11 03:17:17 --> [DEPRECATED] Optional parameter $_api_key declared before required parameter $_mode is implicitly treated as a required parameter in VENDORPATH\btapp\btcore\src\Payment\Stripe.php on line 15.
 1 SYSTEMPATH\Autoloader\Autoloader.php(314): include_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\vendor\\codeigniter4\\framework\\system\\Debug\\Exceptions.php')
 2 SYSTEMPATH\Autoloader\Autoloader.php(250): CodeIgniter\Autoloader\Autoloader->includeFile('C:\\laragon\\www\\app.ai-pro.org\\btapp\\vendor\\composer/../btapp/btcore/src/Payment/Stripe.php')
 3 APPPATH\Controllers\Api.php(2147): CodeIgniter\Autoloader\Autoloader->loadClassmap('BTCore\\Payment\\Stripe')
 4 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->executeStripeDirectLink('f6ce7c54e2a4eeca798190ee06d4ab7b')
 5 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 6 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 7 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 8 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-06-11 03:17:18 --> btdbUpdate ---> INSERT INTO `start-account` (`account_pid`, `user_id`, `plan_id`, `merchant`, `merchant_customer_id`, `merchant_subscription_id`, `trial_end`, `start_date`, `end_date`, `cancelled_at`, `max_tokens`, `created_at`, `updated_at`) VALUES ('4e64fe0b5dc4d44b', '5', '198', 'Stripe', 'cus_STcO6Y59TCC4Pj', 'sub_1RYfA4LtUaKDxQEZ0dlMnEwy', 0, '2025-06-11 03:17:12', '2025-07-11 03:17:12', 0, '2500000', '2025-06-11 03:17:18', '2025-06-11 03:17:18')
DEBUG - 2025-06-11 03:17:18 --> btdbUpdate ---> INSERT INTO `start-payment` (`acctpmt_pid`, `account_id`, `user_id`, `amount`, `currency`, `charge_id`, `merchant`, `mode`, `created_at`, `updated_at`) VALUES ('013ea2e9553bc199', 4, '5', '49', 'USD', 'ch_3RYfA4LtUaKDxQEZ1LJ9s2CO', 'Stripe', 'test', '2025-06-11 03:17:18', '2025-06-11 03:17:18')
DEBUG - 2025-06-11 03:17:18 --> btdbFindBy ---> INSERT INTO `start-payment` (`acctpmt_pid`, `account_id`, `user_id`, `amount`, `currency`, `charge_id`, `merchant`, `mode`, `created_at`, `updated_at`) VALUES ('013ea2e9553bc199', 4, '5', '49', 'USD', 'ch_3RYfA4LtUaKDxQEZ1LJ9s2CO', 'Stripe', 'test', '2025-06-11 03:17:18', '2025-06-11 03:17:18')
DEBUG - 2025-06-11 03:17:18 --> btdbUpdate ---> UPDATE `start-user` SET `orig_email` = '<EMAIL>', `is_fraud` = '', `status` = 'active', `updated_at` = '2025-06-11 03:17:18'
WHERE `start-user`.`user_id` IN ('5')
DEBUG - 2025-06-11 03:17:18 --> btdbFindBy ---> SELECT `start-plan`.*, `start-account`.*, DATE_FORMAT(resumed_at , "%M %e, %Y") as "resumed_at_formatted", `start-user`.`mode`, case when DATE_ADD(end_date, INTERVAL 1 DAY) < now() then 'yes' else 'no' end as 'expired'
FROM `start-account`
JOIN `start-user` ON `start-user`.`user_id` = `start-account`.`user_id`
JOIN `start-plan` ON `start-plan`.`plan_id` = `start-account`.`plan_id`
WHERE `start-account`.`user_id` = '5'
ORDER BY `start-account`.`end_date` DESC
WARNING - 2025-06-11 03:17:18 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 113.
 1 APPPATH\Helpers\btflag_helper.php(113): setcookie('pricing', null, **********, '', '')
 2 APPPATH\Controllers\Api.php(5487): btflag_remove('pricing')
 3 APPPATH\Controllers\Api.php(2261): App\Controllers\Api->doAfterPayment([...])
 4 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->executeStripeDirectLink('f6ce7c54e2a4eeca798190ee06d4ab7b')
 5 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 6 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 7 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 8 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-11 03:17:18 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 114.
 1 APPPATH\Helpers\btflag_helper.php(114): setcookie('pricing', null, **********, '/', '')
 2 APPPATH\Controllers\Api.php(5487): btflag_remove('pricing')
 3 APPPATH\Controllers\Api.php(2261): App\Controllers\Api->doAfterPayment([...])
 4 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->executeStripeDirectLink('f6ce7c54e2a4eeca798190ee06d4ab7b')
 5 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 6 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 7 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 8 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-11 03:17:18 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 113.
 1 APPPATH\Helpers\btflag_helper.php(113): setcookie('daily', null, **********, '', '')
 2 APPPATH\Controllers\Api.php(5495): btflag_remove('daily')
 3 APPPATH\Controllers\Api.php(2261): App\Controllers\Api->doAfterPayment([...])
 4 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->executeStripeDirectLink('f6ce7c54e2a4eeca798190ee06d4ab7b')
 5 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 6 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 7 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 8 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-11 03:17:18 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 114.
 1 APPPATH\Helpers\btflag_helper.php(114): setcookie('daily', null, **********, '/', '')
 2 APPPATH\Controllers\Api.php(5495): btflag_remove('daily')
 3 APPPATH\Controllers\Api.php(2261): App\Controllers\Api->doAfterPayment([...])
 4 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->executeStripeDirectLink('f6ce7c54e2a4eeca798190ee06d4ab7b')
 5 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 6 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 7 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 8 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
INFO - 2025-06-11 03:17:18 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
WARNING - 2025-06-11 03:17:18 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 113.
 1 APPPATH\Helpers\btflag_helper.php(113): setcookie('pricing', null, **********, '', '')
 2 APPPATH\Controllers\FlowPages\ThankYou.php(68): btflag_remove('pricing')
 3 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\FlowPages\ThankYou->index()
 4 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\FlowPages\ThankYou))
 5 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 6 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 7 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-11 03:17:18 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 114.
 1 APPPATH\Helpers\btflag_helper.php(114): setcookie('pricing', null, **********, '/', '')
 2 APPPATH\Controllers\FlowPages\ThankYou.php(68): btflag_remove('pricing')
 3 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\FlowPages\ThankYou->index()
 4 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\FlowPages\ThankYou))
 5 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 6 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 7 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-11 03:17:18 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 113.
 1 APPPATH\Helpers\btflag_helper.php(113): setcookie('pricing', null, **********, '', '.ai-pro.org')
 2 APPPATH\Controllers\FlowPages\ThankYou.php(69): btflag_remove('pricing', null, [...])
 3 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\FlowPages\ThankYou->index()
 4 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\FlowPages\ThankYou))
 5 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 6 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 7 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-11 03:17:18 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 114.
 1 APPPATH\Helpers\btflag_helper.php(114): setcookie('pricing', null, **********, '/', '.ai-pro.org')
 2 APPPATH\Controllers\FlowPages\ThankYou.php(69): btflag_remove('pricing', null, [...])
 3 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\FlowPages\ThankYou->index()
 4 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\FlowPages\ThankYou))
 5 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 6 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 7 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-06-11 03:17:19 --> REQ ----------->$2y$09$eksRuVXOXUgoEpP7NyQ67u/U.
DEBUG - 2025-06-11 03:17:19 --> btdbFindBy ---> 
INFO - 2025-06-11 03:17:19 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 03:17:19 --> REQ ----------->$2y$09$eksRuVXOXUgoEpP7NyQ67u/U.
INFO - 2025-06-11 03:17:19 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 03:19:12 --> REQ ----------->$2y$09$eksRuVXOXUgoEpP7NyQ67u/U.
INFO - 2025-06-11 03:19:12 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2025-06-11 03:19:12 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 03:19:12 --> btdbFindBy ---> 
DEBUG - 2025-06-11 03:19:12 --> btdbFindBy ---> SELECT `start-plan`.*, `start-account`.*, DATE_FORMAT(resumed_at , "%M %e, %Y") as "resumed_at_formatted", `start-user`.`mode`, case when DATE_ADD(end_date, INTERVAL 1 DAY) < now() then 'yes' else 'no' end as 'expired'
FROM `start-account`
JOIN `start-user` ON `start-user`.`user_id` = `start-account`.`user_id`
JOIN `start-plan` ON `start-plan`.`plan_id` = `start-account`.`plan_id`
WHERE `start-account`.`user_id` = '5'
ORDER BY `start-account`.`end_date` DESC
DEBUG - 2025-06-11 03:19:12 --> btdbFindBy ---> SELECT *
FROM `start-plan`
WHERE `plan_id` = '198'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-11 03:19:13 --> REQ ----------->$2y$09$eksRuVXOXUgoEpP7NyQ67u/U.
INFO - 2025-06-11 03:19:13 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 03:19:13 --> REQ ----------->
INFO - 2025-06-11 03:19:13 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 03:19:14 --> REQ ----------->
DEBUG - 2025-06-11 03:19:15 --> REQ ----------->$2y$09$eksRuVXOXUgoEpP7NyQ67u/U.
DEBUG - 2025-06-11 03:19:15 --> btdbFindBy ---> 
INFO - 2025-06-11 03:19:15 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2025-06-11 03:19:15 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 03:19:15 --> btdbFindBy ---> 
DEBUG - 2025-06-11 03:19:15 --> btdbFindBy ---> SELECT `start-plan`.*, `start-account`.*, DATE_FORMAT(resumed_at , "%M %e, %Y") as "resumed_at_formatted", `start-user`.`mode`, case when DATE_ADD(end_date, INTERVAL 1 DAY) < now() then 'yes' else 'no' end as 'expired'
FROM `start-account`
JOIN `start-user` ON `start-user`.`user_id` = `start-account`.`user_id`
JOIN `start-plan` ON `start-plan`.`plan_id` = `start-account`.`plan_id`
WHERE `start-account`.`user_id` = '5'
ORDER BY `start-account`.`end_date` DESC
DEBUG - 2025-06-11 03:19:15 --> REQ ----------->$2y$09$eksRuVXOXUgoEpP7NyQ67u/U.
INFO - 2025-06-11 03:19:15 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 03:19:15 --> REQ ----------->
INFO - 2025-06-11 03:19:15 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 03:19:17 --> REQ ----------->$2y$09$eksRuVXOXUgoEpP7NyQ67u/U.
DEBUG - 2025-06-11 03:19:17 --> btdbFindBy ---> 
INFO - 2025-06-11 03:19:17 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 03:19:17 --> REQ ----------->$2y$09$eksRuVXOXUgoEpP7NyQ67u/U.
DEBUG - 2025-06-11 03:19:17 --> btdbFindBy ---> 
INFO - 2025-06-11 03:19:17 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 03:19:18 --> REQ ----------->$2y$09$eksRuVXOXUgoEpP7NyQ67u/U.
INFO - 2025-06-11 03:19:18 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 03:19:18 --> REQ ----------->$2y$09$eksRuVXOXUgoEpP7NyQ67u/U.
INFO - 2025-06-11 03:19:18 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 03:19:44 --> REQ ----------->$2y$09$eksRuVXOXUgoEpP7NyQ67u/U.
INFO - 2025-06-11 03:19:44 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 03:19:44 --> REQ ----------->
INFO - 2025-06-11 03:19:44 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 03:20:32 --> REQ ----------->$2y$09$eksRuVXOXUgoEpP7NyQ67u/U.
INFO - 2025-06-11 03:20:32 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 03:20:32 --> REQ ----------->
INFO - 2025-06-11 03:20:32 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2025-06-11 03:20:34 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 03:20:34 --> btdbFindBy ---> 
DEBUG - 2025-06-11 03:20:35 --> REQ ----------->
INFO - 2025-06-11 03:20:35 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 03:20:35 --> REQ ----------->
INFO - 2025-06-11 03:20:35 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 03:20:42 --> REQ ----------->
INFO - 2025-06-11 03:20:42 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 03:20:43 --> REQ ----------->
INFO - 2025-06-11 03:20:43 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2025-06-11 03:20:44 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 03:20:45 --> REQ ----------->119
DEBUG - 2025-06-11 03:20:45 --> REQ ----------->
INFO - 2025-06-11 03:20:45 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 03:20:52 --> REQ ----------->
INFO - 2025-06-11 03:20:52 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 03:20:52 --> REQ ----------->
INFO - 2025-06-11 03:20:52 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 03:20:52 --> REQ ----------->
INFO - 2025-06-11 03:20:52 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 03:20:53 --> REQ ----------->
INFO - 2025-06-11 03:20:53 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2025-06-11 03:20:54 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 03:20:54 --> REQ ----------->71
DEBUG - 2025-06-11 03:20:54 --> REQ ----------->
INFO - 2025-06-11 03:20:54 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2025-06-11 03:20:56 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 03:20:56 --> btdbFindBy ---> 
INFO - 2025-06-11 03:20:56 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 03:20:56 --> REQ ----------->
INFO - 2025-06-11 03:20:56 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 03:21:01 --> REQ ----------->testuser061125_05@mailinator.com123123123123
DEBUG - 2025-06-11 03:21:01 --> testuser061125_05@mailinator.com123123123123
DEBUG - 2025-06-11 03:21:01 --> btdbFindBy ---> 
DEBUG - 2025-06-11 03:21:01 --> btdbUpdate ---> INSERT INTO `start-user` (`user_pid`, `email`, `mode`, `password`, `first_name`, `last_name`, `login_token`, `ip_address`, `country`, `flags`, `created_at`, `updated_at`) VALUES ('c05490855be96b4b', '<EMAIL>', 'test', '$P$B4XKg0UcSIGWqC4Ar5unhA40823b4C/', '', '', '$2y$09$iouj2iD7t23XAZvB5.7tOuHipU1mzN6q/oVpRP8/dWoXTKC2ugVqi', '::1', '', '{\"aiwp_logged_in\":null,\"mode\":\"test\",\"ppg\":\"71\",\"pp_ctaclr\":\"1559ED\",\"pmt\":\"paydl\",\"vpay\":\"\",\"navmenu\":\"show\",\"splash\":\"off\",\"ailplogo\":\"off\",\"emailid\":\"\",\"theme_flag_name\":\"kt8typtb\",\"theme_flag_value\":\"arcana\"}', '2025-06-11 03:21:01', '2025-06-11 03:21:01')
DEBUG - 2025-06-11 03:21:01 --> btdbFindBy ---> INSERT INTO `start-user` (`user_pid`, `email`, `mode`, `password`, `first_name`, `last_name`, `login_token`, `ip_address`, `country`, `flags`, `created_at`, `updated_at`) VALUES ('c05490855be96b4b', '<EMAIL>', 'test', '$P$B4XKg0UcSIGWqC4Ar5unhA40823b4C/', '', '', '$2y$09$iouj2iD7t23XAZvB5.7tOuHipU1mzN6q/oVpRP8/dWoXTKC2ugVqi', '::1', '', '{\"aiwp_logged_in\":null,\"mode\":\"test\",\"ppg\":\"71\",\"pp_ctaclr\":\"1559ED\",\"pmt\":\"paydl\",\"vpay\":\"\",\"navmenu\":\"show\",\"splash\":\"off\",\"ailplogo\":\"off\",\"emailid\":\"\",\"theme_flag_name\":\"kt8typtb\",\"theme_flag_value\":\"arcana\"}', '2025-06-11 03:21:01', '2025-06-11 03:21:01')
INFO - 2025-06-11 03:21:01 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 03:21:01 --> btdbFindBy ---> SELECT `start-plan`.*, `start-account`.*, DATE_FORMAT(resumed_at , "%M %e, %Y") as "resumed_at_formatted", `start-user`.`mode`, case when DATE_ADD(end_date, INTERVAL 1 DAY) < now() then 'yes' else 'no' end as 'expired'
FROM `start-account`
JOIN `start-user` ON `start-user`.`user_id` = `start-account`.`user_id`
JOIN `start-plan` ON `start-plan`.`plan_id` = `start-account`.`plan_id`
WHERE `start-account`.`user_id` = '6'
ORDER BY `start-account`.`end_date` DESC
DEBUG - 2025-06-11 03:21:01 --> btdbUpdate ---> INSERT INTO `start-user_app` (`email`, `userapp_pid`, `created_at`, `updated_at`) VALUES ('<EMAIL>', 'ad9d680e7392ec43f0e98fa5c7d9ed7a', '2025-06-11 03:21:01', '2025-06-11 03:21:01')
DEBUG - 2025-06-11 03:21:01 --> btdbFindBy ---> INSERT INTO `start-user_app` (`email`, `userapp_pid`, `created_at`, `updated_at`) VALUES ('<EMAIL>', 'ad9d680e7392ec43f0e98fa5c7d9ed7a', '2025-06-11 03:21:01', '2025-06-11 03:21:01')
INFO - 2025-06-11 03:21:01 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 03:21:01 --> btdbFindBy ---> 
DEBUG - 2025-06-11 03:21:01 --> btdbFindBy ---> SELECT `start-plan`.*, `start-account`.*, DATE_FORMAT(resumed_at , "%M %e, %Y") as "resumed_at_formatted", `start-user`.`mode`, case when DATE_ADD(end_date, INTERVAL 1 DAY) < now() then 'yes' else 'no' end as 'expired'
FROM `start-account`
JOIN `start-user` ON `start-user`.`user_id` = `start-account`.`user_id`
JOIN `start-plan` ON `start-plan`.`plan_id` = `start-account`.`plan_id`
WHERE `start-account`.`user_id` = '6'
ORDER BY `start-account`.`end_date` DESC
DEBUG - 2025-06-11 03:21:02 --> REQ ----------->105
DEBUG - 2025-06-11 03:21:02 --> REQ ----------->$2y$09$iouj2iD7t23XAZvB5.7tOuHip
INFO - 2025-06-11 03:21:02 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 03:21:09 --> REQ ----------->$2y$09$iouj2iD7t23XAZvB5.7tOuHipTestUser4111-1111-1111-111112203412310510
DEBUG - 2025-06-11 03:21:09 --> btdbFindBy ---> 
INFO - 2025-06-11 03:21:09 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 03:21:09 --> btdbFindBy ---> SELECT case when plan_type='promax' then 'pro max' else plan_type end as 'plan_type_display', `start-plan`.*
FROM `start-plan`
WHERE `plan_id` = '105'
DEBUG - 2025-06-11 03:21:09 --> btdbFindBy ---> 
WARNING - 2025-06-11 03:21:09 --> [DEPRECATED] Optional parameter $_api_key declared before required parameter $_mode is implicitly treated as a required parameter in VENDORPATH\btapp\btcore\src\Payment\Recurly.php on line 16.
 1 SYSTEMPATH\Autoloader\Autoloader.php(314): include_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\vendor\\codeigniter4\\framework\\system\\Debug\\Exceptions.php')
 2 SYSTEMPATH\Autoloader\Autoloader.php(250): CodeIgniter\Autoloader\Autoloader->includeFile('C:\\laragon\\www\\app.ai-pro.org\\btapp\\vendor\\composer/../btapp/btcore/src/Payment/Recurly.php')
 3 APPPATH\Controllers\Api.php(2447): CodeIgniter\Autoloader\Autoloader->loadClassmap('BTCore\\Payment\\Recurly')
 4 APPPATH\Controllers\Api.php(162): App\Controllers\Api->createSubscription()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('create-subscription')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-06-11 03:21:16 --> btdbUpdate ---> INSERT INTO `start-account` (`account_pid`, `user_id`, `plan_id`, `cc_number`, `cc_number_encrypted`, `merchant`, `merchant_customer_id`, `merchant_subscription_id`, `trial_end`, `start_date`, `end_date`, `cancelled_at`, `members`, `max_tokens`, `created_at`, `updated_at`) VALUES ('e703e9263f0862c6', '6', '105', '411111****1111', '68bfb396f35af3876fc509665b3dc23a0930aab1', 'Recurly', 'app-e703e9263f0862c6-6', '78cbccd601784d48bc64ad4c3089f503', '2025-07-11 03:21:14', '2025-06-11 03:21:14', '2025-07-11 03:21:14', 0, 9, '740000', '2025-06-11 03:21:16', '2025-06-11 03:21:16')
DEBUG - 2025-06-11 03:21:16 --> btdbUpdate ---> INSERT INTO `start-payment` (`acctpmt_pid`, `account_id`, `user_id`, `amount`, `currency`, `charge_id`, `merchant`, `cc_number`, `mode`, `created_at`, `updated_at`) VALUES ('84a0106586ecfb2f', 5, '6', '160', 'USD', '291346', 'Recurly', '1111', 'test', '2025-06-11 03:21:16', '2025-06-11 03:21:16')
DEBUG - 2025-06-11 03:21:16 --> btdbFindBy ---> INSERT INTO `start-payment` (`acctpmt_pid`, `account_id`, `user_id`, `amount`, `currency`, `charge_id`, `merchant`, `cc_number`, `mode`, `created_at`, `updated_at`) VALUES ('84a0106586ecfb2f', 5, '6', '160', 'USD', '291346', 'Recurly', '1111', 'test', '2025-06-11 03:21:16', '2025-06-11 03:21:16')
DEBUG - 2025-06-11 03:21:16 --> btdbUpdate ---> UPDATE `start-user` SET `orig_email` = '<EMAIL>', `is_fraud` = '', `status` = 'active', `updated_at` = '2025-06-11 03:21:16'
WHERE `start-user`.`user_id` IN ('6')
DEBUG - 2025-06-11 03:21:16 --> btdbFindBy ---> SELECT `start-plan`.*, `start-account`.*, DATE_FORMAT(resumed_at , "%M %e, %Y") as "resumed_at_formatted", `start-user`.`mode`, case when DATE_ADD(end_date, INTERVAL 1 DAY) < now() then 'yes' else 'no' end as 'expired'
FROM `start-account`
JOIN `start-user` ON `start-user`.`user_id` = `start-account`.`user_id`
JOIN `start-plan` ON `start-plan`.`plan_id` = `start-account`.`plan_id`
WHERE `start-account`.`user_id` = '6'
ORDER BY `start-account`.`end_date` DESC
WARNING - 2025-06-11 03:21:16 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 113.
 1 APPPATH\Helpers\btflag_helper.php(113): setcookie('pricing', null, **********, '', '')
 2 APPPATH\Controllers\Api.php(5487): btflag_remove('pricing')
 3 APPPATH\Controllers\Api.php(2659): App\Controllers\Api->doAfterPayment([...])
 4 APPPATH\Controllers\Api.php(162): App\Controllers\Api->createSubscription()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('create-subscription')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-11 03:21:16 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 114.
 1 APPPATH\Helpers\btflag_helper.php(114): setcookie('pricing', null, **********, '/', '')
 2 APPPATH\Controllers\Api.php(5487): btflag_remove('pricing')
 3 APPPATH\Controllers\Api.php(2659): App\Controllers\Api->doAfterPayment([...])
 4 APPPATH\Controllers\Api.php(162): App\Controllers\Api->createSubscription()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('create-subscription')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-11 03:21:16 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 113.
 1 APPPATH\Helpers\btflag_helper.php(113): setcookie('daily', null, **********, '', '')
 2 APPPATH\Controllers\Api.php(5495): btflag_remove('daily')
 3 APPPATH\Controllers\Api.php(2659): App\Controllers\Api->doAfterPayment([...])
 4 APPPATH\Controllers\Api.php(162): App\Controllers\Api->createSubscription()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('create-subscription')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-11 03:21:16 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 114.
 1 APPPATH\Helpers\btflag_helper.php(114): setcookie('daily', null, **********, '/', '')
 2 APPPATH\Controllers\Api.php(5495): btflag_remove('daily')
 3 APPPATH\Controllers\Api.php(2659): App\Controllers\Api->doAfterPayment([...])
 4 APPPATH\Controllers\Api.php(162): App\Controllers\Api->createSubscription()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('create-subscription')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
INFO - 2025-06-11 03:21:16 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
WARNING - 2025-06-11 03:21:16 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 113.
 1 APPPATH\Helpers\btflag_helper.php(113): setcookie('pricing', null, **********, '', '')
 2 APPPATH\Controllers\FlowPages\ThankYou.php(68): btflag_remove('pricing')
 3 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\FlowPages\ThankYou->index()
 4 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\FlowPages\ThankYou))
 5 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 6 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 7 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-11 03:21:16 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 114.
 1 APPPATH\Helpers\btflag_helper.php(114): setcookie('pricing', null, **********, '/', '')
 2 APPPATH\Controllers\FlowPages\ThankYou.php(68): btflag_remove('pricing')
 3 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\FlowPages\ThankYou->index()
 4 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\FlowPages\ThankYou))
 5 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 6 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 7 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-11 03:21:16 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 113.
 1 APPPATH\Helpers\btflag_helper.php(113): setcookie('pricing', null, **********, '', '.ai-pro.org')
 2 APPPATH\Controllers\FlowPages\ThankYou.php(69): btflag_remove('pricing', null, [...])
 3 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\FlowPages\ThankYou->index()
 4 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\FlowPages\ThankYou))
 5 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 6 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 7 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-11 03:21:16 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 114.
 1 APPPATH\Helpers\btflag_helper.php(114): setcookie('pricing', null, **********, '/', '.ai-pro.org')
 2 APPPATH\Controllers\FlowPages\ThankYou.php(69): btflag_remove('pricing', null, [...])
 3 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\FlowPages\ThankYou->index()
 4 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\FlowPages\ThankYou))
 5 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 6 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 7 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-06-11 03:21:16 --> REQ ----------->$2y$09$iouj2iD7t23XAZvB5.7tOuHip
DEBUG - 2025-06-11 03:21:16 --> btdbFindBy ---> 
INFO - 2025-06-11 03:21:16 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 03:21:17 --> REQ ----------->$2y$09$iouj2iD7t23XAZvB5.7tOuHip
INFO - 2025-06-11 03:21:17 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2025-06-11 03:21:19 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 03:21:19 --> btdbFindBy ---> 
DEBUG - 2025-06-11 03:21:19 --> btdbFindBy ---> SELECT `start-plan`.*, `start-account`.*, DATE_FORMAT(resumed_at , "%M %e, %Y") as "resumed_at_formatted", `start-user`.`mode`, case when DATE_ADD(end_date, INTERVAL 1 DAY) < now() then 'yes' else 'no' end as 'expired'
FROM `start-account`
JOIN `start-user` ON `start-user`.`user_id` = `start-account`.`user_id`
JOIN `start-plan` ON `start-plan`.`plan_id` = `start-account`.`plan_id`
WHERE `start-account`.`user_id` = '6'
ORDER BY `start-account`.`end_date` DESC
DEBUG - 2025-06-11 03:21:19 --> btdbFindBy ---> SELECT *
FROM `start-plan`
WHERE `plan_id` = '105'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-11 03:21:20 --> REQ ----------->$2y$09$iouj2iD7t23XAZvB5.7tOuHip
INFO - 2025-06-11 03:21:20 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 03:21:20 --> REQ ----------->
INFO - 2025-06-11 03:21:20 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 03:21:22 --> REQ ----------->
DEBUG - 2025-06-11 03:21:22 --> REQ ----------->$2y$09$iouj2iD7t23XAZvB5.7tOuHip
DEBUG - 2025-06-11 03:21:22 --> btdbFindBy ---> 
INFO - 2025-06-11 03:21:22 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 03:21:22 --> REQ ----------->$2y$09$iouj2iD7t23XAZvB5.7tOuHip
INFO - 2025-06-11 03:21:22 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 03:21:24 --> REQ ----------->$2y$09$iouj2iD7t23XAZvB5.7tOuHip
INFO - 2025-06-11 03:21:24 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 03:21:25 --> REQ ----------->
INFO - 2025-06-11 03:21:25 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2025-06-11 03:21:26 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 03:21:26 --> btdbFindBy ---> 
DEBUG - 2025-06-11 03:21:26 --> btdbFindBy ---> SELECT `start-plan`.*, `start-account`.*, DATE_FORMAT(resumed_at , "%M %e, %Y") as "resumed_at_formatted", `start-user`.`mode`, case when DATE_ADD(end_date, INTERVAL 1 DAY) < now() then 'yes' else 'no' end as 'expired'
FROM `start-account`
JOIN `start-user` ON `start-user`.`user_id` = `start-account`.`user_id`
JOIN `start-plan` ON `start-plan`.`plan_id` = `start-account`.`plan_id`
WHERE `start-account`.`user_id` = '6'
ORDER BY `start-account`.`end_date` DESC
DEBUG - 2025-06-11 03:21:27 --> REQ ----------->$2y$09$iouj2iD7t23XAZvB5.7tOuHip
INFO - 2025-06-11 03:21:27 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 03:21:27 --> REQ ----------->
INFO - 2025-06-11 03:21:27 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 03:21:29 --> REQ ----------->$2y$09$iouj2iD7t23XAZvB5.7tOuHip
DEBUG - 2025-06-11 03:21:29 --> btdbFindBy ---> 
INFO - 2025-06-11 03:21:29 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 03:21:29 --> REQ ----------->$2y$09$iouj2iD7t23XAZvB5.7tOuHip
DEBUG - 2025-06-11 03:21:29 --> btdbFindBy ---> 
INFO - 2025-06-11 03:21:29 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 03:21:29 --> REQ ----------->$2y$09$iouj2iD7t23XAZvB5.7tOuHip
INFO - 2025-06-11 03:21:29 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 03:21:29 --> REQ ----------->$2y$09$iouj2iD7t23XAZvB5.7tOuHip
INFO - 2025-06-11 03:21:29 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 03:21:29 --> REQ ----------->$2y$09$iouj2iD7t23XAZvB5.7tOuHip
INFO - 2025-06-11 03:21:29 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 03:21:32 --> REQ ----------->$2y$09$iouj2iD7t23XAZvB5.7tOuHip
INFO - 2025-06-11 03:21:32 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 03:21:32 --> REQ ----------->
INFO - 2025-06-11 03:21:32 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 03:21:44 --> REQ ----------->$2y$09$iouj2iD7t23XAZvB5.7tOuHip
INFO - 2025-06-11 03:21:44 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 03:21:44 --> REQ ----------->
INFO - 2025-06-11 03:21:44 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2025-06-11 03:21:46 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 03:21:46 --> btdbFindBy ---> 
DEBUG - 2025-06-11 03:21:46 --> REQ ----------->
INFO - 2025-06-11 03:21:46 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 03:21:47 --> REQ ----------->
INFO - 2025-06-11 03:21:47 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 03:21:58 --> REQ ----------->
INFO - 2025-06-11 03:21:58 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 03:21:59 --> REQ ----------->
INFO - 2025-06-11 03:21:59 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 03:22:04 --> REQ ----------->
INFO - 2025-06-11 03:22:04 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 03:22:04 --> REQ ----------->
INFO - 2025-06-11 03:22:04 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 03:22:04 --> REQ ----------->
INFO - 2025-06-11 03:22:04 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2025-06-11 03:22:07 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 03:22:07 --> REQ ----------->
INFO - 2025-06-11 03:22:07 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 03:22:15 --> REQ ----------->
INFO - 2025-06-11 03:22:15 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 03:22:19 --> REQ ----------->testuser061125_06@mailinator.com123123123123
DEBUG - 2025-06-11 03:22:19 --> testuser061125_06@mailinator.com123123123123
DEBUG - 2025-06-11 03:22:19 --> btdbFindBy ---> 
DEBUG - 2025-06-11 03:22:20 --> btdbUpdate ---> INSERT INTO `start-user` (`user_pid`, `email`, `mode`, `password`, `first_name`, `last_name`, `login_token`, `ip_address`, `country`, `flags`, `created_at`, `updated_at`) VALUES ('de2bc7f1e8e389fe', '<EMAIL>', 'test', '$P$BcTuKhRMx5JwBJKTRbLS.hFqoc5ege0', '', '', '$2y$09$fzau3sSMcagssh9x900cy.NYMT5kKH0xPmAp04Papv4rjuDMeNPoS', '::1', '', '{\"aiwp_logged_in\":null,\"mode\":\"test\",\"ppg\":\"118\",\"pp_ctaclr\":\"1559ED\",\"pmt\":\"rec\",\"vpay\":\"mcWiDilmgQ\",\"navmenu\":\"show\",\"splash\":\"off\",\"ailplogo\":\"off\",\"emailid\":\"\",\"theme_flag_name\":\"kt8typtb\",\"theme_flag_value\":\"arcana\"}', '2025-06-11 03:22:20', '2025-06-11 03:22:20')
DEBUG - 2025-06-11 03:22:20 --> btdbFindBy ---> INSERT INTO `start-user` (`user_pid`, `email`, `mode`, `password`, `first_name`, `last_name`, `login_token`, `ip_address`, `country`, `flags`, `created_at`, `updated_at`) VALUES ('de2bc7f1e8e389fe', '<EMAIL>', 'test', '$P$BcTuKhRMx5JwBJKTRbLS.hFqoc5ege0', '', '', '$2y$09$fzau3sSMcagssh9x900cy.NYMT5kKH0xPmAp04Papv4rjuDMeNPoS', '::1', '', '{\"aiwp_logged_in\":null,\"mode\":\"test\",\"ppg\":\"118\",\"pp_ctaclr\":\"1559ED\",\"pmt\":\"rec\",\"vpay\":\"mcWiDilmgQ\",\"navmenu\":\"show\",\"splash\":\"off\",\"ailplogo\":\"off\",\"emailid\":\"\",\"theme_flag_name\":\"kt8typtb\",\"theme_flag_value\":\"arcana\"}', '2025-06-11 03:22:20', '2025-06-11 03:22:20')
INFO - 2025-06-11 03:22:20 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 03:22:20 --> btdbFindBy ---> SELECT `start-plan`.*, `start-account`.*, DATE_FORMAT(resumed_at , "%M %e, %Y") as "resumed_at_formatted", `start-user`.`mode`, case when DATE_ADD(end_date, INTERVAL 1 DAY) < now() then 'yes' else 'no' end as 'expired'
FROM `start-account`
JOIN `start-user` ON `start-user`.`user_id` = `start-account`.`user_id`
JOIN `start-plan` ON `start-plan`.`plan_id` = `start-account`.`plan_id`
WHERE `start-account`.`user_id` = '7'
ORDER BY `start-account`.`end_date` DESC
DEBUG - 2025-06-11 03:22:20 --> btdbUpdate ---> INSERT INTO `start-user_app` (`email`, `userapp_pid`, `created_at`, `updated_at`) VALUES ('<EMAIL>', '43408df4dda152d87987abbb548c1a12', '2025-06-11 03:22:20', '2025-06-11 03:22:20')
DEBUG - 2025-06-11 03:22:20 --> btdbFindBy ---> INSERT INTO `start-user_app` (`email`, `userapp_pid`, `created_at`, `updated_at`) VALUES ('<EMAIL>', '43408df4dda152d87987abbb548c1a12', '2025-06-11 03:22:20', '2025-06-11 03:22:20')
INFO - 2025-06-11 03:22:20 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 03:22:20 --> btdbFindBy ---> 
DEBUG - 2025-06-11 03:22:20 --> btdbFindBy ---> SELECT `start-plan`.*, `start-account`.*, DATE_FORMAT(resumed_at , "%M %e, %Y") as "resumed_at_formatted", `start-user`.`mode`, case when DATE_ADD(end_date, INTERVAL 1 DAY) < now() then 'yes' else 'no' end as 'expired'
FROM `start-account`
JOIN `start-user` ON `start-user`.`user_id` = `start-account`.`user_id`
JOIN `start-plan` ON `start-plan`.`plan_id` = `start-account`.`plan_id`
WHERE `start-account`.`user_id` = '7'
ORDER BY `start-account`.`end_date` DESC
WARNING - 2025-06-11 03:22:20 --> [DEPRECATED] explode(): Passing null to parameter #2 ($string) of type string is deprecated in APPPATH\Models\BTModel.php on line 52.
 1 APPPATH\Models\BTModel.php(52): explode(',', null)
 2 APPPATH\Helpers\btdb_helper.php(180): App\Models\BTModel->findBy([...], null)
 3 APPPATH\Controllers\FlowPages\Payment.php(64): btdbFindBy('PlanModel', 'plan_id', null)
 4 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\FlowPages\Payment->index('mcWiDilmgQ')
 5 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\FlowPages\Payment))
 6 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 7 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 8 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
INFO - 2025-06-11 03:22:20 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 03:22:20 --> REQ ----------->118
INFO - 2025-06-11 03:22:58 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 03:22:58 --> btdbFindBy ---> 
DEBUG - 2025-06-11 03:22:58 --> btdbFindBy ---> SELECT `start-plan`.*, `start-account`.*, DATE_FORMAT(resumed_at , "%M %e, %Y") as "resumed_at_formatted", `start-user`.`mode`, case when DATE_ADD(end_date, INTERVAL 1 DAY) < now() then 'yes' else 'no' end as 'expired'
FROM `start-account`
JOIN `start-user` ON `start-user`.`user_id` = `start-account`.`user_id`
JOIN `start-plan` ON `start-plan`.`plan_id` = `start-account`.`plan_id`
WHERE `start-account`.`user_id` = '7'
ORDER BY `start-account`.`end_date` DESC
DEBUG - 2025-06-11 03:22:59 --> REQ ----------->185
DEBUG - 2025-06-11 03:22:59 --> REQ ----------->$2y$09$fzau3sSMcagssh9x900cy.NYM
INFO - 2025-06-11 03:22:59 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 03:23:16 --> REQ ----------->$2y$09$fzau3sSMcagssh9x900cy.NYM
INFO - 2025-06-11 03:23:16 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 03:23:19 --> REQ ----------->$2y$09$fzau3sSMcagssh9x900cy.NYMTestUser4111-1111-1111-*****************
DEBUG - 2025-06-11 03:23:19 --> btdbFindBy ---> 
INFO - 2025-06-11 03:23:19 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 03:23:19 --> btdbFindBy ---> SELECT case when plan_type='promax' then 'pro max' else plan_type end as 'plan_type_display', `start-plan`.*
FROM `start-plan`
WHERE `plan_id` = '185'
DEBUG - 2025-06-11 03:23:20 --> btdbFindBy ---> 
WARNING - 2025-06-11 03:23:20 --> [DEPRECATED] Optional parameter $_api_key declared before required parameter $_mode is implicitly treated as a required parameter in VENDORPATH\btapp\btcore\src\Payment\Recurly.php on line 16.
 1 SYSTEMPATH\Autoloader\Autoloader.php(314): include_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\vendor\\codeigniter4\\framework\\system\\Debug\\Exceptions.php')
 2 SYSTEMPATH\Autoloader\Autoloader.php(250): CodeIgniter\Autoloader\Autoloader->includeFile('C:\\laragon\\www\\app.ai-pro.org\\btapp\\vendor\\composer/../btapp/btcore/src/Payment/Recurly.php')
 3 APPPATH\Controllers\Api.php(2447): CodeIgniter\Autoloader\Autoloader->loadClassmap('BTCore\\Payment\\Recurly')
 4 APPPATH\Controllers\Api.php(162): App\Controllers\Api->createSubscription()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('create-subscription')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-06-11 03:23:25 --> btdbUpdate ---> INSERT INTO `start-account` (`account_pid`, `user_id`, `plan_id`, `cc_number`, `cc_number_encrypted`, `merchant`, `merchant_customer_id`, `merchant_subscription_id`, `trial_end`, `start_date`, `end_date`, `cancelled_at`, `members`, `max_tokens`, `created_at`, `updated_at`) VALUES ('cbbdb8e6e0219a66', '7', '185', '411111****1111', '68bfb396f35af3876fc509665b3dc23a0930aab1', 'Recurly', 'app-cbbdb8e6e0219a66-7', '78cbced2f4f429a77b339649f493a391', '2025-07-11 03:23:24', '2025-06-11 03:23:24', '2025-07-11 03:23:24', 0, 2, '********', '2025-06-11 03:23:25', '2025-06-11 03:23:25')
DEBUG - 2025-06-11 03:23:25 --> btdbUpdate ---> INSERT INTO `start-payment` (`acctpmt_pid`, `account_id`, `user_id`, `amount`, `currency`, `charge_id`, `merchant`, `cc_number`, `mode`, `created_at`, `updated_at`) VALUES ('2e8d0aa94f239a96', 6, '7', '139.95', 'USD', '291348', 'Recurly', '1111', 'test', '2025-06-11 03:23:25', '2025-06-11 03:23:25')
DEBUG - 2025-06-11 03:23:25 --> btdbFindBy ---> INSERT INTO `start-payment` (`acctpmt_pid`, `account_id`, `user_id`, `amount`, `currency`, `charge_id`, `merchant`, `cc_number`, `mode`, `created_at`, `updated_at`) VALUES ('2e8d0aa94f239a96', 6, '7', '139.95', 'USD', '291348', 'Recurly', '1111', 'test', '2025-06-11 03:23:25', '2025-06-11 03:23:25')
DEBUG - 2025-06-11 03:23:25 --> btdbUpdate ---> UPDATE `start-user` SET `orig_email` = '<EMAIL>', `is_fraud` = '', `status` = 'active', `updated_at` = '2025-06-11 03:23:25'
WHERE `start-user`.`user_id` IN ('7')
DEBUG - 2025-06-11 03:23:25 --> btdbFindBy ---> SELECT `start-plan`.*, `start-account`.*, DATE_FORMAT(resumed_at , "%M %e, %Y") as "resumed_at_formatted", `start-user`.`mode`, case when DATE_ADD(end_date, INTERVAL 1 DAY) < now() then 'yes' else 'no' end as 'expired'
FROM `start-account`
JOIN `start-user` ON `start-user`.`user_id` = `start-account`.`user_id`
JOIN `start-plan` ON `start-plan`.`plan_id` = `start-account`.`plan_id`
WHERE `start-account`.`user_id` = '7'
ORDER BY `start-account`.`end_date` DESC
WARNING - 2025-06-11 03:23:25 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 113.
 1 APPPATH\Helpers\btflag_helper.php(113): setcookie('pricing', null, **********, '', '')
 2 APPPATH\Controllers\Api.php(5487): btflag_remove('pricing')
 3 APPPATH\Controllers\Api.php(2659): App\Controllers\Api->doAfterPayment([...])
 4 APPPATH\Controllers\Api.php(162): App\Controllers\Api->createSubscription()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('create-subscription')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-11 03:23:25 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 114.
 1 APPPATH\Helpers\btflag_helper.php(114): setcookie('pricing', null, **********, '/', '')
 2 APPPATH\Controllers\Api.php(5487): btflag_remove('pricing')
 3 APPPATH\Controllers\Api.php(2659): App\Controllers\Api->doAfterPayment([...])
 4 APPPATH\Controllers\Api.php(162): App\Controllers\Api->createSubscription()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('create-subscription')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-11 03:23:25 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 113.
 1 APPPATH\Helpers\btflag_helper.php(113): setcookie('daily', null, **********, '', '')
 2 APPPATH\Controllers\Api.php(5495): btflag_remove('daily')
 3 APPPATH\Controllers\Api.php(2659): App\Controllers\Api->doAfterPayment([...])
 4 APPPATH\Controllers\Api.php(162): App\Controllers\Api->createSubscription()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('create-subscription')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-11 03:23:25 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 114.
 1 APPPATH\Helpers\btflag_helper.php(114): setcookie('daily', null, **********, '/', '')
 2 APPPATH\Controllers\Api.php(5495): btflag_remove('daily')
 3 APPPATH\Controllers\Api.php(2659): App\Controllers\Api->doAfterPayment([...])
 4 APPPATH\Controllers\Api.php(162): App\Controllers\Api->createSubscription()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('create-subscription')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
INFO - 2025-06-11 03:23:26 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
WARNING - 2025-06-11 03:23:26 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 113.
 1 APPPATH\Helpers\btflag_helper.php(113): setcookie('pricing', null, 1749608606, '', '')
 2 APPPATH\Controllers\FlowPages\ThankYou.php(68): btflag_remove('pricing')
 3 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\FlowPages\ThankYou->index()
 4 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\FlowPages\ThankYou))
 5 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 6 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 7 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-11 03:23:26 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 114.
 1 APPPATH\Helpers\btflag_helper.php(114): setcookie('pricing', null, 1749608606, '/', '')
 2 APPPATH\Controllers\FlowPages\ThankYou.php(68): btflag_remove('pricing')
 3 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\FlowPages\ThankYou->index()
 4 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\FlowPages\ThankYou))
 5 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 6 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 7 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-11 03:23:26 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 113.
 1 APPPATH\Helpers\btflag_helper.php(113): setcookie('pricing', null, 1749608606, '', '.ai-pro.org')
 2 APPPATH\Controllers\FlowPages\ThankYou.php(69): btflag_remove('pricing', null, [...])
 3 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\FlowPages\ThankYou->index()
 4 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\FlowPages\ThankYou))
 5 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 6 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 7 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-11 03:23:26 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 114.
 1 APPPATH\Helpers\btflag_helper.php(114): setcookie('pricing', null, 1749608606, '/', '.ai-pro.org')
 2 APPPATH\Controllers\FlowPages\ThankYou.php(69): btflag_remove('pricing', null, [...])
 3 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\FlowPages\ThankYou->index()
 4 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\FlowPages\ThankYou))
 5 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 6 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 7 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-06-11 03:23:26 --> REQ ----------->$2y$09$fzau3sSMcagssh9x900cy.NYM
DEBUG - 2025-06-11 03:23:26 --> btdbFindBy ---> 
INFO - 2025-06-11 03:23:26 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 03:23:26 --> REQ ----------->$2y$09$fzau3sSMcagssh9x900cy.NYM
INFO - 2025-06-11 03:23:26 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 03:23:30 --> REQ ----------->$2y$09$fzau3sSMcagssh9x900cy.NYM
INFO - 2025-06-11 03:23:30 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2025-06-11 03:23:30 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 03:23:30 --> btdbFindBy ---> 
DEBUG - 2025-06-11 03:23:30 --> btdbFindBy ---> SELECT `start-plan`.*, `start-account`.*, DATE_FORMAT(resumed_at , "%M %e, %Y") as "resumed_at_formatted", `start-user`.`mode`, case when DATE_ADD(end_date, INTERVAL 1 DAY) < now() then 'yes' else 'no' end as 'expired'
FROM `start-account`
JOIN `start-user` ON `start-user`.`user_id` = `start-account`.`user_id`
JOIN `start-plan` ON `start-plan`.`plan_id` = `start-account`.`plan_id`
WHERE `start-account`.`user_id` = '7'
ORDER BY `start-account`.`end_date` DESC
DEBUG - 2025-06-11 03:23:30 --> btdbFindBy ---> SELECT *
FROM `start-plan`
WHERE `plan_id` = '185'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-11 03:23:31 --> REQ ----------->$2y$09$fzau3sSMcagssh9x900cy.NYM
INFO - 2025-06-11 03:23:31 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 03:23:31 --> REQ ----------->
INFO - 2025-06-11 03:23:31 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 03:23:34 --> REQ ----------->
INFO - 2025-06-11 03:23:34 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 03:23:34 --> btdbFindBy ---> 
DEBUG - 2025-06-11 03:23:34 --> btdbFindBy ---> SELECT `start-plan`.*, `start-account`.*, DATE_FORMAT(resumed_at , "%M %e, %Y") as "resumed_at_formatted", `start-user`.`mode`, case when DATE_ADD(end_date, INTERVAL 1 DAY) < now() then 'yes' else 'no' end as 'expired'
FROM `start-account`
JOIN `start-user` ON `start-user`.`user_id` = `start-account`.`user_id`
JOIN `start-plan` ON `start-plan`.`plan_id` = `start-account`.`plan_id`
WHERE `start-account`.`user_id` = '7'
ORDER BY `start-account`.`end_date` DESC
DEBUG - 2025-06-11 03:23:34 --> REQ ----------->$2y$09$fzau3sSMcagssh9x900cy.NYM
DEBUG - 2025-06-11 03:23:34 --> btdbFindBy ---> 
INFO - 2025-06-11 03:23:34 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 03:23:35 --> REQ ----------->$2y$09$fzau3sSMcagssh9x900cy.NYM
INFO - 2025-06-11 03:23:35 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 03:23:35 --> REQ ----------->
INFO - 2025-06-11 03:23:35 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 03:23:37 --> REQ ----------->$2y$09$fzau3sSMcagssh9x900cy.NYM
DEBUG - 2025-06-11 03:23:37 --> btdbFindBy ---> 
INFO - 2025-06-11 03:23:37 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 03:23:37 --> REQ ----------->$2y$09$fzau3sSMcagssh9x900cy.NYM
DEBUG - 2025-06-11 03:23:37 --> btdbFindBy ---> 
INFO - 2025-06-11 03:23:37 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 03:23:37 --> REQ ----------->$2y$09$fzau3sSMcagssh9x900cy.NYM
INFO - 2025-06-11 03:23:37 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 03:23:37 --> REQ ----------->$2y$09$fzau3sSMcagssh9x900cy.NYM
INFO - 2025-06-11 03:23:37 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 03:23:37 --> REQ ----------->$2y$09$fzau3sSMcagssh9x900cy.NYM
INFO - 2025-06-11 03:23:37 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 03:23:49 --> REQ ----------->$2y$09$fzau3sSMcagssh9x900cy.NYM
INFO - 2025-06-11 03:23:49 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 03:23:49 --> REQ ----------->
INFO - 2025-06-11 03:23:49 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2025-06-11 03:23:52 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 03:23:52 --> btdbFindBy ---> 
DEBUG - 2025-06-11 03:23:52 --> REQ ----------->
INFO - 2025-06-11 03:23:52 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 03:23:52 --> REQ ----------->
INFO - 2025-06-11 03:23:52 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 03:24:22 --> REQ ----------->
INFO - 2025-06-11 03:24:22 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 03:24:26 --> REQ ----------->testuser061125_07@mailinator.com123123
DEBUG - 2025-06-11 03:24:26 --> btdbFindBy ---> 
INFO - 2025-06-11 03:24:29 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 03:24:29 --> REQ ----------->
INFO - 2025-06-11 03:24:29 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 03:24:36 --> REQ ----------->
INFO - 2025-06-11 03:24:36 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 03:24:37 --> REQ ----------->
INFO - 2025-06-11 03:24:37 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 03:24:37 --> REQ ----------->
INFO - 2025-06-11 03:24:37 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2025-06-11 03:24:39 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 03:24:39 --> REQ ----------->
INFO - 2025-06-11 03:24:39 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 03:24:43 --> REQ ----------->testuser061125_07@mailinator.com123123123123
DEBUG - 2025-06-11 03:24:43 --> testuser061125_07@mailinator.com123123123123
DEBUG - 2025-06-11 03:24:43 --> btdbFindBy ---> 
DEBUG - 2025-06-11 03:24:44 --> btdbUpdate ---> INSERT INTO `start-user` (`user_pid`, `email`, `mode`, `password`, `first_name`, `last_name`, `login_token`, `ip_address`, `country`, `flags`, `created_at`, `updated_at`) VALUES ('6f627fdf66d6529b', '<EMAIL>', 'test', '$P$Bluu55EDK8H./E9Bv7xq1u7X0vLmBd0', '', '', '$2y$09$22TFHGpS69JVW0ZmlXdjxu1LxvtKcwCPJf2YBRRvxvGI5W1VrAAA2', '::1', '', '{\"aiwp_logged_in\":null,\"mode\":\"test\",\"ppg\":\"118\",\"pp_ctaclr\":\"1559ED\",\"pmt\":\"rec\",\"vpay\":\"mcWiDilmgQ\",\"navmenu\":\"show\",\"splash\":\"off\",\"ailplogo\":\"off\",\"emailid\":\"\",\"theme_flag_name\":\"kt8typtb\",\"theme_flag_value\":\"arcana\"}', '2025-06-11 03:24:44', '2025-06-11 03:24:44')
DEBUG - 2025-06-11 03:24:44 --> btdbFindBy ---> INSERT INTO `start-user` (`user_pid`, `email`, `mode`, `password`, `first_name`, `last_name`, `login_token`, `ip_address`, `country`, `flags`, `created_at`, `updated_at`) VALUES ('6f627fdf66d6529b', '<EMAIL>', 'test', '$P$Bluu55EDK8H./E9Bv7xq1u7X0vLmBd0', '', '', '$2y$09$22TFHGpS69JVW0ZmlXdjxu1LxvtKcwCPJf2YBRRvxvGI5W1VrAAA2', '::1', '', '{\"aiwp_logged_in\":null,\"mode\":\"test\",\"ppg\":\"118\",\"pp_ctaclr\":\"1559ED\",\"pmt\":\"rec\",\"vpay\":\"mcWiDilmgQ\",\"navmenu\":\"show\",\"splash\":\"off\",\"ailplogo\":\"off\",\"emailid\":\"\",\"theme_flag_name\":\"kt8typtb\",\"theme_flag_value\":\"arcana\"}', '2025-06-11 03:24:44', '2025-06-11 03:24:44')
INFO - 2025-06-11 03:24:44 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 03:24:44 --> btdbFindBy ---> SELECT `start-plan`.*, `start-account`.*, DATE_FORMAT(resumed_at , "%M %e, %Y") as "resumed_at_formatted", `start-user`.`mode`, case when DATE_ADD(end_date, INTERVAL 1 DAY) < now() then 'yes' else 'no' end as 'expired'
FROM `start-account`
JOIN `start-user` ON `start-user`.`user_id` = `start-account`.`user_id`
JOIN `start-plan` ON `start-plan`.`plan_id` = `start-account`.`plan_id`
WHERE `start-account`.`user_id` = '8'
ORDER BY `start-account`.`end_date` DESC
DEBUG - 2025-06-11 03:24:44 --> btdbUpdate ---> INSERT INTO `start-user_app` (`email`, `userapp_pid`, `created_at`, `updated_at`) VALUES ('<EMAIL>', '5b3a5dc65803e6d03efb40efe145de64', '2025-06-11 03:24:44', '2025-06-11 03:24:44')
DEBUG - 2025-06-11 03:24:44 --> btdbFindBy ---> INSERT INTO `start-user_app` (`email`, `userapp_pid`, `created_at`, `updated_at`) VALUES ('<EMAIL>', '5b3a5dc65803e6d03efb40efe145de64', '2025-06-11 03:24:44', '2025-06-11 03:24:44')
INFO - 2025-06-11 03:24:44 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 03:24:44 --> btdbFindBy ---> 
DEBUG - 2025-06-11 03:24:44 --> btdbFindBy ---> SELECT `start-plan`.*, `start-account`.*, DATE_FORMAT(resumed_at , "%M %e, %Y") as "resumed_at_formatted", `start-user`.`mode`, case when DATE_ADD(end_date, INTERVAL 1 DAY) < now() then 'yes' else 'no' end as 'expired'
FROM `start-account`
JOIN `start-user` ON `start-user`.`user_id` = `start-account`.`user_id`
JOIN `start-plan` ON `start-plan`.`plan_id` = `start-account`.`plan_id`
WHERE `start-account`.`user_id` = '8'
ORDER BY `start-account`.`end_date` DESC
WARNING - 2025-06-11 03:24:44 --> [DEPRECATED] explode(): Passing null to parameter #2 ($string) of type string is deprecated in APPPATH\Models\BTModel.php on line 52.
 1 APPPATH\Models\BTModel.php(52): explode(',', null)
 2 APPPATH\Helpers\btdb_helper.php(180): App\Models\BTModel->findBy([...], null)
 3 APPPATH\Controllers\FlowPages\Payment.php(64): btdbFindBy('PlanModel', 'plan_id', null)
 4 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\FlowPages\Payment->index('mcWiDilmgQ')
 5 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\FlowPages\Payment))
 6 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 7 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 8 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
INFO - 2025-06-11 03:24:44 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 03:24:45 --> REQ ----------->118
INFO - 2025-06-11 03:24:50 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 03:24:50 --> btdbFindBy ---> 
DEBUG - 2025-06-11 03:24:50 --> btdbFindBy ---> SELECT `start-plan`.*, `start-account`.*, DATE_FORMAT(resumed_at , "%M %e, %Y") as "resumed_at_formatted", `start-user`.`mode`, case when DATE_ADD(end_date, INTERVAL 1 DAY) < now() then 'yes' else 'no' end as 'expired'
FROM `start-account`
JOIN `start-user` ON `start-user`.`user_id` = `start-account`.`user_id`
JOIN `start-plan` ON `start-plan`.`plan_id` = `start-account`.`plan_id`
WHERE `start-account`.`user_id` = '8'
ORDER BY `start-account`.`end_date` DESC
DEBUG - 2025-06-11 03:24:51 --> REQ ----------->186
DEBUG - 2025-06-11 03:24:51 --> REQ ----------->$2y$09$22TFHGpS69JVW0ZmlXdjxu1Lx
INFO - 2025-06-11 03:24:51 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 03:24:58 --> REQ ----------->$2y$09$22TFHGpS69JVW0ZmlXdjxu1LxTestUser4111-1111-1111-*****************
DEBUG - 2025-06-11 03:24:58 --> btdbFindBy ---> 
INFO - 2025-06-11 03:24:58 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 03:24:58 --> btdbFindBy ---> SELECT case when plan_type='promax' then 'pro max' else plan_type end as 'plan_type_display', `start-plan`.*
FROM `start-plan`
WHERE `plan_id` = '186'
DEBUG - 2025-06-11 03:24:58 --> btdbFindBy ---> 
WARNING - 2025-06-11 03:24:58 --> [DEPRECATED] Optional parameter $_api_key declared before required parameter $_mode is implicitly treated as a required parameter in VENDORPATH\btapp\btcore\src\Payment\Recurly.php on line 16.
 1 SYSTEMPATH\Autoloader\Autoloader.php(314): include_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\vendor\\codeigniter4\\framework\\system\\Debug\\Exceptions.php')
 2 SYSTEMPATH\Autoloader\Autoloader.php(250): CodeIgniter\Autoloader\Autoloader->includeFile('C:\\laragon\\www\\app.ai-pro.org\\btapp\\vendor\\composer/../btapp/btcore/src/Payment/Recurly.php')
 3 APPPATH\Controllers\Api.php(2447): CodeIgniter\Autoloader\Autoloader->loadClassmap('BTCore\\Payment\\Recurly')
 4 APPPATH\Controllers\Api.php(162): App\Controllers\Api->createSubscription()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('create-subscription')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-06-11 03:25:04 --> btdbUpdate ---> INSERT INTO `start-account` (`account_pid`, `user_id`, `plan_id`, `cc_number`, `cc_number_encrypted`, `merchant`, `merchant_customer_id`, `merchant_subscription_id`, `trial_end`, `start_date`, `end_date`, `cancelled_at`, `members`, `max_tokens`, `created_at`, `updated_at`) VALUES ('a8c312161616f797', '8', '186', '411111****1111', '68bfb396f35af3876fc509665b3dc23a0930aab1', 'Recurly', 'app-a8c312161616f797-8', '78cbd0546e1691f4c499e148d2b9a546', '2025-07-11 03:25:03', '2025-06-11 03:25:03', '2025-07-11 03:25:03', 0, 4, '********', '2025-06-11 03:25:04', '2025-06-11 03:25:04')
DEBUG - 2025-06-11 03:25:04 --> btdbUpdate ---> INSERT INTO `start-payment` (`acctpmt_pid`, `account_id`, `user_id`, `amount`, `currency`, `charge_id`, `merchant`, `cc_number`, `mode`, `created_at`, `updated_at`) VALUES ('7cc675d1920fb538', 7, '8', '249.95', 'USD', '291350', 'Recurly', '1111', 'test', '2025-06-11 03:25:04', '2025-06-11 03:25:04')
DEBUG - 2025-06-11 03:25:04 --> btdbFindBy ---> INSERT INTO `start-payment` (`acctpmt_pid`, `account_id`, `user_id`, `amount`, `currency`, `charge_id`, `merchant`, `cc_number`, `mode`, `created_at`, `updated_at`) VALUES ('7cc675d1920fb538', 7, '8', '249.95', 'USD', '291350', 'Recurly', '1111', 'test', '2025-06-11 03:25:04', '2025-06-11 03:25:04')
DEBUG - 2025-06-11 03:25:04 --> btdbUpdate ---> UPDATE `start-user` SET `orig_email` = '<EMAIL>', `is_fraud` = '', `status` = 'active', `updated_at` = '2025-06-11 03:25:04'
WHERE `start-user`.`user_id` IN ('8')
DEBUG - 2025-06-11 03:25:04 --> btdbFindBy ---> SELECT `start-plan`.*, `start-account`.*, DATE_FORMAT(resumed_at , "%M %e, %Y") as "resumed_at_formatted", `start-user`.`mode`, case when DATE_ADD(end_date, INTERVAL 1 DAY) < now() then 'yes' else 'no' end as 'expired'
FROM `start-account`
JOIN `start-user` ON `start-user`.`user_id` = `start-account`.`user_id`
JOIN `start-plan` ON `start-plan`.`plan_id` = `start-account`.`plan_id`
WHERE `start-account`.`user_id` = '8'
ORDER BY `start-account`.`end_date` DESC
WARNING - 2025-06-11 03:25:04 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 113.
 1 APPPATH\Helpers\btflag_helper.php(113): setcookie('pricing', null, **********, '', '')
 2 APPPATH\Controllers\Api.php(5487): btflag_remove('pricing')
 3 APPPATH\Controllers\Api.php(2659): App\Controllers\Api->doAfterPayment([...])
 4 APPPATH\Controllers\Api.php(162): App\Controllers\Api->createSubscription()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('create-subscription')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-11 03:25:04 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 114.
 1 APPPATH\Helpers\btflag_helper.php(114): setcookie('pricing', null, **********, '/', '')
 2 APPPATH\Controllers\Api.php(5487): btflag_remove('pricing')
 3 APPPATH\Controllers\Api.php(2659): App\Controllers\Api->doAfterPayment([...])
 4 APPPATH\Controllers\Api.php(162): App\Controllers\Api->createSubscription()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('create-subscription')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-11 03:25:04 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 113.
 1 APPPATH\Helpers\btflag_helper.php(113): setcookie('daily', null, **********, '', '')
 2 APPPATH\Controllers\Api.php(5495): btflag_remove('daily')
 3 APPPATH\Controllers\Api.php(2659): App\Controllers\Api->doAfterPayment([...])
 4 APPPATH\Controllers\Api.php(162): App\Controllers\Api->createSubscription()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('create-subscription')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-11 03:25:04 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 114.
 1 APPPATH\Helpers\btflag_helper.php(114): setcookie('daily', null, **********, '/', '')
 2 APPPATH\Controllers\Api.php(5495): btflag_remove('daily')
 3 APPPATH\Controllers\Api.php(2659): App\Controllers\Api->doAfterPayment([...])
 4 APPPATH\Controllers\Api.php(162): App\Controllers\Api->createSubscription()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('create-subscription')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-06-11 03:25:04 --> REQ ----------->$2y$09$22TFHGpS69JVW0ZmlXdjxu1Lx
INFO - 2025-06-11 03:25:04 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2025-06-11 03:25:04 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
WARNING - 2025-06-11 03:25:04 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 113.
 1 APPPATH\Helpers\btflag_helper.php(113): setcookie('pricing', null, **********, '', '')
 2 APPPATH\Controllers\FlowPages\ThankYou.php(68): btflag_remove('pricing')
 3 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\FlowPages\ThankYou->index()
 4 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\FlowPages\ThankYou))
 5 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 6 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 7 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-11 03:25:04 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 114.
 1 APPPATH\Helpers\btflag_helper.php(114): setcookie('pricing', null, **********, '/', '')
 2 APPPATH\Controllers\FlowPages\ThankYou.php(68): btflag_remove('pricing')
 3 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\FlowPages\ThankYou->index()
 4 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\FlowPages\ThankYou))
 5 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 6 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 7 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-11 03:25:04 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 113.
 1 APPPATH\Helpers\btflag_helper.php(113): setcookie('pricing', null, **********, '', '.ai-pro.org')
 2 APPPATH\Controllers\FlowPages\ThankYou.php(69): btflag_remove('pricing', null, [...])
 3 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\FlowPages\ThankYou->index()
 4 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\FlowPages\ThankYou))
 5 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 6 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 7 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-11 03:25:04 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 114.
 1 APPPATH\Helpers\btflag_helper.php(114): setcookie('pricing', null, **********, '/', '.ai-pro.org')
 2 APPPATH\Controllers\FlowPages\ThankYou.php(69): btflag_remove('pricing', null, [...])
 3 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\FlowPages\ThankYou->index()
 4 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\FlowPages\ThankYou))
 5 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 6 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 7 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-06-11 03:25:05 --> REQ ----------->$2y$09$22TFHGpS69JVW0ZmlXdjxu1Lx
DEBUG - 2025-06-11 03:25:05 --> btdbFindBy ---> 
INFO - 2025-06-11 03:25:05 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 03:25:05 --> REQ ----------->$2y$09$22TFHGpS69JVW0ZmlXdjxu1Lx
INFO - 2025-06-11 03:25:05 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2025-06-11 03:25:07 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 03:25:07 --> btdbFindBy ---> 
DEBUG - 2025-06-11 03:25:07 --> btdbFindBy ---> SELECT `start-plan`.*, `start-account`.*, DATE_FORMAT(resumed_at , "%M %e, %Y") as "resumed_at_formatted", `start-user`.`mode`, case when DATE_ADD(end_date, INTERVAL 1 DAY) < now() then 'yes' else 'no' end as 'expired'
FROM `start-account`
JOIN `start-user` ON `start-user`.`user_id` = `start-account`.`user_id`
JOIN `start-plan` ON `start-plan`.`plan_id` = `start-account`.`plan_id`
WHERE `start-account`.`user_id` = '8'
ORDER BY `start-account`.`end_date` DESC
DEBUG - 2025-06-11 03:25:08 --> REQ ----------->$2y$09$22TFHGpS69JVW0ZmlXdjxu1Lx
INFO - 2025-06-11 03:25:08 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 03:25:08 --> REQ ----------->
INFO - 2025-06-11 03:25:08 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 03:25:10 --> REQ ----------->$2y$09$22TFHGpS69JVW0ZmlXdjxu1Lx
DEBUG - 2025-06-11 03:25:10 --> btdbFindBy ---> 
INFO - 2025-06-11 03:25:10 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 03:25:10 --> REQ ----------->$2y$09$22TFHGpS69JVW0ZmlXdjxu1Lx
DEBUG - 2025-06-11 03:25:10 --> btdbFindBy ---> 
INFO - 2025-06-11 03:25:10 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 03:25:10 --> REQ ----------->$2y$09$22TFHGpS69JVW0ZmlXdjxu1Lx
INFO - 2025-06-11 03:25:10 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 03:25:11 --> REQ ----------->$2y$09$22TFHGpS69JVW0ZmlXdjxu1Lx
INFO - 2025-06-11 03:25:11 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 03:25:11 --> REQ ----------->$2y$09$22TFHGpS69JVW0ZmlXdjxu1Lx
INFO - 2025-06-11 03:25:11 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 03:25:14 --> REQ ----------->$2y$09$22TFHGpS69JVW0ZmlXdjxu1Lx
INFO - 2025-06-11 03:25:14 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 03:25:14 --> REQ ----------->
INFO - 2025-06-11 03:25:14 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2025-06-11 03:25:17 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 03:25:17 --> btdbFindBy ---> 
DEBUG - 2025-06-11 03:25:18 --> REQ ----------->
INFO - 2025-06-11 03:25:18 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 03:25:18 --> REQ ----------->
INFO - 2025-06-11 03:25:18 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 03:25:26 --> REQ ----------->
INFO - 2025-06-11 03:25:26 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2025-06-11 03:25:31 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 03:25:32 --> REQ ----------->118
DEBUG - 2025-06-11 03:25:32 --> REQ ----------->
INFO - 2025-06-11 03:25:32 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 03:25:34 --> REQ ----------->
INFO - 2025-06-11 03:25:34 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 03:25:34 --> REQ ----------->
INFO - 2025-06-11 03:25:34 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 03:25:34 --> REQ ----------->
INFO - 2025-06-11 03:25:34 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2025-06-11 03:25:35 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 03:25:35 --> REQ ----------->
INFO - 2025-06-11 03:25:35 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 03:25:39 --> REQ ----------->testuser061125_08@mailinator.com123123123123
DEBUG - 2025-06-11 03:25:39 --> testuser061125_08@mailinator.com123123123123
DEBUG - 2025-06-11 03:25:39 --> btdbFindBy ---> 
DEBUG - 2025-06-11 03:25:39 --> btdbUpdate ---> INSERT INTO `start-user` (`user_pid`, `email`, `mode`, `password`, `first_name`, `last_name`, `login_token`, `ip_address`, `country`, `flags`, `created_at`, `updated_at`) VALUES ('bb914956b750d4f6', '<EMAIL>', 'test', '$P$BihzY9PmzxDwUFOORYJw21/zTh1ZVq1', '', '', '$2y$09$chfUZ9641H4vdQ8wEI66UuM31.pofS9CfmtIoarUPWKu.cT3pXGGa', '::1', '', '{\"aiwp_logged_in\":null,\"mode\":\"test\",\"ppg\":\"118\",\"pp_ctaclr\":\"1559ED\",\"pmt\":\"rec\",\"vpay\":\"mcWiDilmgQ\",\"navmenu\":\"show\",\"splash\":\"off\",\"ailplogo\":\"off\",\"emailid\":\"\",\"theme_flag_name\":\"kt8typtb\",\"theme_flag_value\":\"arcana\"}', '2025-06-11 03:25:39', '2025-06-11 03:25:39')
DEBUG - 2025-06-11 03:25:39 --> btdbFindBy ---> INSERT INTO `start-user` (`user_pid`, `email`, `mode`, `password`, `first_name`, `last_name`, `login_token`, `ip_address`, `country`, `flags`, `created_at`, `updated_at`) VALUES ('bb914956b750d4f6', '<EMAIL>', 'test', '$P$BihzY9PmzxDwUFOORYJw21/zTh1ZVq1', '', '', '$2y$09$chfUZ9641H4vdQ8wEI66UuM31.pofS9CfmtIoarUPWKu.cT3pXGGa', '::1', '', '{\"aiwp_logged_in\":null,\"mode\":\"test\",\"ppg\":\"118\",\"pp_ctaclr\":\"1559ED\",\"pmt\":\"rec\",\"vpay\":\"mcWiDilmgQ\",\"navmenu\":\"show\",\"splash\":\"off\",\"ailplogo\":\"off\",\"emailid\":\"\",\"theme_flag_name\":\"kt8typtb\",\"theme_flag_value\":\"arcana\"}', '2025-06-11 03:25:39', '2025-06-11 03:25:39')
INFO - 2025-06-11 03:25:39 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 03:25:39 --> btdbFindBy ---> SELECT `start-plan`.*, `start-account`.*, DATE_FORMAT(resumed_at , "%M %e, %Y") as "resumed_at_formatted", `start-user`.`mode`, case when DATE_ADD(end_date, INTERVAL 1 DAY) < now() then 'yes' else 'no' end as 'expired'
FROM `start-account`
JOIN `start-user` ON `start-user`.`user_id` = `start-account`.`user_id`
JOIN `start-plan` ON `start-plan`.`plan_id` = `start-account`.`plan_id`
WHERE `start-account`.`user_id` = '9'
ORDER BY `start-account`.`end_date` DESC
DEBUG - 2025-06-11 03:25:39 --> btdbUpdate ---> INSERT INTO `start-user_app` (`email`, `userapp_pid`, `created_at`, `updated_at`) VALUES ('<EMAIL>', '8ae4fd62e04eca977a82a1988ae7c6de', '2025-06-11 03:25:39', '2025-06-11 03:25:39')
DEBUG - 2025-06-11 03:25:39 --> btdbFindBy ---> INSERT INTO `start-user_app` (`email`, `userapp_pid`, `created_at`, `updated_at`) VALUES ('<EMAIL>', '8ae4fd62e04eca977a82a1988ae7c6de', '2025-06-11 03:25:39', '2025-06-11 03:25:39')
INFO - 2025-06-11 03:25:39 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 03:25:39 --> btdbFindBy ---> 
DEBUG - 2025-06-11 03:25:39 --> btdbFindBy ---> SELECT `start-plan`.*, `start-account`.*, DATE_FORMAT(resumed_at , "%M %e, %Y") as "resumed_at_formatted", `start-user`.`mode`, case when DATE_ADD(end_date, INTERVAL 1 DAY) < now() then 'yes' else 'no' end as 'expired'
FROM `start-account`
JOIN `start-user` ON `start-user`.`user_id` = `start-account`.`user_id`
JOIN `start-plan` ON `start-plan`.`plan_id` = `start-account`.`plan_id`
WHERE `start-account`.`user_id` = '9'
ORDER BY `start-account`.`end_date` DESC
WARNING - 2025-06-11 03:25:39 --> [DEPRECATED] explode(): Passing null to parameter #2 ($string) of type string is deprecated in APPPATH\Models\BTModel.php on line 52.
 1 APPPATH\Models\BTModel.php(52): explode(',', null)
 2 APPPATH\Helpers\btdb_helper.php(180): App\Models\BTModel->findBy([...], null)
 3 APPPATH\Controllers\FlowPages\Payment.php(64): btdbFindBy('PlanModel', 'plan_id', null)
 4 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\FlowPages\Payment->index('mcWiDilmgQ')
 5 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\FlowPages\Payment))
 6 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 7 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 8 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
INFO - 2025-06-11 03:25:39 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 03:25:40 --> REQ ----------->118
INFO - 2025-06-11 03:25:46 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 03:25:46 --> btdbFindBy ---> 
DEBUG - 2025-06-11 03:25:46 --> btdbFindBy ---> SELECT `start-plan`.*, `start-account`.*, DATE_FORMAT(resumed_at , "%M %e, %Y") as "resumed_at_formatted", `start-user`.`mode`, case when DATE_ADD(end_date, INTERVAL 1 DAY) < now() then 'yes' else 'no' end as 'expired'
FROM `start-account`
JOIN `start-user` ON `start-user`.`user_id` = `start-account`.`user_id`
JOIN `start-plan` ON `start-plan`.`plan_id` = `start-account`.`plan_id`
WHERE `start-account`.`user_id` = '9'
ORDER BY `start-account`.`end_date` DESC
DEBUG - 2025-06-11 03:25:46 --> REQ ----------->187
DEBUG - 2025-06-11 03:25:47 --> REQ ----------->$2y$09$chfUZ9641H4vdQ8wEI66UuM31
INFO - 2025-06-11 03:25:47 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 03:25:56 --> REQ ----------->$2y$09$chfUZ9641H4vdQ8wEI66UuM31TestUser4111-1111-1111-111112203412318710
DEBUG - 2025-06-11 03:25:56 --> btdbFindBy ---> 
INFO - 2025-06-11 03:25:56 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 03:25:56 --> btdbFindBy ---> SELECT case when plan_type='promax' then 'pro max' else plan_type end as 'plan_type_display', `start-plan`.*
FROM `start-plan`
WHERE `plan_id` = '187'
DEBUG - 2025-06-11 03:25:57 --> btdbFindBy ---> 
WARNING - 2025-06-11 03:25:57 --> [DEPRECATED] Optional parameter $_api_key declared before required parameter $_mode is implicitly treated as a required parameter in VENDORPATH\btapp\btcore\src\Payment\Recurly.php on line 16.
 1 SYSTEMPATH\Autoloader\Autoloader.php(314): include_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\vendor\\codeigniter4\\framework\\system\\Debug\\Exceptions.php')
 2 SYSTEMPATH\Autoloader\Autoloader.php(250): CodeIgniter\Autoloader\Autoloader->includeFile('C:\\laragon\\www\\app.ai-pro.org\\btapp\\vendor\\composer/../btapp/btcore/src/Payment/Recurly.php')
 3 APPPATH\Controllers\Api.php(2447): CodeIgniter\Autoloader\Autoloader->loadClassmap('BTCore\\Payment\\Recurly')
 4 APPPATH\Controllers\Api.php(162): App\Controllers\Api->createSubscription()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('create-subscription')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-06-11 03:26:02 --> btdbUpdate ---> INSERT INTO `start-account` (`account_pid`, `user_id`, `plan_id`, `cc_number`, `cc_number_encrypted`, `merchant`, `merchant_customer_id`, `merchant_subscription_id`, `trial_end`, `start_date`, `end_date`, `cancelled_at`, `members`, `max_tokens`, `created_at`, `updated_at`) VALUES ('a8578ec54bb6dc95', '9', '187', '411111****1111', '68bfb396f35af3876fc509665b3dc23a0930aab1', 'Recurly', 'app-a8578ec54bb6dc95-9', '78cbd137441c7f57857e934dfab5bbdf', '2025-07-11 03:26:01', '2025-06-11 03:26:01', '2025-07-11 03:26:01', 0, 9, '********', '2025-06-11 03:26:02', '2025-06-11 03:26:02')
DEBUG - 2025-06-11 03:26:02 --> btdbUpdate ---> INSERT INTO `start-payment` (`acctpmt_pid`, `account_id`, `user_id`, `amount`, `currency`, `charge_id`, `merchant`, `cc_number`, `mode`, `created_at`, `updated_at`) VALUES ('22633789d573b164', 8, '9', '499.95', 'USD', '291352', 'Recurly', '1111', 'test', '2025-06-11 03:26:02', '2025-06-11 03:26:02')
DEBUG - 2025-06-11 03:26:02 --> btdbFindBy ---> INSERT INTO `start-payment` (`acctpmt_pid`, `account_id`, `user_id`, `amount`, `currency`, `charge_id`, `merchant`, `cc_number`, `mode`, `created_at`, `updated_at`) VALUES ('22633789d573b164', 8, '9', '499.95', 'USD', '291352', 'Recurly', '1111', 'test', '2025-06-11 03:26:02', '2025-06-11 03:26:02')
DEBUG - 2025-06-11 03:26:02 --> btdbUpdate ---> UPDATE `start-user` SET `orig_email` = '<EMAIL>', `is_fraud` = '', `status` = 'active', `updated_at` = '2025-06-11 03:26:02'
WHERE `start-user`.`user_id` IN ('9')
DEBUG - 2025-06-11 03:26:02 --> btdbFindBy ---> SELECT `start-plan`.*, `start-account`.*, DATE_FORMAT(resumed_at , "%M %e, %Y") as "resumed_at_formatted", `start-user`.`mode`, case when DATE_ADD(end_date, INTERVAL 1 DAY) < now() then 'yes' else 'no' end as 'expired'
FROM `start-account`
JOIN `start-user` ON `start-user`.`user_id` = `start-account`.`user_id`
JOIN `start-plan` ON `start-plan`.`plan_id` = `start-account`.`plan_id`
WHERE `start-account`.`user_id` = '9'
ORDER BY `start-account`.`end_date` DESC
WARNING - 2025-06-11 03:26:02 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 113.
 1 APPPATH\Helpers\btflag_helper.php(113): setcookie('pricing', null, **********, '', '')
 2 APPPATH\Controllers\Api.php(5487): btflag_remove('pricing')
 3 APPPATH\Controllers\Api.php(2659): App\Controllers\Api->doAfterPayment([...])
 4 APPPATH\Controllers\Api.php(162): App\Controllers\Api->createSubscription()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('create-subscription')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-11 03:26:02 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 114.
 1 APPPATH\Helpers\btflag_helper.php(114): setcookie('pricing', null, **********, '/', '')
 2 APPPATH\Controllers\Api.php(5487): btflag_remove('pricing')
 3 APPPATH\Controllers\Api.php(2659): App\Controllers\Api->doAfterPayment([...])
 4 APPPATH\Controllers\Api.php(162): App\Controllers\Api->createSubscription()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('create-subscription')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-11 03:26:02 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 113.
 1 APPPATH\Helpers\btflag_helper.php(113): setcookie('daily', null, **********, '', '')
 2 APPPATH\Controllers\Api.php(5495): btflag_remove('daily')
 3 APPPATH\Controllers\Api.php(2659): App\Controllers\Api->doAfterPayment([...])
 4 APPPATH\Controllers\Api.php(162): App\Controllers\Api->createSubscription()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('create-subscription')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-11 03:26:02 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 114.
 1 APPPATH\Helpers\btflag_helper.php(114): setcookie('daily', null, **********, '/', '')
 2 APPPATH\Controllers\Api.php(5495): btflag_remove('daily')
 3 APPPATH\Controllers\Api.php(2659): App\Controllers\Api->doAfterPayment([...])
 4 APPPATH\Controllers\Api.php(162): App\Controllers\Api->createSubscription()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('create-subscription')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
INFO - 2025-06-11 03:26:02 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
WARNING - 2025-06-11 03:26:02 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 113.
 1 APPPATH\Helpers\btflag_helper.php(113): setcookie('pricing', null, **********, '', '')
 2 APPPATH\Controllers\FlowPages\ThankYou.php(68): btflag_remove('pricing')
 3 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\FlowPages\ThankYou->index()
 4 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\FlowPages\ThankYou))
 5 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 6 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 7 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-11 03:26:02 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 114.
 1 APPPATH\Helpers\btflag_helper.php(114): setcookie('pricing', null, **********, '/', '')
 2 APPPATH\Controllers\FlowPages\ThankYou.php(68): btflag_remove('pricing')
 3 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\FlowPages\ThankYou->index()
 4 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\FlowPages\ThankYou))
 5 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 6 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 7 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-11 03:26:02 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 113.
 1 APPPATH\Helpers\btflag_helper.php(113): setcookie('pricing', null, **********, '', '.ai-pro.org')
 2 APPPATH\Controllers\FlowPages\ThankYou.php(69): btflag_remove('pricing', null, [...])
 3 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\FlowPages\ThankYou->index()
 4 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\FlowPages\ThankYou))
 5 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 6 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 7 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-11 03:26:02 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 114.
 1 APPPATH\Helpers\btflag_helper.php(114): setcookie('pricing', null, **********, '/', '.ai-pro.org')
 2 APPPATH\Controllers\FlowPages\ThankYou.php(69): btflag_remove('pricing', null, [...])
 3 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\FlowPages\ThankYou->index()
 4 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\FlowPages\ThankYou))
 5 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 6 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 7 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-06-11 03:26:03 --> REQ ----------->$2y$09$chfUZ9641H4vdQ8wEI66UuM31
DEBUG - 2025-06-11 03:26:03 --> btdbFindBy ---> 
INFO - 2025-06-11 03:26:03 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 03:26:03 --> REQ ----------->$2y$09$chfUZ9641H4vdQ8wEI66UuM31
INFO - 2025-06-11 03:26:03 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2025-06-11 03:26:04 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 03:26:04 --> btdbFindBy ---> 
DEBUG - 2025-06-11 03:26:04 --> btdbFindBy ---> SELECT `start-plan`.*, `start-account`.*, DATE_FORMAT(resumed_at , "%M %e, %Y") as "resumed_at_formatted", `start-user`.`mode`, case when DATE_ADD(end_date, INTERVAL 1 DAY) < now() then 'yes' else 'no' end as 'expired'
FROM `start-account`
JOIN `start-user` ON `start-user`.`user_id` = `start-account`.`user_id`
JOIN `start-plan` ON `start-plan`.`plan_id` = `start-account`.`plan_id`
WHERE `start-account`.`user_id` = '9'
ORDER BY `start-account`.`end_date` DESC
DEBUG - 2025-06-11 03:26:04 --> btdbFindBy ---> SELECT *
FROM `start-plan`
WHERE `plan_id` = '187'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-11 03:26:05 --> REQ ----------->$2y$09$chfUZ9641H4vdQ8wEI66UuM31
INFO - 2025-06-11 03:26:05 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 03:26:05 --> REQ ----------->
INFO - 2025-06-11 03:26:05 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 03:26:07 --> REQ ----------->
DEBUG - 2025-06-11 03:26:07 --> REQ ----------->$2y$09$chfUZ9641H4vdQ8wEI66UuM31
DEBUG - 2025-06-11 03:26:07 --> btdbFindBy ---> 
INFO - 2025-06-11 03:26:07 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2025-06-11 03:26:07 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 03:26:07 --> btdbFindBy ---> 
DEBUG - 2025-06-11 03:26:07 --> btdbFindBy ---> SELECT `start-plan`.*, `start-account`.*, DATE_FORMAT(resumed_at , "%M %e, %Y") as "resumed_at_formatted", `start-user`.`mode`, case when DATE_ADD(end_date, INTERVAL 1 DAY) < now() then 'yes' else 'no' end as 'expired'
FROM `start-account`
JOIN `start-user` ON `start-user`.`user_id` = `start-account`.`user_id`
JOIN `start-plan` ON `start-plan`.`plan_id` = `start-account`.`plan_id`
WHERE `start-account`.`user_id` = '9'
ORDER BY `start-account`.`end_date` DESC
DEBUG - 2025-06-11 03:26:08 --> REQ ----------->$2y$09$chfUZ9641H4vdQ8wEI66UuM31
INFO - 2025-06-11 03:26:08 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 03:26:08 --> REQ ----------->$2y$09$chfUZ9641H4vdQ8wEI66UuM31
INFO - 2025-06-11 03:26:08 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 03:26:08 --> REQ ----------->
INFO - 2025-06-11 03:26:08 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 03:26:10 --> REQ ----------->$2y$09$chfUZ9641H4vdQ8wEI66UuM31
DEBUG - 2025-06-11 03:26:10 --> btdbFindBy ---> 
INFO - 2025-06-11 03:26:10 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 03:26:10 --> REQ ----------->$2y$09$chfUZ9641H4vdQ8wEI66UuM31
DEBUG - 2025-06-11 03:26:10 --> btdbFindBy ---> 
INFO - 2025-06-11 03:26:10 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 03:26:10 --> REQ ----------->$2y$09$chfUZ9641H4vdQ8wEI66UuM31
INFO - 2025-06-11 03:26:10 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 03:26:10 --> REQ ----------->$2y$09$chfUZ9641H4vdQ8wEI66UuM31
INFO - 2025-06-11 03:26:10 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 03:26:10 --> REQ ----------->$2y$09$chfUZ9641H4vdQ8wEI66UuM31
INFO - 2025-06-11 03:26:10 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 03:26:18 --> REQ ----------->$2y$09$chfUZ9641H4vdQ8wEI66UuM31
INFO - 2025-06-11 03:26:18 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 03:26:18 --> REQ ----------->
INFO - 2025-06-11 03:26:18 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 03:26:31 --> REQ ----------->$2y$09$chfUZ9641H4vdQ8wEI66UuM31
INFO - 2025-06-11 03:26:31 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 03:26:31 --> REQ ----------->
INFO - 2025-06-11 03:26:31 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 03:26:41 --> REQ ----------->$2y$09$chfUZ9641H4vdQ8wEI66UuM31
INFO - 2025-06-11 03:26:41 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 03:26:41 --> REQ ----------->
INFO - 2025-06-11 03:26:41 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2025-06-11 03:26:43 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 03:26:43 --> btdbFindBy ---> 
DEBUG - 2025-06-11 03:26:44 --> REQ ----------->
INFO - 2025-06-11 03:26:44 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 03:26:44 --> REQ ----------->
INFO - 2025-06-11 03:26:44 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2025-06-11 05:39:39 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 05:39:42 --> REQ ----------->$2y$09$GPcROzqMXqL9IPlz8DJir.etwtJY5CmaRJVr9KHLEItMZPM4CYLFi
INFO - 2025-06-11 05:39:42 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 05:39:42 --> btdbFindBy ---> 
DEBUG - 2025-06-11 05:39:43 --> REQ ----------->$2y$09$GPcROzqMXqL9IPlz8DJir.etwtJY5CmaRJVr9KHLEItMZPM4CYLFi
DEBUG - 2025-06-11 05:39:43 --> btdbFindBy ---> 
INFO - 2025-06-11 05:39:43 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 05:39:49 --> REQ ----------->$2y$09$GPcROzqMXqL9IPlz8DJir.etwtJY5CmaRJVr9KHLEItMZPM4CYLFi
INFO - 2025-06-11 05:39:49 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2025-06-11 05:52:14 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 05:52:15 --> REQ ----------->$2y$09$GPcROzqMXqL9IPlz8DJir.etwtJY5CmaRJVr9KHLEItMZPM4CYLFi
INFO - 2025-06-11 05:52:15 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 05:52:15 --> btdbFindBy ---> 
DEBUG - 2025-06-11 05:52:15 --> REQ ----------->$2y$09$GPcROzqMXqL9IPlz8DJir.etwtJY5CmaRJVr9KHLEItMZPM4CYLFi
DEBUG - 2025-06-11 05:52:15 --> btdbFindBy ---> 
INFO - 2025-06-11 05:52:15 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 05:52:40 --> REQ ----------->$2y$09$GPcROzqMXqL9IPlz8DJir.etwtJY5CmaRJVr9KHLEItMZPM4CYLFi
INFO - 2025-06-11 05:52:40 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 05:53:34 --> REQ ----------->$2y$09$GPcROzqMXqL9IPlz8DJir.etwtJY5CmaRJVr9KHLEItMZPM4CYLFi
INFO - 2025-06-11 05:53:34 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2025-06-11 05:58:31 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 05:58:32 --> REQ ----------->$2y$09$GPcROzqMXqL9IPlz8DJir.etwtJY5CmaRJVr9KHLEItMZPM4CYLFi
INFO - 2025-06-11 05:58:32 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 05:58:32 --> btdbFindBy ---> 
DEBUG - 2025-06-11 05:58:32 --> REQ ----------->$2y$09$GPcROzqMXqL9IPlz8DJir.etwtJY5CmaRJVr9KHLEItMZPM4CYLFi
DEBUG - 2025-06-11 05:58:32 --> btdbFindBy ---> 
INFO - 2025-06-11 05:58:32 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 05:58:51 --> REQ ----------->$2y$09$GPcROzqMXqL9IPlz8DJir.etwtJY5CmaRJVr9KHLEItMZPM4CYLFi
INFO - 2025-06-11 05:58:51 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 05:58:51 --> btdbFindBy ---> 
DEBUG - 2025-06-11 05:58:52 --> REQ ----------->$2y$09$GPcROzqMXqL9IPlz8DJir.etwtJY5CmaRJVr9KHLEItMZPM4CYLFi
INFO - 2025-06-11 05:58:52 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 05:58:52 --> REQ ----------->$2y$09$GPcROzqMXqL9IPlz8DJir.etwtJY5CmaRJVr9KHLEItMZPM4CYLFi
DEBUG - 2025-06-11 05:58:52 --> btdbFindBy ---> 
INFO - 2025-06-11 05:58:52 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 05:59:58 --> REQ ----------->$2y$09$GPcROzqMXqL9IPlz8DJir.etwtJY5CmaRJVr9KHLEItMZPM4CYLFi
INFO - 2025-06-11 05:59:58 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 06:00:00 --> REQ ----------->$2y$09$GPcROzqMXqL9IPlz8DJir.etwtJY5CmaRJVr9KHLEItMZPM4CYLFi
INFO - 2025-06-11 06:00:00 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 06:00:01 --> REQ ----------->$2y$09$GPcROzqMXqL9IPlz8DJir.etwtJY5CmaRJVr9KHLEItMZPM4CYLFi
DEBUG - 2025-06-11 06:00:01 --> btdbFindBy ---> 
INFO - 2025-06-11 06:00:01 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 06:00:04 --> REQ ----------->$2y$09$GPcROzqMXqL9IPlz8DJir.etwtJY5CmaRJVr9KHLEItMZPM4CYLFi
INFO - 2025-06-11 06:00:04 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 06:00:04 --> REQ ----------->$2y$09$GPcROzqMXqL9IPlz8DJir.etwtJY5CmaRJVr9KHLEItMZPM4CYLFi
DEBUG - 2025-06-11 06:00:04 --> btdbFindBy ---> 
INFO - 2025-06-11 06:00:04 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 06:00:12 --> REQ ----------->$2y$09$GPcROzqMXqL9IPlz8DJir.etwtJY5CmaRJVr9KHLEItMZPM4CYLFi
INFO - 2025-06-11 06:00:12 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2025-06-11 06:01:18 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 06:01:19 --> REQ ----------->$2y$09$GPcROzqMXqL9IPlz8DJir.etwtJY5CmaRJVr9KHLEItMZPM4CYLFi
INFO - 2025-06-11 06:01:19 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 06:01:19 --> REQ ----------->$2y$09$GPcROzqMXqL9IPlz8DJir.etwtJY5CmaRJVr9KHLEItMZPM4CYLFi
DEBUG - 2025-06-11 06:01:19 --> btdbFindBy ---> 
INFO - 2025-06-11 06:01:19 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 06:02:09 --> REQ ----------->$2y$09$GPcROzqMXqL9IPlz8DJir.etwtJY5CmaRJVr9KHLEItMZPM4CYLFi
INFO - 2025-06-11 06:02:09 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2025-06-11 06:03:01 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 06:03:02 --> REQ ----------->$2y$09$GPcROzqMXqL9IPlz8DJir.etwtJY5CmaRJVr9KHLEItMZPM4CYLFi
INFO - 2025-06-11 06:03:02 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 06:03:02 --> btdbFindBy ---> 
DEBUG - 2025-06-11 06:03:02 --> REQ ----------->$2y$09$GPcROzqMXqL9IPlz8DJir.etwtJY5CmaRJVr9KHLEItMZPM4CYLFi
DEBUG - 2025-06-11 06:03:02 --> btdbFindBy ---> 
INFO - 2025-06-11 06:03:02 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 06:03:11 --> REQ ----------->$2y$09$GPcROzqMXqL9IPlz8DJir.etwtJY5CmaRJVr9KHLEItMZPM4CYLFi
INFO - 2025-06-11 06:03:11 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 06:03:11 --> btdbFindBy ---> 
INFO - 2025-06-11 06:03:55 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 06:03:56 --> REQ ----------->$2y$09$GPcROzqMXqL9IPlz8DJir.etwtJY5CmaRJVr9KHLEItMZPM4CYLFi
INFO - 2025-06-11 06:03:56 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 06:03:56 --> REQ ----------->$2y$09$GPcROzqMXqL9IPlz8DJir.etwtJY5CmaRJVr9KHLEItMZPM4CYLFi
DEBUG - 2025-06-11 06:03:56 --> btdbFindBy ---> 
INFO - 2025-06-11 06:03:56 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 06:04:24 --> REQ ----------->$2y$09$GPcROzqMXqL9IPlz8DJir.etwtJY5CmaRJVr9KHLEItMZPM4CYLFi
DEBUG - 2025-06-11 06:04:31 --> REQ ----------->
INFO - 2025-06-11 06:05:05 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 06:05:06 --> REQ ----------->$2y$09$GPcROzqMXqL9IPlz8DJir.etwtJY5CmaRJVr9KHLEItMZPM4CYLFi
DEBUG - 2025-06-11 06:05:07 --> REQ ----------->$2y$09$GPcROzqMXqL9IPlz8DJir.etwtJY5CmaRJVr9KHLEItMZPM4CYLFi
DEBUG - 2025-06-11 06:05:07 --> btdbFindBy ---> 
INFO - 2025-06-11 06:05:07 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 06:05:13 --> REQ ----------->$2y$09$GPcROzqMXqL9IPlz8DJir.etwtJY5CmaRJVr9KHLEItMZPM4CYLFi
INFO - 2025-06-11 06:07:54 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 07:01:38 --> REQ ----------->
INFO - 2025-06-11 07:01:38 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 07:01:39 --> REQ ----------->
INFO - 2025-06-11 07:01:39 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 07:01:40 --> REQ ----------->
INFO - 2025-06-11 07:01:40 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 07:01:40 --> REQ ----------->
INFO - 2025-06-11 07:01:40 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 07:01:40 --> REQ ----------->
INFO - 2025-06-11 07:01:40 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 07:01:40 --> REQ ----------->
INFO - 2025-06-11 07:01:40 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 07:01:41 --> REQ ----------->
INFO - 2025-06-11 07:01:41 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 07:01:41 --> REQ ----------->
INFO - 2025-06-11 07:01:41 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 07:01:43 --> REQ ----------->
INFO - 2025-06-11 07:01:43 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 07:01:44 --> REQ ----------->
INFO - 2025-06-11 07:01:44 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 07:01:46 --> REQ ----------->
INFO - 2025-06-11 07:01:46 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 07:01:47 --> REQ ----------->
INFO - 2025-06-11 07:01:47 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 07:01:50 --> REQ ----------->
INFO - 2025-06-11 07:01:50 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
CRITICAL - 2025-06-11 07:01:53 --> Unable to find template "/theme_basilisk/index_v1.twig" (looked into: C:\laragon\www\app.ai-pro.org\btapp\app\Views/twig).
in VENDORPATH\twig\twig\src\Loader\FilesystemLoader.php on line 227.
 1 VENDORPATH\twig\twig\src\Loader\FilesystemLoader.php(131): Twig\Loader\FilesystemLoader->findTemplate('/theme_basilisk/index_v1.twig')
 2 VENDORPATH\twig\twig\src\Environment.php(261): Twig\Loader\FilesystemLoader->getCacheKey('/theme_basilisk/index_v1.twig')
 3 VENDORPATH\twig\twig\src\Environment.php(309): Twig\Environment->getTemplateClass('/theme_basilisk/index_v1.twig')
 4 VENDORPATH\twig\twig\src\Environment.php(277): Twig\Environment->load('/theme_basilisk/index_v1.twig')
 5 VENDORPATH\btapp\btcore\src\TemplateEngine\TwigCustom.php(39): Twig\Environment->render('/theme_basilisk/index_v1.twig', [...])
 6 VENDORPATH\btapp\btcore\src\TemplateEngine.php(28): BTCore\TemplateEngine\TwigCustom->view('/theme_basilisk/index_v1.twig', [...])
 7 APPPATH\Controllers\FlowPages\LandingPage.php(168): BTCore\TemplateEngine->view('/theme_basilisk/index_v1.twig', [...])
 8 APPPATH\Controllers\FlowPages\LandingPage.php(62): App\Controllers\FlowPages\LandingPage->theme_basilisk()
 9 APPPATH\Controllers\FlowPages\LandingPage.php(80): App\Controllers\FlowPages\LandingPage->index()
10 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\FlowPages\LandingPage->show_404()
11 SYSTEMPATH\CodeIgniter.php(961): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\FlowPages\LandingPage))
12 SYSTEMPATH\CodeIgniter.php(385): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
13 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
14 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
CRITICAL - 2025-06-11 07:01:59 --> Unable to find template "/theme_basilisk/index_v1.twig" (looked into: C:\laragon\www\app.ai-pro.org\btapp\app\Views/twig).
in VENDORPATH\twig\twig\src\Loader\FilesystemLoader.php on line 227.
 1 VENDORPATH\twig\twig\src\Loader\FilesystemLoader.php(131): Twig\Loader\FilesystemLoader->findTemplate('/theme_basilisk/index_v1.twig')
 2 VENDORPATH\twig\twig\src\Environment.php(261): Twig\Loader\FilesystemLoader->getCacheKey('/theme_basilisk/index_v1.twig')
 3 VENDORPATH\twig\twig\src\Environment.php(309): Twig\Environment->getTemplateClass('/theme_basilisk/index_v1.twig')
 4 VENDORPATH\twig\twig\src\Environment.php(277): Twig\Environment->load('/theme_basilisk/index_v1.twig')
 5 VENDORPATH\btapp\btcore\src\TemplateEngine\TwigCustom.php(39): Twig\Environment->render('/theme_basilisk/index_v1.twig', [...])
 6 VENDORPATH\btapp\btcore\src\TemplateEngine.php(28): BTCore\TemplateEngine\TwigCustom->view('/theme_basilisk/index_v1.twig', [...])
 7 APPPATH\Controllers\FlowPages\LandingPage.php(168): BTCore\TemplateEngine->view('/theme_basilisk/index_v1.twig', [...])
 8 APPPATH\Controllers\FlowPages\LandingPage.php(62): App\Controllers\FlowPages\LandingPage->theme_basilisk()
 9 APPPATH\Controllers\FlowPages\LandingPage.php(80): App\Controllers\FlowPages\LandingPage->index()
10 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\FlowPages\LandingPage->show_404()
11 SYSTEMPATH\CodeIgniter.php(961): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\FlowPages\LandingPage))
12 SYSTEMPATH\CodeIgniter.php(385): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
13 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
14 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
CRITICAL - 2025-06-11 07:02:05 --> Unable to find template "/theme_basilisk/index_v1.twig" (looked into: C:\laragon\www\app.ai-pro.org\btapp\app\Views/twig).
in VENDORPATH\twig\twig\src\Loader\FilesystemLoader.php on line 227.
 1 VENDORPATH\twig\twig\src\Loader\FilesystemLoader.php(131): Twig\Loader\FilesystemLoader->findTemplate('/theme_basilisk/index_v1.twig')
 2 VENDORPATH\twig\twig\src\Environment.php(261): Twig\Loader\FilesystemLoader->getCacheKey('/theme_basilisk/index_v1.twig')
 3 VENDORPATH\twig\twig\src\Environment.php(309): Twig\Environment->getTemplateClass('/theme_basilisk/index_v1.twig')
 4 VENDORPATH\twig\twig\src\Environment.php(277): Twig\Environment->load('/theme_basilisk/index_v1.twig')
 5 VENDORPATH\btapp\btcore\src\TemplateEngine\TwigCustom.php(39): Twig\Environment->render('/theme_basilisk/index_v1.twig', [...])
 6 VENDORPATH\btapp\btcore\src\TemplateEngine.php(28): BTCore\TemplateEngine\TwigCustom->view('/theme_basilisk/index_v1.twig', [...])
 7 APPPATH\Controllers\FlowPages\LandingPage.php(168): BTCore\TemplateEngine->view('/theme_basilisk/index_v1.twig', [...])
 8 APPPATH\Controllers\FlowPages\LandingPage.php(62): App\Controllers\FlowPages\LandingPage->theme_basilisk()
 9 APPPATH\Controllers\FlowPages\LandingPage.php(80): App\Controllers\FlowPages\LandingPage->index()
10 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\FlowPages\LandingPage->show_404()
11 SYSTEMPATH\CodeIgniter.php(961): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\FlowPages\LandingPage))
12 SYSTEMPATH\CodeIgniter.php(385): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
13 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
14 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
CRITICAL - 2025-06-11 07:02:11 --> Unable to find template "/theme_basilisk/index_v1.twig" (looked into: C:\laragon\www\app.ai-pro.org\btapp\app\Views/twig).
in VENDORPATH\twig\twig\src\Loader\FilesystemLoader.php on line 227.
 1 VENDORPATH\twig\twig\src\Loader\FilesystemLoader.php(131): Twig\Loader\FilesystemLoader->findTemplate('/theme_basilisk/index_v1.twig')
 2 VENDORPATH\twig\twig\src\Environment.php(261): Twig\Loader\FilesystemLoader->getCacheKey('/theme_basilisk/index_v1.twig')
 3 VENDORPATH\twig\twig\src\Environment.php(309): Twig\Environment->getTemplateClass('/theme_basilisk/index_v1.twig')
 4 VENDORPATH\twig\twig\src\Environment.php(277): Twig\Environment->load('/theme_basilisk/index_v1.twig')
 5 VENDORPATH\btapp\btcore\src\TemplateEngine\TwigCustom.php(39): Twig\Environment->render('/theme_basilisk/index_v1.twig', [...])
 6 VENDORPATH\btapp\btcore\src\TemplateEngine.php(28): BTCore\TemplateEngine\TwigCustom->view('/theme_basilisk/index_v1.twig', [...])
 7 APPPATH\Controllers\FlowPages\LandingPage.php(168): BTCore\TemplateEngine->view('/theme_basilisk/index_v1.twig', [...])
 8 APPPATH\Controllers\FlowPages\LandingPage.php(62): App\Controllers\FlowPages\LandingPage->theme_basilisk()
 9 APPPATH\Controllers\FlowPages\LandingPage.php(80): App\Controllers\FlowPages\LandingPage->index()
10 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\FlowPages\LandingPage->show_404()
11 SYSTEMPATH\CodeIgniter.php(961): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\FlowPages\LandingPage))
12 SYSTEMPATH\CodeIgniter.php(385): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
13 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
14 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-06-11 07:02:12 --> REQ ----------->
INFO - 2025-06-11 07:02:12 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
CRITICAL - 2025-06-11 07:02:18 --> Unable to find template "/theme_basilisk/index_v1.twig" (looked into: C:\laragon\www\app.ai-pro.org\btapp\app\Views/twig).
in VENDORPATH\twig\twig\src\Loader\FilesystemLoader.php on line 227.
 1 VENDORPATH\twig\twig\src\Loader\FilesystemLoader.php(131): Twig\Loader\FilesystemLoader->findTemplate('/theme_basilisk/index_v1.twig')
 2 VENDORPATH\twig\twig\src\Environment.php(261): Twig\Loader\FilesystemLoader->getCacheKey('/theme_basilisk/index_v1.twig')
 3 VENDORPATH\twig\twig\src\Environment.php(309): Twig\Environment->getTemplateClass('/theme_basilisk/index_v1.twig')
 4 VENDORPATH\twig\twig\src\Environment.php(277): Twig\Environment->load('/theme_basilisk/index_v1.twig')
 5 VENDORPATH\btapp\btcore\src\TemplateEngine\TwigCustom.php(39): Twig\Environment->render('/theme_basilisk/index_v1.twig', [...])
 6 VENDORPATH\btapp\btcore\src\TemplateEngine.php(28): BTCore\TemplateEngine\TwigCustom->view('/theme_basilisk/index_v1.twig', [...])
 7 APPPATH\Controllers\FlowPages\LandingPage.php(168): BTCore\TemplateEngine->view('/theme_basilisk/index_v1.twig', [...])
 8 APPPATH\Controllers\FlowPages\LandingPage.php(62): App\Controllers\FlowPages\LandingPage->theme_basilisk()
 9 APPPATH\Controllers\FlowPages\LandingPage.php(80): App\Controllers\FlowPages\LandingPage->index()
10 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\FlowPages\LandingPage->show_404()
11 SYSTEMPATH\CodeIgniter.php(961): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\FlowPages\LandingPage))
12 SYSTEMPATH\CodeIgniter.php(385): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
13 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
14 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
CRITICAL - 2025-06-11 07:02:24 --> Unable to find template "/theme_basilisk/index_v1.twig" (looked into: C:\laragon\www\app.ai-pro.org\btapp\app\Views/twig).
in VENDORPATH\twig\twig\src\Loader\FilesystemLoader.php on line 227.
 1 VENDORPATH\twig\twig\src\Loader\FilesystemLoader.php(131): Twig\Loader\FilesystemLoader->findTemplate('/theme_basilisk/index_v1.twig')
 2 VENDORPATH\twig\twig\src\Environment.php(261): Twig\Loader\FilesystemLoader->getCacheKey('/theme_basilisk/index_v1.twig')
 3 VENDORPATH\twig\twig\src\Environment.php(309): Twig\Environment->getTemplateClass('/theme_basilisk/index_v1.twig')
 4 VENDORPATH\twig\twig\src\Environment.php(277): Twig\Environment->load('/theme_basilisk/index_v1.twig')
 5 VENDORPATH\btapp\btcore\src\TemplateEngine\TwigCustom.php(39): Twig\Environment->render('/theme_basilisk/index_v1.twig', [...])
 6 VENDORPATH\btapp\btcore\src\TemplateEngine.php(28): BTCore\TemplateEngine\TwigCustom->view('/theme_basilisk/index_v1.twig', [...])
 7 APPPATH\Controllers\FlowPages\LandingPage.php(168): BTCore\TemplateEngine->view('/theme_basilisk/index_v1.twig', [...])
 8 APPPATH\Controllers\FlowPages\LandingPage.php(62): App\Controllers\FlowPages\LandingPage->theme_basilisk()
 9 APPPATH\Controllers\FlowPages\LandingPage.php(80): App\Controllers\FlowPages\LandingPage->index()
10 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\FlowPages\LandingPage->show_404()
11 SYSTEMPATH\CodeIgniter.php(961): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\FlowPages\LandingPage))
12 SYSTEMPATH\CodeIgniter.php(385): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
13 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
14 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
CRITICAL - 2025-06-11 07:02:30 --> Unable to find template "/theme_basilisk/index_v1.twig" (looked into: C:\laragon\www\app.ai-pro.org\btapp\app\Views/twig).
in VENDORPATH\twig\twig\src\Loader\FilesystemLoader.php on line 227.
 1 VENDORPATH\twig\twig\src\Loader\FilesystemLoader.php(131): Twig\Loader\FilesystemLoader->findTemplate('/theme_basilisk/index_v1.twig')
 2 VENDORPATH\twig\twig\src\Environment.php(261): Twig\Loader\FilesystemLoader->getCacheKey('/theme_basilisk/index_v1.twig')
 3 VENDORPATH\twig\twig\src\Environment.php(309): Twig\Environment->getTemplateClass('/theme_basilisk/index_v1.twig')
 4 VENDORPATH\twig\twig\src\Environment.php(277): Twig\Environment->load('/theme_basilisk/index_v1.twig')
 5 VENDORPATH\btapp\btcore\src\TemplateEngine\TwigCustom.php(39): Twig\Environment->render('/theme_basilisk/index_v1.twig', [...])
 6 VENDORPATH\btapp\btcore\src\TemplateEngine.php(28): BTCore\TemplateEngine\TwigCustom->view('/theme_basilisk/index_v1.twig', [...])
 7 APPPATH\Controllers\FlowPages\LandingPage.php(168): BTCore\TemplateEngine->view('/theme_basilisk/index_v1.twig', [...])
 8 APPPATH\Controllers\FlowPages\LandingPage.php(62): App\Controllers\FlowPages\LandingPage->theme_basilisk()
 9 APPPATH\Controllers\FlowPages\LandingPage.php(80): App\Controllers\FlowPages\LandingPage->index()
10 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\FlowPages\LandingPage->show_404()
11 SYSTEMPATH\CodeIgniter.php(961): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\FlowPages\LandingPage))
12 SYSTEMPATH\CodeIgniter.php(385): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
13 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
14 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
CRITICAL - 2025-06-11 07:17:43 --> Unable to find template "/theme_basilisk/index_v1.twig" (looked into: C:\laragon\www\app.ai-pro.org\btapp\app\Views/twig).
in VENDORPATH\twig\twig\src\Loader\FilesystemLoader.php on line 227.
 1 VENDORPATH\twig\twig\src\Loader\FilesystemLoader.php(131): Twig\Loader\FilesystemLoader->findTemplate('/theme_basilisk/index_v1.twig')
 2 VENDORPATH\twig\twig\src\Environment.php(261): Twig\Loader\FilesystemLoader->getCacheKey('/theme_basilisk/index_v1.twig')
 3 VENDORPATH\twig\twig\src\Environment.php(309): Twig\Environment->getTemplateClass('/theme_basilisk/index_v1.twig')
 4 VENDORPATH\twig\twig\src\Environment.php(277): Twig\Environment->load('/theme_basilisk/index_v1.twig')
 5 VENDORPATH\btapp\btcore\src\TemplateEngine\TwigCustom.php(39): Twig\Environment->render('/theme_basilisk/index_v1.twig', [...])
 6 VENDORPATH\btapp\btcore\src\TemplateEngine.php(28): BTCore\TemplateEngine\TwigCustom->view('/theme_basilisk/index_v1.twig', [...])
 7 APPPATH\Controllers\FlowPages\LandingPage.php(168): BTCore\TemplateEngine->view('/theme_basilisk/index_v1.twig', [...])
 8 APPPATH\Controllers\FlowPages\LandingPage.php(62): App\Controllers\FlowPages\LandingPage->theme_basilisk()
 9 APPPATH\Controllers\FlowPages\LandingPage.php(80): App\Controllers\FlowPages\LandingPage->index()
10 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\FlowPages\LandingPage->show_404()
11 SYSTEMPATH\CodeIgniter.php(961): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\FlowPages\LandingPage))
12 SYSTEMPATH\CodeIgniter.php(385): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
13 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
14 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
CRITICAL - 2025-06-11 07:18:10 --> Unable to find template "/theme_basilisk/index_v1.twig" (looked into: C:\laragon\www\app.ai-pro.org\btapp\app\Views/twig).
in VENDORPATH\twig\twig\src\Loader\FilesystemLoader.php on line 227.
 1 VENDORPATH\twig\twig\src\Loader\FilesystemLoader.php(131): Twig\Loader\FilesystemLoader->findTemplate('/theme_basilisk/index_v1.twig')
 2 VENDORPATH\twig\twig\src\Environment.php(261): Twig\Loader\FilesystemLoader->getCacheKey('/theme_basilisk/index_v1.twig')
 3 VENDORPATH\twig\twig\src\Environment.php(309): Twig\Environment->getTemplateClass('/theme_basilisk/index_v1.twig')
 4 VENDORPATH\twig\twig\src\Environment.php(277): Twig\Environment->load('/theme_basilisk/index_v1.twig')
 5 VENDORPATH\btapp\btcore\src\TemplateEngine\TwigCustom.php(39): Twig\Environment->render('/theme_basilisk/index_v1.twig', [...])
 6 VENDORPATH\btapp\btcore\src\TemplateEngine.php(28): BTCore\TemplateEngine\TwigCustom->view('/theme_basilisk/index_v1.twig', [...])
 7 APPPATH\Controllers\FlowPages\LandingPage.php(168): BTCore\TemplateEngine->view('/theme_basilisk/index_v1.twig', [...])
 8 APPPATH\Controllers\FlowPages\LandingPage.php(62): App\Controllers\FlowPages\LandingPage->theme_basilisk()
 9 APPPATH\Controllers\FlowPages\LandingPage.php(80): App\Controllers\FlowPages\LandingPage->index()
10 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\FlowPages\LandingPage->show_404()
11 SYSTEMPATH\CodeIgniter.php(961): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\FlowPages\LandingPage))
12 SYSTEMPATH\CodeIgniter.php(385): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
13 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
14 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
CRITICAL - 2025-06-11 07:19:15 --> Unable to find template "/theme_basilisk/index_v1.twig" (looked into: C:\laragon\www\app.ai-pro.org\btapp\app\Views/twig).
in VENDORPATH\twig\twig\src\Loader\FilesystemLoader.php on line 227.
 1 VENDORPATH\twig\twig\src\Loader\FilesystemLoader.php(131): Twig\Loader\FilesystemLoader->findTemplate('/theme_basilisk/index_v1.twig')
 2 VENDORPATH\twig\twig\src\Environment.php(261): Twig\Loader\FilesystemLoader->getCacheKey('/theme_basilisk/index_v1.twig')
 3 VENDORPATH\twig\twig\src\Environment.php(309): Twig\Environment->getTemplateClass('/theme_basilisk/index_v1.twig')
 4 VENDORPATH\twig\twig\src\Environment.php(277): Twig\Environment->load('/theme_basilisk/index_v1.twig')
 5 VENDORPATH\btapp\btcore\src\TemplateEngine\TwigCustom.php(39): Twig\Environment->render('/theme_basilisk/index_v1.twig', [...])
 6 VENDORPATH\btapp\btcore\src\TemplateEngine.php(28): BTCore\TemplateEngine\TwigCustom->view('/theme_basilisk/index_v1.twig', [...])
 7 APPPATH\Controllers\FlowPages\LandingPage.php(168): BTCore\TemplateEngine->view('/theme_basilisk/index_v1.twig', [...])
 8 APPPATH\Controllers\FlowPages\LandingPage.php(62): App\Controllers\FlowPages\LandingPage->theme_basilisk()
 9 APPPATH\Controllers\FlowPages\LandingPage.php(80): App\Controllers\FlowPages\LandingPage->index()
10 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\FlowPages\LandingPage->show_404()
11 SYSTEMPATH\CodeIgniter.php(961): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\FlowPages\LandingPage))
12 SYSTEMPATH\CodeIgniter.php(385): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
13 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
14 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
CRITICAL - 2025-06-11 07:19:16 --> Unable to find template "/theme_basilisk/index_v1.twig" (looked into: C:\laragon\www\app.ai-pro.org\btapp\app\Views/twig).
in VENDORPATH\twig\twig\src\Loader\FilesystemLoader.php on line 227.
 1 VENDORPATH\twig\twig\src\Loader\FilesystemLoader.php(131): Twig\Loader\FilesystemLoader->findTemplate('/theme_basilisk/index_v1.twig')
 2 VENDORPATH\twig\twig\src\Environment.php(261): Twig\Loader\FilesystemLoader->getCacheKey('/theme_basilisk/index_v1.twig')
 3 VENDORPATH\twig\twig\src\Environment.php(309): Twig\Environment->getTemplateClass('/theme_basilisk/index_v1.twig')
 4 VENDORPATH\twig\twig\src\Environment.php(277): Twig\Environment->load('/theme_basilisk/index_v1.twig')
 5 VENDORPATH\btapp\btcore\src\TemplateEngine\TwigCustom.php(39): Twig\Environment->render('/theme_basilisk/index_v1.twig', [...])
 6 VENDORPATH\btapp\btcore\src\TemplateEngine.php(28): BTCore\TemplateEngine\TwigCustom->view('/theme_basilisk/index_v1.twig', [...])
 7 APPPATH\Controllers\FlowPages\LandingPage.php(168): BTCore\TemplateEngine->view('/theme_basilisk/index_v1.twig', [...])
 8 APPPATH\Controllers\FlowPages\LandingPage.php(62): App\Controllers\FlowPages\LandingPage->theme_basilisk()
 9 APPPATH\Controllers\FlowPages\LandingPage.php(80): App\Controllers\FlowPages\LandingPage->index()
10 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\FlowPages\LandingPage->show_404()
11 SYSTEMPATH\CodeIgniter.php(961): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\FlowPages\LandingPage))
12 SYSTEMPATH\CodeIgniter.php(385): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
13 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
14 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
CRITICAL - 2025-06-11 07:19:30 --> Unable to find template "/theme_basilisk/index_v1.twig" (looked into: C:\laragon\www\app.ai-pro.org\btapp\app\Views/twig).
in VENDORPATH\twig\twig\src\Loader\FilesystemLoader.php on line 227.
 1 VENDORPATH\twig\twig\src\Loader\FilesystemLoader.php(131): Twig\Loader\FilesystemLoader->findTemplate('/theme_basilisk/index_v1.twig')
 2 VENDORPATH\twig\twig\src\Environment.php(261): Twig\Loader\FilesystemLoader->getCacheKey('/theme_basilisk/index_v1.twig')
 3 VENDORPATH\twig\twig\src\Environment.php(309): Twig\Environment->getTemplateClass('/theme_basilisk/index_v1.twig')
 4 VENDORPATH\twig\twig\src\Environment.php(277): Twig\Environment->load('/theme_basilisk/index_v1.twig')
 5 VENDORPATH\btapp\btcore\src\TemplateEngine\TwigCustom.php(39): Twig\Environment->render('/theme_basilisk/index_v1.twig', [...])
 6 VENDORPATH\btapp\btcore\src\TemplateEngine.php(28): BTCore\TemplateEngine\TwigCustom->view('/theme_basilisk/index_v1.twig', [...])
 7 APPPATH\Controllers\FlowPages\LandingPage.php(168): BTCore\TemplateEngine->view('/theme_basilisk/index_v1.twig', [...])
 8 APPPATH\Controllers\FlowPages\LandingPage.php(62): App\Controllers\FlowPages\LandingPage->theme_basilisk()
 9 APPPATH\Controllers\FlowPages\LandingPage.php(80): App\Controllers\FlowPages\LandingPage->index()
10 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\FlowPages\LandingPage->show_404()
11 SYSTEMPATH\CodeIgniter.php(961): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\FlowPages\LandingPage))
12 SYSTEMPATH\CodeIgniter.php(385): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
13 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
14 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
CRITICAL - 2025-06-11 07:19:31 --> Unable to find template "/theme_basilisk/index_v1.twig" (looked into: C:\laragon\www\app.ai-pro.org\btapp\app\Views/twig).
in VENDORPATH\twig\twig\src\Loader\FilesystemLoader.php on line 227.
 1 VENDORPATH\twig\twig\src\Loader\FilesystemLoader.php(131): Twig\Loader\FilesystemLoader->findTemplate('/theme_basilisk/index_v1.twig')
 2 VENDORPATH\twig\twig\src\Environment.php(261): Twig\Loader\FilesystemLoader->getCacheKey('/theme_basilisk/index_v1.twig')
 3 VENDORPATH\twig\twig\src\Environment.php(309): Twig\Environment->getTemplateClass('/theme_basilisk/index_v1.twig')
 4 VENDORPATH\twig\twig\src\Environment.php(277): Twig\Environment->load('/theme_basilisk/index_v1.twig')
 5 VENDORPATH\btapp\btcore\src\TemplateEngine\TwigCustom.php(39): Twig\Environment->render('/theme_basilisk/index_v1.twig', [...])
 6 VENDORPATH\btapp\btcore\src\TemplateEngine.php(28): BTCore\TemplateEngine\TwigCustom->view('/theme_basilisk/index_v1.twig', [...])
 7 APPPATH\Controllers\FlowPages\LandingPage.php(168): BTCore\TemplateEngine->view('/theme_basilisk/index_v1.twig', [...])
 8 APPPATH\Controllers\FlowPages\LandingPage.php(62): App\Controllers\FlowPages\LandingPage->theme_basilisk()
 9 APPPATH\Controllers\FlowPages\LandingPage.php(80): App\Controllers\FlowPages\LandingPage->index()
10 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\FlowPages\LandingPage->show_404()
11 SYSTEMPATH\CodeIgniter.php(961): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\FlowPages\LandingPage))
12 SYSTEMPATH\CodeIgniter.php(385): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
13 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
14 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
CRITICAL - 2025-06-11 07:20:16 --> Unable to find template "/theme_basilisk/index_v1.twig" (looked into: C:\laragon\www\app.ai-pro.org\btapp\app\Views/twig).
in VENDORPATH\twig\twig\src\Loader\FilesystemLoader.php on line 227.
 1 VENDORPATH\twig\twig\src\Loader\FilesystemLoader.php(131): Twig\Loader\FilesystemLoader->findTemplate('/theme_basilisk/index_v1.twig')
 2 VENDORPATH\twig\twig\src\Environment.php(261): Twig\Loader\FilesystemLoader->getCacheKey('/theme_basilisk/index_v1.twig')
 3 VENDORPATH\twig\twig\src\Environment.php(309): Twig\Environment->getTemplateClass('/theme_basilisk/index_v1.twig')
 4 VENDORPATH\twig\twig\src\Environment.php(277): Twig\Environment->load('/theme_basilisk/index_v1.twig')
 5 VENDORPATH\btapp\btcore\src\TemplateEngine\TwigCustom.php(39): Twig\Environment->render('/theme_basilisk/index_v1.twig', [...])
 6 VENDORPATH\btapp\btcore\src\TemplateEngine.php(28): BTCore\TemplateEngine\TwigCustom->view('/theme_basilisk/index_v1.twig', [...])
 7 APPPATH\Controllers\FlowPages\LandingPage.php(168): BTCore\TemplateEngine->view('/theme_basilisk/index_v1.twig', [...])
 8 APPPATH\Controllers\FlowPages\LandingPage.php(62): App\Controllers\FlowPages\LandingPage->theme_basilisk()
 9 APPPATH\Controllers\FlowPages\LandingPage.php(80): App\Controllers\FlowPages\LandingPage->index()
10 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\FlowPages\LandingPage->show_404()
11 SYSTEMPATH\CodeIgniter.php(961): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\FlowPages\LandingPage))
12 SYSTEMPATH\CodeIgniter.php(385): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
13 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
14 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-06-11 07:20:17 --> REQ ----------->
INFO - 2025-06-11 07:20:17 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
CRITICAL - 2025-06-11 07:20:17 --> Unable to find template "/theme_basilisk/index_v1.twig" (looked into: C:\laragon\www\app.ai-pro.org\btapp\app\Views/twig).
in VENDORPATH\twig\twig\src\Loader\FilesystemLoader.php on line 227.
 1 VENDORPATH\twig\twig\src\Loader\FilesystemLoader.php(131): Twig\Loader\FilesystemLoader->findTemplate('/theme_basilisk/index_v1.twig')
 2 VENDORPATH\twig\twig\src\Environment.php(261): Twig\Loader\FilesystemLoader->getCacheKey('/theme_basilisk/index_v1.twig')
 3 VENDORPATH\twig\twig\src\Environment.php(309): Twig\Environment->getTemplateClass('/theme_basilisk/index_v1.twig')
 4 VENDORPATH\twig\twig\src\Environment.php(277): Twig\Environment->load('/theme_basilisk/index_v1.twig')
 5 VENDORPATH\btapp\btcore\src\TemplateEngine\TwigCustom.php(39): Twig\Environment->render('/theme_basilisk/index_v1.twig', [...])
 6 VENDORPATH\btapp\btcore\src\TemplateEngine.php(28): BTCore\TemplateEngine\TwigCustom->view('/theme_basilisk/index_v1.twig', [...])
 7 APPPATH\Controllers\FlowPages\LandingPage.php(168): BTCore\TemplateEngine->view('/theme_basilisk/index_v1.twig', [...])
 8 APPPATH\Controllers\FlowPages\LandingPage.php(62): App\Controllers\FlowPages\LandingPage->theme_basilisk()
 9 APPPATH\Controllers\FlowPages\LandingPage.php(80): App\Controllers\FlowPages\LandingPage->index()
10 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\FlowPages\LandingPage->show_404()
11 SYSTEMPATH\CodeIgniter.php(961): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\FlowPages\LandingPage))
12 SYSTEMPATH\CodeIgniter.php(385): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
13 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
14 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-06-11 07:20:17 --> REQ ----------->
INFO - 2025-06-11 07:20:17 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
CRITICAL - 2025-06-11 07:20:17 --> Unable to find template "/theme_basilisk/index_v1.twig" (looked into: C:\laragon\www\app.ai-pro.org\btapp\app\Views/twig).
in VENDORPATH\twig\twig\src\Loader\FilesystemLoader.php on line 227.
 1 VENDORPATH\twig\twig\src\Loader\FilesystemLoader.php(131): Twig\Loader\FilesystemLoader->findTemplate('/theme_basilisk/index_v1.twig')
 2 VENDORPATH\twig\twig\src\Environment.php(261): Twig\Loader\FilesystemLoader->getCacheKey('/theme_basilisk/index_v1.twig')
 3 VENDORPATH\twig\twig\src\Environment.php(309): Twig\Environment->getTemplateClass('/theme_basilisk/index_v1.twig')
 4 VENDORPATH\twig\twig\src\Environment.php(277): Twig\Environment->load('/theme_basilisk/index_v1.twig')
 5 VENDORPATH\btapp\btcore\src\TemplateEngine\TwigCustom.php(39): Twig\Environment->render('/theme_basilisk/index_v1.twig', [...])
 6 VENDORPATH\btapp\btcore\src\TemplateEngine.php(28): BTCore\TemplateEngine\TwigCustom->view('/theme_basilisk/index_v1.twig', [...])
 7 APPPATH\Controllers\FlowPages\LandingPage.php(168): BTCore\TemplateEngine->view('/theme_basilisk/index_v1.twig', [...])
 8 APPPATH\Controllers\FlowPages\LandingPage.php(62): App\Controllers\FlowPages\LandingPage->theme_basilisk()
 9 APPPATH\Controllers\FlowPages\LandingPage.php(80): App\Controllers\FlowPages\LandingPage->index()
10 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\FlowPages\LandingPage->show_404()
11 SYSTEMPATH\CodeIgniter.php(961): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\FlowPages\LandingPage))
12 SYSTEMPATH\CodeIgniter.php(385): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
13 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
14 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
CRITICAL - 2025-06-11 07:20:19 --> Unable to find template "/theme_basilisk/index_v1.twig" (looked into: C:\laragon\www\app.ai-pro.org\btapp\app\Views/twig).
in VENDORPATH\twig\twig\src\Loader\FilesystemLoader.php on line 227.
 1 VENDORPATH\twig\twig\src\Loader\FilesystemLoader.php(131): Twig\Loader\FilesystemLoader->findTemplate('/theme_basilisk/index_v1.twig')
 2 VENDORPATH\twig\twig\src\Environment.php(261): Twig\Loader\FilesystemLoader->getCacheKey('/theme_basilisk/index_v1.twig')
 3 VENDORPATH\twig\twig\src\Environment.php(309): Twig\Environment->getTemplateClass('/theme_basilisk/index_v1.twig')
 4 VENDORPATH\twig\twig\src\Environment.php(277): Twig\Environment->load('/theme_basilisk/index_v1.twig')
 5 VENDORPATH\btapp\btcore\src\TemplateEngine\TwigCustom.php(39): Twig\Environment->render('/theme_basilisk/index_v1.twig', [...])
 6 VENDORPATH\btapp\btcore\src\TemplateEngine.php(28): BTCore\TemplateEngine\TwigCustom->view('/theme_basilisk/index_v1.twig', [...])
 7 APPPATH\Controllers\FlowPages\LandingPage.php(168): BTCore\TemplateEngine->view('/theme_basilisk/index_v1.twig', [...])
 8 APPPATH\Controllers\FlowPages\LandingPage.php(62): App\Controllers\FlowPages\LandingPage->theme_basilisk()
 9 APPPATH\Controllers\FlowPages\LandingPage.php(80): App\Controllers\FlowPages\LandingPage->index()
10 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\FlowPages\LandingPage->show_404()
11 SYSTEMPATH\CodeIgniter.php(961): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\FlowPages\LandingPage))
12 SYSTEMPATH\CodeIgniter.php(385): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
13 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
14 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
CRITICAL - 2025-06-11 07:20:21 --> Unable to find template "/theme_basilisk/index_v1.twig" (looked into: C:\laragon\www\app.ai-pro.org\btapp\app\Views/twig).
in VENDORPATH\twig\twig\src\Loader\FilesystemLoader.php on line 227.
 1 VENDORPATH\twig\twig\src\Loader\FilesystemLoader.php(131): Twig\Loader\FilesystemLoader->findTemplate('/theme_basilisk/index_v1.twig')
 2 VENDORPATH\twig\twig\src\Environment.php(261): Twig\Loader\FilesystemLoader->getCacheKey('/theme_basilisk/index_v1.twig')
 3 VENDORPATH\twig\twig\src\Environment.php(309): Twig\Environment->getTemplateClass('/theme_basilisk/index_v1.twig')
 4 VENDORPATH\twig\twig\src\Environment.php(277): Twig\Environment->load('/theme_basilisk/index_v1.twig')
 5 VENDORPATH\btapp\btcore\src\TemplateEngine\TwigCustom.php(39): Twig\Environment->render('/theme_basilisk/index_v1.twig', [...])
 6 VENDORPATH\btapp\btcore\src\TemplateEngine.php(28): BTCore\TemplateEngine\TwigCustom->view('/theme_basilisk/index_v1.twig', [...])
 7 APPPATH\Controllers\FlowPages\LandingPage.php(168): BTCore\TemplateEngine->view('/theme_basilisk/index_v1.twig', [...])
 8 APPPATH\Controllers\FlowPages\LandingPage.php(62): App\Controllers\FlowPages\LandingPage->theme_basilisk()
 9 APPPATH\Controllers\FlowPages\LandingPage.php(80): App\Controllers\FlowPages\LandingPage->index()
10 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\FlowPages\LandingPage->show_404()
11 SYSTEMPATH\CodeIgniter.php(961): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\FlowPages\LandingPage))
12 SYSTEMPATH\CodeIgniter.php(385): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
13 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
14 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-06-11 07:20:22 --> REQ ----------->
INFO - 2025-06-11 07:20:22 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 07:20:24 --> REQ ----------->
INFO - 2025-06-11 07:20:24 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 07:20:24 --> REQ ----------->
INFO - 2025-06-11 07:20:24 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 07:20:25 --> REQ ----------->
INFO - 2025-06-11 07:20:25 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
CRITICAL - 2025-06-11 07:20:26 --> Unable to find template "/theme_basilisk/index_v1.twig" (looked into: C:\laragon\www\app.ai-pro.org\btapp\app\Views/twig).
in VENDORPATH\twig\twig\src\Loader\FilesystemLoader.php on line 227.
 1 VENDORPATH\twig\twig\src\Loader\FilesystemLoader.php(131): Twig\Loader\FilesystemLoader->findTemplate('/theme_basilisk/index_v1.twig')
 2 VENDORPATH\twig\twig\src\Environment.php(261): Twig\Loader\FilesystemLoader->getCacheKey('/theme_basilisk/index_v1.twig')
 3 VENDORPATH\twig\twig\src\Environment.php(309): Twig\Environment->getTemplateClass('/theme_basilisk/index_v1.twig')
 4 VENDORPATH\twig\twig\src\Environment.php(277): Twig\Environment->load('/theme_basilisk/index_v1.twig')
 5 VENDORPATH\btapp\btcore\src\TemplateEngine\TwigCustom.php(39): Twig\Environment->render('/theme_basilisk/index_v1.twig', [...])
 6 VENDORPATH\btapp\btcore\src\TemplateEngine.php(28): BTCore\TemplateEngine\TwigCustom->view('/theme_basilisk/index_v1.twig', [...])
 7 APPPATH\Controllers\FlowPages\LandingPage.php(168): BTCore\TemplateEngine->view('/theme_basilisk/index_v1.twig', [...])
 8 APPPATH\Controllers\FlowPages\LandingPage.php(62): App\Controllers\FlowPages\LandingPage->theme_basilisk()
 9 APPPATH\Controllers\FlowPages\LandingPage.php(80): App\Controllers\FlowPages\LandingPage->index()
10 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\FlowPages\LandingPage->show_404()
11 SYSTEMPATH\CodeIgniter.php(961): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\FlowPages\LandingPage))
12 SYSTEMPATH\CodeIgniter.php(385): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
13 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
14 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-06-11 07:20:26 --> REQ ----------->
INFO - 2025-06-11 07:20:26 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2025-06-11 07:20:27 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
CRITICAL - 2025-06-11 07:20:27 --> Unable to find template "/theme_basilisk/index_v1.twig" (looked into: C:\laragon\www\app.ai-pro.org\btapp\app\Views/twig).
in VENDORPATH\twig\twig\src\Loader\FilesystemLoader.php on line 227.
 1 VENDORPATH\twig\twig\src\Loader\FilesystemLoader.php(131): Twig\Loader\FilesystemLoader->findTemplate('/theme_basilisk/index_v1.twig')
 2 VENDORPATH\twig\twig\src\Environment.php(261): Twig\Loader\FilesystemLoader->getCacheKey('/theme_basilisk/index_v1.twig')
 3 VENDORPATH\twig\twig\src\Environment.php(309): Twig\Environment->getTemplateClass('/theme_basilisk/index_v1.twig')
 4 VENDORPATH\twig\twig\src\Environment.php(277): Twig\Environment->load('/theme_basilisk/index_v1.twig')
 5 VENDORPATH\btapp\btcore\src\TemplateEngine\TwigCustom.php(39): Twig\Environment->render('/theme_basilisk/index_v1.twig', [...])
 6 VENDORPATH\btapp\btcore\src\TemplateEngine.php(28): BTCore\TemplateEngine\TwigCustom->view('/theme_basilisk/index_v1.twig', [...])
 7 APPPATH\Controllers\FlowPages\Pricing.php(308): BTCore\TemplateEngine->view('/theme_basilisk/index_v1.twig', [...])
 8 APPPATH\Controllers\FlowPages\Pricing.php(274): App\Controllers\FlowPages\Pricing->theme_basilisk()
 9 APPPATH\Controllers\FlowPages\Pricing.php(83): App\Controllers\FlowPages\Pricing->switch_theme()
10 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\FlowPages\Pricing->index()
11 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\FlowPages\Pricing))
12 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
13 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
14 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-06-11 07:20:31 --> REQ ----------->
INFO - 2025-06-11 07:20:31 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
CRITICAL - 2025-06-11 07:20:31 --> Unable to find template "/theme_basilisk/index_v1.twig" (looked into: C:\laragon\www\app.ai-pro.org\btapp\app\Views/twig).
in VENDORPATH\twig\twig\src\Loader\FilesystemLoader.php on line 227.
 1 VENDORPATH\twig\twig\src\Loader\FilesystemLoader.php(131): Twig\Loader\FilesystemLoader->findTemplate('/theme_basilisk/index_v1.twig')
 2 VENDORPATH\twig\twig\src\Environment.php(261): Twig\Loader\FilesystemLoader->getCacheKey('/theme_basilisk/index_v1.twig')
 3 VENDORPATH\twig\twig\src\Environment.php(309): Twig\Environment->getTemplateClass('/theme_basilisk/index_v1.twig')
 4 VENDORPATH\twig\twig\src\Environment.php(277): Twig\Environment->load('/theme_basilisk/index_v1.twig')
 5 VENDORPATH\btapp\btcore\src\TemplateEngine\TwigCustom.php(39): Twig\Environment->render('/theme_basilisk/index_v1.twig', [...])
 6 VENDORPATH\btapp\btcore\src\TemplateEngine.php(28): BTCore\TemplateEngine\TwigCustom->view('/theme_basilisk/index_v1.twig', [...])
 7 APPPATH\Controllers\FlowPages\LandingPage.php(168): BTCore\TemplateEngine->view('/theme_basilisk/index_v1.twig', [...])
 8 APPPATH\Controllers\FlowPages\LandingPage.php(62): App\Controllers\FlowPages\LandingPage->theme_basilisk()
 9 APPPATH\Controllers\FlowPages\LandingPage.php(80): App\Controllers\FlowPages\LandingPage->index()
10 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\FlowPages\LandingPage->show_404()
11 SYSTEMPATH\CodeIgniter.php(961): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\FlowPages\LandingPage))
12 SYSTEMPATH\CodeIgniter.php(385): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
13 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
14 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
CRITICAL - 2025-06-11 07:20:36 --> Unable to find template "/theme_basilisk/index_v1.twig" (looked into: C:\laragon\www\app.ai-pro.org\btapp\app\Views/twig).
in VENDORPATH\twig\twig\src\Loader\FilesystemLoader.php on line 227.
 1 VENDORPATH\twig\twig\src\Loader\FilesystemLoader.php(131): Twig\Loader\FilesystemLoader->findTemplate('/theme_basilisk/index_v1.twig')
 2 VENDORPATH\twig\twig\src\Environment.php(261): Twig\Loader\FilesystemLoader->getCacheKey('/theme_basilisk/index_v1.twig')
 3 VENDORPATH\twig\twig\src\Environment.php(309): Twig\Environment->getTemplateClass('/theme_basilisk/index_v1.twig')
 4 VENDORPATH\twig\twig\src\Environment.php(277): Twig\Environment->load('/theme_basilisk/index_v1.twig')
 5 VENDORPATH\btapp\btcore\src\TemplateEngine\TwigCustom.php(39): Twig\Environment->render('/theme_basilisk/index_v1.twig', [...])
 6 VENDORPATH\btapp\btcore\src\TemplateEngine.php(28): BTCore\TemplateEngine\TwigCustom->view('/theme_basilisk/index_v1.twig', [...])
 7 APPPATH\Controllers\FlowPages\LandingPage.php(168): BTCore\TemplateEngine->view('/theme_basilisk/index_v1.twig', [...])
 8 APPPATH\Controllers\FlowPages\LandingPage.php(62): App\Controllers\FlowPages\LandingPage->theme_basilisk()
 9 APPPATH\Controllers\FlowPages\LandingPage.php(80): App\Controllers\FlowPages\LandingPage->index()
10 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\FlowPages\LandingPage->show_404()
11 SYSTEMPATH\CodeIgniter.php(961): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\FlowPages\LandingPage))
12 SYSTEMPATH\CodeIgniter.php(385): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
13 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
14 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
CRITICAL - 2025-06-11 07:20:42 --> Unable to find template "/theme_basilisk/index_v1.twig" (looked into: C:\laragon\www\app.ai-pro.org\btapp\app\Views/twig).
in VENDORPATH\twig\twig\src\Loader\FilesystemLoader.php on line 227.
 1 VENDORPATH\twig\twig\src\Loader\FilesystemLoader.php(131): Twig\Loader\FilesystemLoader->findTemplate('/theme_basilisk/index_v1.twig')
 2 VENDORPATH\twig\twig\src\Environment.php(261): Twig\Loader\FilesystemLoader->getCacheKey('/theme_basilisk/index_v1.twig')
 3 VENDORPATH\twig\twig\src\Environment.php(309): Twig\Environment->getTemplateClass('/theme_basilisk/index_v1.twig')
 4 VENDORPATH\twig\twig\src\Environment.php(277): Twig\Environment->load('/theme_basilisk/index_v1.twig')
 5 VENDORPATH\btapp\btcore\src\TemplateEngine\TwigCustom.php(39): Twig\Environment->render('/theme_basilisk/index_v1.twig', [...])
 6 VENDORPATH\btapp\btcore\src\TemplateEngine.php(28): BTCore\TemplateEngine\TwigCustom->view('/theme_basilisk/index_v1.twig', [...])
 7 APPPATH\Controllers\FlowPages\LandingPage.php(168): BTCore\TemplateEngine->view('/theme_basilisk/index_v1.twig', [...])
 8 APPPATH\Controllers\FlowPages\LandingPage.php(62): App\Controllers\FlowPages\LandingPage->theme_basilisk()
 9 APPPATH\Controllers\FlowPages\LandingPage.php(80): App\Controllers\FlowPages\LandingPage->index()
10 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\FlowPages\LandingPage->show_404()
11 SYSTEMPATH\CodeIgniter.php(961): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\FlowPages\LandingPage))
12 SYSTEMPATH\CodeIgniter.php(385): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
13 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
14 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
CRITICAL - 2025-06-11 07:20:47 --> Unable to find template "/theme_basilisk/index_v1.twig" (looked into: C:\laragon\www\app.ai-pro.org\btapp\app\Views/twig).
in VENDORPATH\twig\twig\src\Loader\FilesystemLoader.php on line 227.
 1 VENDORPATH\twig\twig\src\Loader\FilesystemLoader.php(131): Twig\Loader\FilesystemLoader->findTemplate('/theme_basilisk/index_v1.twig')
 2 VENDORPATH\twig\twig\src\Environment.php(261): Twig\Loader\FilesystemLoader->getCacheKey('/theme_basilisk/index_v1.twig')
 3 VENDORPATH\twig\twig\src\Environment.php(309): Twig\Environment->getTemplateClass('/theme_basilisk/index_v1.twig')
 4 VENDORPATH\twig\twig\src\Environment.php(277): Twig\Environment->load('/theme_basilisk/index_v1.twig')
 5 VENDORPATH\btapp\btcore\src\TemplateEngine\TwigCustom.php(39): Twig\Environment->render('/theme_basilisk/index_v1.twig', [...])
 6 VENDORPATH\btapp\btcore\src\TemplateEngine.php(28): BTCore\TemplateEngine\TwigCustom->view('/theme_basilisk/index_v1.twig', [...])
 7 APPPATH\Controllers\FlowPages\LandingPage.php(168): BTCore\TemplateEngine->view('/theme_basilisk/index_v1.twig', [...])
 8 APPPATH\Controllers\FlowPages\LandingPage.php(62): App\Controllers\FlowPages\LandingPage->theme_basilisk()
 9 APPPATH\Controllers\FlowPages\LandingPage.php(80): App\Controllers\FlowPages\LandingPage->index()
10 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\FlowPages\LandingPage->show_404()
11 SYSTEMPATH\CodeIgniter.php(961): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\FlowPages\LandingPage))
12 SYSTEMPATH\CodeIgniter.php(385): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
13 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
14 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
CRITICAL - 2025-06-11 07:20:52 --> Unable to find template "/theme_basilisk/index_v1.twig" (looked into: C:\laragon\www\app.ai-pro.org\btapp\app\Views/twig).
in VENDORPATH\twig\twig\src\Loader\FilesystemLoader.php on line 227.
 1 VENDORPATH\twig\twig\src\Loader\FilesystemLoader.php(131): Twig\Loader\FilesystemLoader->findTemplate('/theme_basilisk/index_v1.twig')
 2 VENDORPATH\twig\twig\src\Environment.php(261): Twig\Loader\FilesystemLoader->getCacheKey('/theme_basilisk/index_v1.twig')
 3 VENDORPATH\twig\twig\src\Environment.php(309): Twig\Environment->getTemplateClass('/theme_basilisk/index_v1.twig')
 4 VENDORPATH\twig\twig\src\Environment.php(277): Twig\Environment->load('/theme_basilisk/index_v1.twig')
 5 VENDORPATH\btapp\btcore\src\TemplateEngine\TwigCustom.php(39): Twig\Environment->render('/theme_basilisk/index_v1.twig', [...])
 6 VENDORPATH\btapp\btcore\src\TemplateEngine.php(28): BTCore\TemplateEngine\TwigCustom->view('/theme_basilisk/index_v1.twig', [...])
 7 APPPATH\Controllers\FlowPages\LandingPage.php(168): BTCore\TemplateEngine->view('/theme_basilisk/index_v1.twig', [...])
 8 APPPATH\Controllers\FlowPages\LandingPage.php(62): App\Controllers\FlowPages\LandingPage->theme_basilisk()
 9 APPPATH\Controllers\FlowPages\LandingPage.php(80): App\Controllers\FlowPages\LandingPage->index()
10 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\FlowPages\LandingPage->show_404()
11 SYSTEMPATH\CodeIgniter.php(961): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\FlowPages\LandingPage))
12 SYSTEMPATH\CodeIgniter.php(385): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
13 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
14 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
CRITICAL - 2025-06-11 07:20:57 --> Unable to find template "/theme_basilisk/index_v1.twig" (looked into: C:\laragon\www\app.ai-pro.org\btapp\app\Views/twig).
in VENDORPATH\twig\twig\src\Loader\FilesystemLoader.php on line 227.
 1 VENDORPATH\twig\twig\src\Loader\FilesystemLoader.php(131): Twig\Loader\FilesystemLoader->findTemplate('/theme_basilisk/index_v1.twig')
 2 VENDORPATH\twig\twig\src\Environment.php(261): Twig\Loader\FilesystemLoader->getCacheKey('/theme_basilisk/index_v1.twig')
 3 VENDORPATH\twig\twig\src\Environment.php(309): Twig\Environment->getTemplateClass('/theme_basilisk/index_v1.twig')
 4 VENDORPATH\twig\twig\src\Environment.php(277): Twig\Environment->load('/theme_basilisk/index_v1.twig')
 5 VENDORPATH\btapp\btcore\src\TemplateEngine\TwigCustom.php(39): Twig\Environment->render('/theme_basilisk/index_v1.twig', [...])
 6 VENDORPATH\btapp\btcore\src\TemplateEngine.php(28): BTCore\TemplateEngine\TwigCustom->view('/theme_basilisk/index_v1.twig', [...])
 7 APPPATH\Controllers\FlowPages\LandingPage.php(168): BTCore\TemplateEngine->view('/theme_basilisk/index_v1.twig', [...])
 8 APPPATH\Controllers\FlowPages\LandingPage.php(62): App\Controllers\FlowPages\LandingPage->theme_basilisk()
 9 APPPATH\Controllers\FlowPages\LandingPage.php(80): App\Controllers\FlowPages\LandingPage->index()
10 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\FlowPages\LandingPage->show_404()
11 SYSTEMPATH\CodeIgniter.php(961): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\FlowPages\LandingPage))
12 SYSTEMPATH\CodeIgniter.php(385): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
13 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
14 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
CRITICAL - 2025-06-11 07:21:02 --> Unable to find template "/theme_basilisk/index_v1.twig" (looked into: C:\laragon\www\app.ai-pro.org\btapp\app\Views/twig).
in VENDORPATH\twig\twig\src\Loader\FilesystemLoader.php on line 227.
 1 VENDORPATH\twig\twig\src\Loader\FilesystemLoader.php(131): Twig\Loader\FilesystemLoader->findTemplate('/theme_basilisk/index_v1.twig')
 2 VENDORPATH\twig\twig\src\Environment.php(261): Twig\Loader\FilesystemLoader->getCacheKey('/theme_basilisk/index_v1.twig')
 3 VENDORPATH\twig\twig\src\Environment.php(309): Twig\Environment->getTemplateClass('/theme_basilisk/index_v1.twig')
 4 VENDORPATH\twig\twig\src\Environment.php(277): Twig\Environment->load('/theme_basilisk/index_v1.twig')
 5 VENDORPATH\btapp\btcore\src\TemplateEngine\TwigCustom.php(39): Twig\Environment->render('/theme_basilisk/index_v1.twig', [...])
 6 VENDORPATH\btapp\btcore\src\TemplateEngine.php(28): BTCore\TemplateEngine\TwigCustom->view('/theme_basilisk/index_v1.twig', [...])
 7 APPPATH\Controllers\FlowPages\LandingPage.php(168): BTCore\TemplateEngine->view('/theme_basilisk/index_v1.twig', [...])
 8 APPPATH\Controllers\FlowPages\LandingPage.php(62): App\Controllers\FlowPages\LandingPage->theme_basilisk()
 9 APPPATH\Controllers\FlowPages\LandingPage.php(80): App\Controllers\FlowPages\LandingPage->index()
10 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\FlowPages\LandingPage->show_404()
11 SYSTEMPATH\CodeIgniter.php(961): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\FlowPages\LandingPage))
12 SYSTEMPATH\CodeIgniter.php(385): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
13 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
14 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
CRITICAL - 2025-06-11 07:21:07 --> Unable to find template "/theme_basilisk/index_v1.twig" (looked into: C:\laragon\www\app.ai-pro.org\btapp\app\Views/twig).
in VENDORPATH\twig\twig\src\Loader\FilesystemLoader.php on line 227.
 1 VENDORPATH\twig\twig\src\Loader\FilesystemLoader.php(131): Twig\Loader\FilesystemLoader->findTemplate('/theme_basilisk/index_v1.twig')
 2 VENDORPATH\twig\twig\src\Environment.php(261): Twig\Loader\FilesystemLoader->getCacheKey('/theme_basilisk/index_v1.twig')
 3 VENDORPATH\twig\twig\src\Environment.php(309): Twig\Environment->getTemplateClass('/theme_basilisk/index_v1.twig')
 4 VENDORPATH\twig\twig\src\Environment.php(277): Twig\Environment->load('/theme_basilisk/index_v1.twig')
 5 VENDORPATH\btapp\btcore\src\TemplateEngine\TwigCustom.php(39): Twig\Environment->render('/theme_basilisk/index_v1.twig', [...])
 6 VENDORPATH\btapp\btcore\src\TemplateEngine.php(28): BTCore\TemplateEngine\TwigCustom->view('/theme_basilisk/index_v1.twig', [...])
 7 APPPATH\Controllers\FlowPages\LandingPage.php(168): BTCore\TemplateEngine->view('/theme_basilisk/index_v1.twig', [...])
 8 APPPATH\Controllers\FlowPages\LandingPage.php(62): App\Controllers\FlowPages\LandingPage->theme_basilisk()
 9 APPPATH\Controllers\FlowPages\LandingPage.php(80): App\Controllers\FlowPages\LandingPage->index()
10 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\FlowPages\LandingPage->show_404()
11 SYSTEMPATH\CodeIgniter.php(961): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\FlowPages\LandingPage))
12 SYSTEMPATH\CodeIgniter.php(385): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
13 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
14 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
CRITICAL - 2025-06-11 07:21:12 --> Unable to find template "/theme_basilisk/index_v1.twig" (looked into: C:\laragon\www\app.ai-pro.org\btapp\app\Views/twig).
in VENDORPATH\twig\twig\src\Loader\FilesystemLoader.php on line 227.
 1 VENDORPATH\twig\twig\src\Loader\FilesystemLoader.php(131): Twig\Loader\FilesystemLoader->findTemplate('/theme_basilisk/index_v1.twig')
 2 VENDORPATH\twig\twig\src\Environment.php(261): Twig\Loader\FilesystemLoader->getCacheKey('/theme_basilisk/index_v1.twig')
 3 VENDORPATH\twig\twig\src\Environment.php(309): Twig\Environment->getTemplateClass('/theme_basilisk/index_v1.twig')
 4 VENDORPATH\twig\twig\src\Environment.php(277): Twig\Environment->load('/theme_basilisk/index_v1.twig')
 5 VENDORPATH\btapp\btcore\src\TemplateEngine\TwigCustom.php(39): Twig\Environment->render('/theme_basilisk/index_v1.twig', [...])
 6 VENDORPATH\btapp\btcore\src\TemplateEngine.php(28): BTCore\TemplateEngine\TwigCustom->view('/theme_basilisk/index_v1.twig', [...])
 7 APPPATH\Controllers\FlowPages\LandingPage.php(168): BTCore\TemplateEngine->view('/theme_basilisk/index_v1.twig', [...])
 8 APPPATH\Controllers\FlowPages\LandingPage.php(62): App\Controllers\FlowPages\LandingPage->theme_basilisk()
 9 APPPATH\Controllers\FlowPages\LandingPage.php(80): App\Controllers\FlowPages\LandingPage->index()
10 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\FlowPages\LandingPage->show_404()
11 SYSTEMPATH\CodeIgniter.php(961): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\FlowPages\LandingPage))
12 SYSTEMPATH\CodeIgniter.php(385): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
13 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
14 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
CRITICAL - 2025-06-11 07:21:17 --> Unable to find template "/theme_basilisk/index_v1.twig" (looked into: C:\laragon\www\app.ai-pro.org\btapp\app\Views/twig).
in VENDORPATH\twig\twig\src\Loader\FilesystemLoader.php on line 227.
 1 VENDORPATH\twig\twig\src\Loader\FilesystemLoader.php(131): Twig\Loader\FilesystemLoader->findTemplate('/theme_basilisk/index_v1.twig')
 2 VENDORPATH\twig\twig\src\Environment.php(261): Twig\Loader\FilesystemLoader->getCacheKey('/theme_basilisk/index_v1.twig')
 3 VENDORPATH\twig\twig\src\Environment.php(309): Twig\Environment->getTemplateClass('/theme_basilisk/index_v1.twig')
 4 VENDORPATH\twig\twig\src\Environment.php(277): Twig\Environment->load('/theme_basilisk/index_v1.twig')
 5 VENDORPATH\btapp\btcore\src\TemplateEngine\TwigCustom.php(39): Twig\Environment->render('/theme_basilisk/index_v1.twig', [...])
 6 VENDORPATH\btapp\btcore\src\TemplateEngine.php(28): BTCore\TemplateEngine\TwigCustom->view('/theme_basilisk/index_v1.twig', [...])
 7 APPPATH\Controllers\FlowPages\LandingPage.php(168): BTCore\TemplateEngine->view('/theme_basilisk/index_v1.twig', [...])
 8 APPPATH\Controllers\FlowPages\LandingPage.php(62): App\Controllers\FlowPages\LandingPage->theme_basilisk()
 9 APPPATH\Controllers\FlowPages\LandingPage.php(80): App\Controllers\FlowPages\LandingPage->index()
10 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\FlowPages\LandingPage->show_404()
11 SYSTEMPATH\CodeIgniter.php(961): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\FlowPages\LandingPage))
12 SYSTEMPATH\CodeIgniter.php(385): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
13 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
14 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
CRITICAL - 2025-06-11 07:21:22 --> Unable to find template "/theme_basilisk/index_v1.twig" (looked into: C:\laragon\www\app.ai-pro.org\btapp\app\Views/twig).
in VENDORPATH\twig\twig\src\Loader\FilesystemLoader.php on line 227.
 1 VENDORPATH\twig\twig\src\Loader\FilesystemLoader.php(131): Twig\Loader\FilesystemLoader->findTemplate('/theme_basilisk/index_v1.twig')
 2 VENDORPATH\twig\twig\src\Environment.php(261): Twig\Loader\FilesystemLoader->getCacheKey('/theme_basilisk/index_v1.twig')
 3 VENDORPATH\twig\twig\src\Environment.php(309): Twig\Environment->getTemplateClass('/theme_basilisk/index_v1.twig')
 4 VENDORPATH\twig\twig\src\Environment.php(277): Twig\Environment->load('/theme_basilisk/index_v1.twig')
 5 VENDORPATH\btapp\btcore\src\TemplateEngine\TwigCustom.php(39): Twig\Environment->render('/theme_basilisk/index_v1.twig', [...])
 6 VENDORPATH\btapp\btcore\src\TemplateEngine.php(28): BTCore\TemplateEngine\TwigCustom->view('/theme_basilisk/index_v1.twig', [...])
 7 APPPATH\Controllers\FlowPages\LandingPage.php(168): BTCore\TemplateEngine->view('/theme_basilisk/index_v1.twig', [...])
 8 APPPATH\Controllers\FlowPages\LandingPage.php(62): App\Controllers\FlowPages\LandingPage->theme_basilisk()
 9 APPPATH\Controllers\FlowPages\LandingPage.php(80): App\Controllers\FlowPages\LandingPage->index()
10 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\FlowPages\LandingPage->show_404()
11 SYSTEMPATH\CodeIgniter.php(961): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\FlowPages\LandingPage))
12 SYSTEMPATH\CodeIgniter.php(385): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
13 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
14 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
CRITICAL - 2025-06-11 07:21:27 --> Unable to find template "/theme_basilisk/index_v1.twig" (looked into: C:\laragon\www\app.ai-pro.org\btapp\app\Views/twig).
in VENDORPATH\twig\twig\src\Loader\FilesystemLoader.php on line 227.
 1 VENDORPATH\twig\twig\src\Loader\FilesystemLoader.php(131): Twig\Loader\FilesystemLoader->findTemplate('/theme_basilisk/index_v1.twig')
 2 VENDORPATH\twig\twig\src\Environment.php(261): Twig\Loader\FilesystemLoader->getCacheKey('/theme_basilisk/index_v1.twig')
 3 VENDORPATH\twig\twig\src\Environment.php(309): Twig\Environment->getTemplateClass('/theme_basilisk/index_v1.twig')
 4 VENDORPATH\twig\twig\src\Environment.php(277): Twig\Environment->load('/theme_basilisk/index_v1.twig')
 5 VENDORPATH\btapp\btcore\src\TemplateEngine\TwigCustom.php(39): Twig\Environment->render('/theme_basilisk/index_v1.twig', [...])
 6 VENDORPATH\btapp\btcore\src\TemplateEngine.php(28): BTCore\TemplateEngine\TwigCustom->view('/theme_basilisk/index_v1.twig', [...])
 7 APPPATH\Controllers\FlowPages\LandingPage.php(168): BTCore\TemplateEngine->view('/theme_basilisk/index_v1.twig', [...])
 8 APPPATH\Controllers\FlowPages\LandingPage.php(62): App\Controllers\FlowPages\LandingPage->theme_basilisk()
 9 APPPATH\Controllers\FlowPages\LandingPage.php(80): App\Controllers\FlowPages\LandingPage->index()
10 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\FlowPages\LandingPage->show_404()
11 SYSTEMPATH\CodeIgniter.php(961): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\FlowPages\LandingPage))
12 SYSTEMPATH\CodeIgniter.php(385): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
13 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
14 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
CRITICAL - 2025-06-11 07:21:33 --> Unable to find template "/theme_basilisk/index_v1.twig" (looked into: C:\laragon\www\app.ai-pro.org\btapp\app\Views/twig).
in VENDORPATH\twig\twig\src\Loader\FilesystemLoader.php on line 227.
 1 VENDORPATH\twig\twig\src\Loader\FilesystemLoader.php(131): Twig\Loader\FilesystemLoader->findTemplate('/theme_basilisk/index_v1.twig')
 2 VENDORPATH\twig\twig\src\Environment.php(261): Twig\Loader\FilesystemLoader->getCacheKey('/theme_basilisk/index_v1.twig')
 3 VENDORPATH\twig\twig\src\Environment.php(309): Twig\Environment->getTemplateClass('/theme_basilisk/index_v1.twig')
 4 VENDORPATH\twig\twig\src\Environment.php(277): Twig\Environment->load('/theme_basilisk/index_v1.twig')
 5 VENDORPATH\btapp\btcore\src\TemplateEngine\TwigCustom.php(39): Twig\Environment->render('/theme_basilisk/index_v1.twig', [...])
 6 VENDORPATH\btapp\btcore\src\TemplateEngine.php(28): BTCore\TemplateEngine\TwigCustom->view('/theme_basilisk/index_v1.twig', [...])
 7 APPPATH\Controllers\FlowPages\LandingPage.php(168): BTCore\TemplateEngine->view('/theme_basilisk/index_v1.twig', [...])
 8 APPPATH\Controllers\FlowPages\LandingPage.php(62): App\Controllers\FlowPages\LandingPage->theme_basilisk()
 9 APPPATH\Controllers\FlowPages\LandingPage.php(80): App\Controllers\FlowPages\LandingPage->index()
10 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\FlowPages\LandingPage->show_404()
11 SYSTEMPATH\CodeIgniter.php(961): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\FlowPages\LandingPage))
12 SYSTEMPATH\CodeIgniter.php(385): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
13 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
14 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
CRITICAL - 2025-06-11 07:21:38 --> Unable to find template "/theme_basilisk/index_v1.twig" (looked into: C:\laragon\www\app.ai-pro.org\btapp\app\Views/twig).
in VENDORPATH\twig\twig\src\Loader\FilesystemLoader.php on line 227.
 1 VENDORPATH\twig\twig\src\Loader\FilesystemLoader.php(131): Twig\Loader\FilesystemLoader->findTemplate('/theme_basilisk/index_v1.twig')
 2 VENDORPATH\twig\twig\src\Environment.php(261): Twig\Loader\FilesystemLoader->getCacheKey('/theme_basilisk/index_v1.twig')
 3 VENDORPATH\twig\twig\src\Environment.php(309): Twig\Environment->getTemplateClass('/theme_basilisk/index_v1.twig')
 4 VENDORPATH\twig\twig\src\Environment.php(277): Twig\Environment->load('/theme_basilisk/index_v1.twig')
 5 VENDORPATH\btapp\btcore\src\TemplateEngine\TwigCustom.php(39): Twig\Environment->render('/theme_basilisk/index_v1.twig', [...])
 6 VENDORPATH\btapp\btcore\src\TemplateEngine.php(28): BTCore\TemplateEngine\TwigCustom->view('/theme_basilisk/index_v1.twig', [...])
 7 APPPATH\Controllers\FlowPages\LandingPage.php(168): BTCore\TemplateEngine->view('/theme_basilisk/index_v1.twig', [...])
 8 APPPATH\Controllers\FlowPages\LandingPage.php(62): App\Controllers\FlowPages\LandingPage->theme_basilisk()
 9 APPPATH\Controllers\FlowPages\LandingPage.php(80): App\Controllers\FlowPages\LandingPage->index()
10 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\FlowPages\LandingPage->show_404()
11 SYSTEMPATH\CodeIgniter.php(961): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\FlowPages\LandingPage))
12 SYSTEMPATH\CodeIgniter.php(385): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
13 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
14 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
CRITICAL - 2025-06-11 07:21:43 --> Unable to find template "/theme_basilisk/index_v1.twig" (looked into: C:\laragon\www\app.ai-pro.org\btapp\app\Views/twig).
in VENDORPATH\twig\twig\src\Loader\FilesystemLoader.php on line 227.
 1 VENDORPATH\twig\twig\src\Loader\FilesystemLoader.php(131): Twig\Loader\FilesystemLoader->findTemplate('/theme_basilisk/index_v1.twig')
 2 VENDORPATH\twig\twig\src\Environment.php(261): Twig\Loader\FilesystemLoader->getCacheKey('/theme_basilisk/index_v1.twig')
 3 VENDORPATH\twig\twig\src\Environment.php(309): Twig\Environment->getTemplateClass('/theme_basilisk/index_v1.twig')
 4 VENDORPATH\twig\twig\src\Environment.php(277): Twig\Environment->load('/theme_basilisk/index_v1.twig')
 5 VENDORPATH\btapp\btcore\src\TemplateEngine\TwigCustom.php(39): Twig\Environment->render('/theme_basilisk/index_v1.twig', [...])
 6 VENDORPATH\btapp\btcore\src\TemplateEngine.php(28): BTCore\TemplateEngine\TwigCustom->view('/theme_basilisk/index_v1.twig', [...])
 7 APPPATH\Controllers\FlowPages\LandingPage.php(168): BTCore\TemplateEngine->view('/theme_basilisk/index_v1.twig', [...])
 8 APPPATH\Controllers\FlowPages\LandingPage.php(62): App\Controllers\FlowPages\LandingPage->theme_basilisk()
 9 APPPATH\Controllers\FlowPages\LandingPage.php(80): App\Controllers\FlowPages\LandingPage->index()
10 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\FlowPages\LandingPage->show_404()
11 SYSTEMPATH\CodeIgniter.php(961): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\FlowPages\LandingPage))
12 SYSTEMPATH\CodeIgniter.php(385): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
13 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
14 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
CRITICAL - 2025-06-11 07:21:48 --> Unable to find template "/theme_basilisk/index_v1.twig" (looked into: C:\laragon\www\app.ai-pro.org\btapp\app\Views/twig).
in VENDORPATH\twig\twig\src\Loader\FilesystemLoader.php on line 227.
 1 VENDORPATH\twig\twig\src\Loader\FilesystemLoader.php(131): Twig\Loader\FilesystemLoader->findTemplate('/theme_basilisk/index_v1.twig')
 2 VENDORPATH\twig\twig\src\Environment.php(261): Twig\Loader\FilesystemLoader->getCacheKey('/theme_basilisk/index_v1.twig')
 3 VENDORPATH\twig\twig\src\Environment.php(309): Twig\Environment->getTemplateClass('/theme_basilisk/index_v1.twig')
 4 VENDORPATH\twig\twig\src\Environment.php(277): Twig\Environment->load('/theme_basilisk/index_v1.twig')
 5 VENDORPATH\btapp\btcore\src\TemplateEngine\TwigCustom.php(39): Twig\Environment->render('/theme_basilisk/index_v1.twig', [...])
 6 VENDORPATH\btapp\btcore\src\TemplateEngine.php(28): BTCore\TemplateEngine\TwigCustom->view('/theme_basilisk/index_v1.twig', [...])
 7 APPPATH\Controllers\FlowPages\LandingPage.php(168): BTCore\TemplateEngine->view('/theme_basilisk/index_v1.twig', [...])
 8 APPPATH\Controllers\FlowPages\LandingPage.php(62): App\Controllers\FlowPages\LandingPage->theme_basilisk()
 9 APPPATH\Controllers\FlowPages\LandingPage.php(80): App\Controllers\FlowPages\LandingPage->index()
10 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\FlowPages\LandingPage->show_404()
11 SYSTEMPATH\CodeIgniter.php(961): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\FlowPages\LandingPage))
12 SYSTEMPATH\CodeIgniter.php(385): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
13 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
14 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
CRITICAL - 2025-06-11 07:21:53 --> Unable to find template "/theme_basilisk/index_v1.twig" (looked into: C:\laragon\www\app.ai-pro.org\btapp\app\Views/twig).
in VENDORPATH\twig\twig\src\Loader\FilesystemLoader.php on line 227.
 1 VENDORPATH\twig\twig\src\Loader\FilesystemLoader.php(131): Twig\Loader\FilesystemLoader->findTemplate('/theme_basilisk/index_v1.twig')
 2 VENDORPATH\twig\twig\src\Environment.php(261): Twig\Loader\FilesystemLoader->getCacheKey('/theme_basilisk/index_v1.twig')
 3 VENDORPATH\twig\twig\src\Environment.php(309): Twig\Environment->getTemplateClass('/theme_basilisk/index_v1.twig')
 4 VENDORPATH\twig\twig\src\Environment.php(277): Twig\Environment->load('/theme_basilisk/index_v1.twig')
 5 VENDORPATH\btapp\btcore\src\TemplateEngine\TwigCustom.php(39): Twig\Environment->render('/theme_basilisk/index_v1.twig', [...])
 6 VENDORPATH\btapp\btcore\src\TemplateEngine.php(28): BTCore\TemplateEngine\TwigCustom->view('/theme_basilisk/index_v1.twig', [...])
 7 APPPATH\Controllers\FlowPages\LandingPage.php(168): BTCore\TemplateEngine->view('/theme_basilisk/index_v1.twig', [...])
 8 APPPATH\Controllers\FlowPages\LandingPage.php(62): App\Controllers\FlowPages\LandingPage->theme_basilisk()
 9 APPPATH\Controllers\FlowPages\LandingPage.php(80): App\Controllers\FlowPages\LandingPage->index()
10 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\FlowPages\LandingPage->show_404()
11 SYSTEMPATH\CodeIgniter.php(961): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\FlowPages\LandingPage))
12 SYSTEMPATH\CodeIgniter.php(385): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
13 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
14 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
CRITICAL - 2025-06-11 07:21:58 --> Unable to find template "/theme_basilisk/index_v1.twig" (looked into: C:\laragon\www\app.ai-pro.org\btapp\app\Views/twig).
in VENDORPATH\twig\twig\src\Loader\FilesystemLoader.php on line 227.
 1 VENDORPATH\twig\twig\src\Loader\FilesystemLoader.php(131): Twig\Loader\FilesystemLoader->findTemplate('/theme_basilisk/index_v1.twig')
 2 VENDORPATH\twig\twig\src\Environment.php(261): Twig\Loader\FilesystemLoader->getCacheKey('/theme_basilisk/index_v1.twig')
 3 VENDORPATH\twig\twig\src\Environment.php(309): Twig\Environment->getTemplateClass('/theme_basilisk/index_v1.twig')
 4 VENDORPATH\twig\twig\src\Environment.php(277): Twig\Environment->load('/theme_basilisk/index_v1.twig')
 5 VENDORPATH\btapp\btcore\src\TemplateEngine\TwigCustom.php(39): Twig\Environment->render('/theme_basilisk/index_v1.twig', [...])
 6 VENDORPATH\btapp\btcore\src\TemplateEngine.php(28): BTCore\TemplateEngine\TwigCustom->view('/theme_basilisk/index_v1.twig', [...])
 7 APPPATH\Controllers\FlowPages\LandingPage.php(168): BTCore\TemplateEngine->view('/theme_basilisk/index_v1.twig', [...])
 8 APPPATH\Controllers\FlowPages\LandingPage.php(62): App\Controllers\FlowPages\LandingPage->theme_basilisk()
 9 APPPATH\Controllers\FlowPages\LandingPage.php(80): App\Controllers\FlowPages\LandingPage->index()
10 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\FlowPages\LandingPage->show_404()
11 SYSTEMPATH\CodeIgniter.php(961): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\FlowPages\LandingPage))
12 SYSTEMPATH\CodeIgniter.php(385): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
13 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
14 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
CRITICAL - 2025-06-11 07:22:03 --> Unable to find template "/theme_basilisk/index_v1.twig" (looked into: C:\laragon\www\app.ai-pro.org\btapp\app\Views/twig).
in VENDORPATH\twig\twig\src\Loader\FilesystemLoader.php on line 227.
 1 VENDORPATH\twig\twig\src\Loader\FilesystemLoader.php(131): Twig\Loader\FilesystemLoader->findTemplate('/theme_basilisk/index_v1.twig')
 2 VENDORPATH\twig\twig\src\Environment.php(261): Twig\Loader\FilesystemLoader->getCacheKey('/theme_basilisk/index_v1.twig')
 3 VENDORPATH\twig\twig\src\Environment.php(309): Twig\Environment->getTemplateClass('/theme_basilisk/index_v1.twig')
 4 VENDORPATH\twig\twig\src\Environment.php(277): Twig\Environment->load('/theme_basilisk/index_v1.twig')
 5 VENDORPATH\btapp\btcore\src\TemplateEngine\TwigCustom.php(39): Twig\Environment->render('/theme_basilisk/index_v1.twig', [...])
 6 VENDORPATH\btapp\btcore\src\TemplateEngine.php(28): BTCore\TemplateEngine\TwigCustom->view('/theme_basilisk/index_v1.twig', [...])
 7 APPPATH\Controllers\FlowPages\LandingPage.php(168): BTCore\TemplateEngine->view('/theme_basilisk/index_v1.twig', [...])
 8 APPPATH\Controllers\FlowPages\LandingPage.php(62): App\Controllers\FlowPages\LandingPage->theme_basilisk()
 9 APPPATH\Controllers\FlowPages\LandingPage.php(80): App\Controllers\FlowPages\LandingPage->index()
10 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\FlowPages\LandingPage->show_404()
11 SYSTEMPATH\CodeIgniter.php(961): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\FlowPages\LandingPage))
12 SYSTEMPATH\CodeIgniter.php(385): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
13 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
14 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-06-11 07:22:07 --> REQ ----------->
INFO - 2025-06-11 07:22:07 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
CRITICAL - 2025-06-11 07:22:09 --> Unable to find template "/theme_basilisk/index_v1.twig" (looked into: C:\laragon\www\app.ai-pro.org\btapp\app\Views/twig).
in VENDORPATH\twig\twig\src\Loader\FilesystemLoader.php on line 227.
 1 VENDORPATH\twig\twig\src\Loader\FilesystemLoader.php(131): Twig\Loader\FilesystemLoader->findTemplate('/theme_basilisk/index_v1.twig')
 2 VENDORPATH\twig\twig\src\Environment.php(261): Twig\Loader\FilesystemLoader->getCacheKey('/theme_basilisk/index_v1.twig')
 3 VENDORPATH\twig\twig\src\Environment.php(309): Twig\Environment->getTemplateClass('/theme_basilisk/index_v1.twig')
 4 VENDORPATH\twig\twig\src\Environment.php(277): Twig\Environment->load('/theme_basilisk/index_v1.twig')
 5 VENDORPATH\btapp\btcore\src\TemplateEngine\TwigCustom.php(39): Twig\Environment->render('/theme_basilisk/index_v1.twig', [...])
 6 VENDORPATH\btapp\btcore\src\TemplateEngine.php(28): BTCore\TemplateEngine\TwigCustom->view('/theme_basilisk/index_v1.twig', [...])
 7 APPPATH\Controllers\FlowPages\LandingPage.php(168): BTCore\TemplateEngine->view('/theme_basilisk/index_v1.twig', [...])
 8 APPPATH\Controllers\FlowPages\LandingPage.php(62): App\Controllers\FlowPages\LandingPage->theme_basilisk()
 9 APPPATH\Controllers\FlowPages\LandingPage.php(80): App\Controllers\FlowPages\LandingPage->index()
10 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\FlowPages\LandingPage->show_404()
11 SYSTEMPATH\CodeIgniter.php(961): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\FlowPages\LandingPage))
12 SYSTEMPATH\CodeIgniter.php(385): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
13 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
14 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
CRITICAL - 2025-06-11 07:22:15 --> Unable to find template "/theme_basilisk/index_v1.twig" (looked into: C:\laragon\www\app.ai-pro.org\btapp\app\Views/twig).
in VENDORPATH\twig\twig\src\Loader\FilesystemLoader.php on line 227.
 1 VENDORPATH\twig\twig\src\Loader\FilesystemLoader.php(131): Twig\Loader\FilesystemLoader->findTemplate('/theme_basilisk/index_v1.twig')
 2 VENDORPATH\twig\twig\src\Environment.php(261): Twig\Loader\FilesystemLoader->getCacheKey('/theme_basilisk/index_v1.twig')
 3 VENDORPATH\twig\twig\src\Environment.php(309): Twig\Environment->getTemplateClass('/theme_basilisk/index_v1.twig')
 4 VENDORPATH\twig\twig\src\Environment.php(277): Twig\Environment->load('/theme_basilisk/index_v1.twig')
 5 VENDORPATH\btapp\btcore\src\TemplateEngine\TwigCustom.php(39): Twig\Environment->render('/theme_basilisk/index_v1.twig', [...])
 6 VENDORPATH\btapp\btcore\src\TemplateEngine.php(28): BTCore\TemplateEngine\TwigCustom->view('/theme_basilisk/index_v1.twig', [...])
 7 APPPATH\Controllers\FlowPages\LandingPage.php(168): BTCore\TemplateEngine->view('/theme_basilisk/index_v1.twig', [...])
 8 APPPATH\Controllers\FlowPages\LandingPage.php(62): App\Controllers\FlowPages\LandingPage->theme_basilisk()
 9 APPPATH\Controllers\FlowPages\LandingPage.php(80): App\Controllers\FlowPages\LandingPage->index()
10 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\FlowPages\LandingPage->show_404()
11 SYSTEMPATH\CodeIgniter.php(961): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\FlowPages\LandingPage))
12 SYSTEMPATH\CodeIgniter.php(385): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
13 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
14 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
CRITICAL - 2025-06-11 07:22:21 --> Unable to find template "/theme_basilisk/index_v1.twig" (looked into: C:\laragon\www\app.ai-pro.org\btapp\app\Views/twig).
in VENDORPATH\twig\twig\src\Loader\FilesystemLoader.php on line 227.
 1 VENDORPATH\twig\twig\src\Loader\FilesystemLoader.php(131): Twig\Loader\FilesystemLoader->findTemplate('/theme_basilisk/index_v1.twig')
 2 VENDORPATH\twig\twig\src\Environment.php(261): Twig\Loader\FilesystemLoader->getCacheKey('/theme_basilisk/index_v1.twig')
 3 VENDORPATH\twig\twig\src\Environment.php(309): Twig\Environment->getTemplateClass('/theme_basilisk/index_v1.twig')
 4 VENDORPATH\twig\twig\src\Environment.php(277): Twig\Environment->load('/theme_basilisk/index_v1.twig')
 5 VENDORPATH\btapp\btcore\src\TemplateEngine\TwigCustom.php(39): Twig\Environment->render('/theme_basilisk/index_v1.twig', [...])
 6 VENDORPATH\btapp\btcore\src\TemplateEngine.php(28): BTCore\TemplateEngine\TwigCustom->view('/theme_basilisk/index_v1.twig', [...])
 7 APPPATH\Controllers\FlowPages\LandingPage.php(168): BTCore\TemplateEngine->view('/theme_basilisk/index_v1.twig', [...])
 8 APPPATH\Controllers\FlowPages\LandingPage.php(62): App\Controllers\FlowPages\LandingPage->theme_basilisk()
 9 APPPATH\Controllers\FlowPages\LandingPage.php(80): App\Controllers\FlowPages\LandingPage->index()
10 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\FlowPages\LandingPage->show_404()
11 SYSTEMPATH\CodeIgniter.php(961): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\FlowPages\LandingPage))
12 SYSTEMPATH\CodeIgniter.php(385): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
13 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
14 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
CRITICAL - 2025-06-11 07:22:27 --> Unable to find template "/theme_basilisk/index_v1.twig" (looked into: C:\laragon\www\app.ai-pro.org\btapp\app\Views/twig).
in VENDORPATH\twig\twig\src\Loader\FilesystemLoader.php on line 227.
 1 VENDORPATH\twig\twig\src\Loader\FilesystemLoader.php(131): Twig\Loader\FilesystemLoader->findTemplate('/theme_basilisk/index_v1.twig')
 2 VENDORPATH\twig\twig\src\Environment.php(261): Twig\Loader\FilesystemLoader->getCacheKey('/theme_basilisk/index_v1.twig')
 3 VENDORPATH\twig\twig\src\Environment.php(309): Twig\Environment->getTemplateClass('/theme_basilisk/index_v1.twig')
 4 VENDORPATH\twig\twig\src\Environment.php(277): Twig\Environment->load('/theme_basilisk/index_v1.twig')
 5 VENDORPATH\btapp\btcore\src\TemplateEngine\TwigCustom.php(39): Twig\Environment->render('/theme_basilisk/index_v1.twig', [...])
 6 VENDORPATH\btapp\btcore\src\TemplateEngine.php(28): BTCore\TemplateEngine\TwigCustom->view('/theme_basilisk/index_v1.twig', [...])
 7 APPPATH\Controllers\FlowPages\LandingPage.php(168): BTCore\TemplateEngine->view('/theme_basilisk/index_v1.twig', [...])
 8 APPPATH\Controllers\FlowPages\LandingPage.php(62): App\Controllers\FlowPages\LandingPage->theme_basilisk()
 9 APPPATH\Controllers\FlowPages\LandingPage.php(80): App\Controllers\FlowPages\LandingPage->index()
10 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\FlowPages\LandingPage->show_404()
11 SYSTEMPATH\CodeIgniter.php(961): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\FlowPages\LandingPage))
12 SYSTEMPATH\CodeIgniter.php(385): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
13 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
14 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
CRITICAL - 2025-06-11 07:22:33 --> Unable to find template "/theme_basilisk/index_v1.twig" (looked into: C:\laragon\www\app.ai-pro.org\btapp\app\Views/twig).
in VENDORPATH\twig\twig\src\Loader\FilesystemLoader.php on line 227.
 1 VENDORPATH\twig\twig\src\Loader\FilesystemLoader.php(131): Twig\Loader\FilesystemLoader->findTemplate('/theme_basilisk/index_v1.twig')
 2 VENDORPATH\twig\twig\src\Environment.php(261): Twig\Loader\FilesystemLoader->getCacheKey('/theme_basilisk/index_v1.twig')
 3 VENDORPATH\twig\twig\src\Environment.php(309): Twig\Environment->getTemplateClass('/theme_basilisk/index_v1.twig')
 4 VENDORPATH\twig\twig\src\Environment.php(277): Twig\Environment->load('/theme_basilisk/index_v1.twig')
 5 VENDORPATH\btapp\btcore\src\TemplateEngine\TwigCustom.php(39): Twig\Environment->render('/theme_basilisk/index_v1.twig', [...])
 6 VENDORPATH\btapp\btcore\src\TemplateEngine.php(28): BTCore\TemplateEngine\TwigCustom->view('/theme_basilisk/index_v1.twig', [...])
 7 APPPATH\Controllers\FlowPages\LandingPage.php(168): BTCore\TemplateEngine->view('/theme_basilisk/index_v1.twig', [...])
 8 APPPATH\Controllers\FlowPages\LandingPage.php(62): App\Controllers\FlowPages\LandingPage->theme_basilisk()
 9 APPPATH\Controllers\FlowPages\LandingPage.php(80): App\Controllers\FlowPages\LandingPage->index()
10 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\FlowPages\LandingPage->show_404()
11 SYSTEMPATH\CodeIgniter.php(961): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\FlowPages\LandingPage))
12 SYSTEMPATH\CodeIgniter.php(385): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
13 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
14 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
CRITICAL - 2025-06-11 07:22:39 --> Unable to find template "/theme_basilisk/index_v1.twig" (looked into: C:\laragon\www\app.ai-pro.org\btapp\app\Views/twig).
in VENDORPATH\twig\twig\src\Loader\FilesystemLoader.php on line 227.
 1 VENDORPATH\twig\twig\src\Loader\FilesystemLoader.php(131): Twig\Loader\FilesystemLoader->findTemplate('/theme_basilisk/index_v1.twig')
 2 VENDORPATH\twig\twig\src\Environment.php(261): Twig\Loader\FilesystemLoader->getCacheKey('/theme_basilisk/index_v1.twig')
 3 VENDORPATH\twig\twig\src\Environment.php(309): Twig\Environment->getTemplateClass('/theme_basilisk/index_v1.twig')
 4 VENDORPATH\twig\twig\src\Environment.php(277): Twig\Environment->load('/theme_basilisk/index_v1.twig')
 5 VENDORPATH\btapp\btcore\src\TemplateEngine\TwigCustom.php(39): Twig\Environment->render('/theme_basilisk/index_v1.twig', [...])
 6 VENDORPATH\btapp\btcore\src\TemplateEngine.php(28): BTCore\TemplateEngine\TwigCustom->view('/theme_basilisk/index_v1.twig', [...])
 7 APPPATH\Controllers\FlowPages\LandingPage.php(168): BTCore\TemplateEngine->view('/theme_basilisk/index_v1.twig', [...])
 8 APPPATH\Controllers\FlowPages\LandingPage.php(62): App\Controllers\FlowPages\LandingPage->theme_basilisk()
 9 APPPATH\Controllers\FlowPages\LandingPage.php(80): App\Controllers\FlowPages\LandingPage->index()
10 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\FlowPages\LandingPage->show_404()
11 SYSTEMPATH\CodeIgniter.php(961): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\FlowPages\LandingPage))
12 SYSTEMPATH\CodeIgniter.php(385): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
13 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
14 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
CRITICAL - 2025-06-11 07:22:45 --> Unable to find template "/theme_basilisk/index_v1.twig" (looked into: C:\laragon\www\app.ai-pro.org\btapp\app\Views/twig).
in VENDORPATH\twig\twig\src\Loader\FilesystemLoader.php on line 227.
 1 VENDORPATH\twig\twig\src\Loader\FilesystemLoader.php(131): Twig\Loader\FilesystemLoader->findTemplate('/theme_basilisk/index_v1.twig')
 2 VENDORPATH\twig\twig\src\Environment.php(261): Twig\Loader\FilesystemLoader->getCacheKey('/theme_basilisk/index_v1.twig')
 3 VENDORPATH\twig\twig\src\Environment.php(309): Twig\Environment->getTemplateClass('/theme_basilisk/index_v1.twig')
 4 VENDORPATH\twig\twig\src\Environment.php(277): Twig\Environment->load('/theme_basilisk/index_v1.twig')
 5 VENDORPATH\btapp\btcore\src\TemplateEngine\TwigCustom.php(39): Twig\Environment->render('/theme_basilisk/index_v1.twig', [...])
 6 VENDORPATH\btapp\btcore\src\TemplateEngine.php(28): BTCore\TemplateEngine\TwigCustom->view('/theme_basilisk/index_v1.twig', [...])
 7 APPPATH\Controllers\FlowPages\LandingPage.php(168): BTCore\TemplateEngine->view('/theme_basilisk/index_v1.twig', [...])
 8 APPPATH\Controllers\FlowPages\LandingPage.php(62): App\Controllers\FlowPages\LandingPage->theme_basilisk()
 9 APPPATH\Controllers\FlowPages\LandingPage.php(80): App\Controllers\FlowPages\LandingPage->index()
10 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\FlowPages\LandingPage->show_404()
11 SYSTEMPATH\CodeIgniter.php(961): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\FlowPages\LandingPage))
12 SYSTEMPATH\CodeIgniter.php(385): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
13 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
14 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
CRITICAL - 2025-06-11 07:22:51 --> Unable to find template "/theme_basilisk/index_v1.twig" (looked into: C:\laragon\www\app.ai-pro.org\btapp\app\Views/twig).
in VENDORPATH\twig\twig\src\Loader\FilesystemLoader.php on line 227.
 1 VENDORPATH\twig\twig\src\Loader\FilesystemLoader.php(131): Twig\Loader\FilesystemLoader->findTemplate('/theme_basilisk/index_v1.twig')
 2 VENDORPATH\twig\twig\src\Environment.php(261): Twig\Loader\FilesystemLoader->getCacheKey('/theme_basilisk/index_v1.twig')
 3 VENDORPATH\twig\twig\src\Environment.php(309): Twig\Environment->getTemplateClass('/theme_basilisk/index_v1.twig')
 4 VENDORPATH\twig\twig\src\Environment.php(277): Twig\Environment->load('/theme_basilisk/index_v1.twig')
 5 VENDORPATH\btapp\btcore\src\TemplateEngine\TwigCustom.php(39): Twig\Environment->render('/theme_basilisk/index_v1.twig', [...])
 6 VENDORPATH\btapp\btcore\src\TemplateEngine.php(28): BTCore\TemplateEngine\TwigCustom->view('/theme_basilisk/index_v1.twig', [...])
 7 APPPATH\Controllers\FlowPages\LandingPage.php(168): BTCore\TemplateEngine->view('/theme_basilisk/index_v1.twig', [...])
 8 APPPATH\Controllers\FlowPages\LandingPage.php(62): App\Controllers\FlowPages\LandingPage->theme_basilisk()
 9 APPPATH\Controllers\FlowPages\LandingPage.php(80): App\Controllers\FlowPages\LandingPage->index()
10 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\FlowPages\LandingPage->show_404()
11 SYSTEMPATH\CodeIgniter.php(961): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\FlowPages\LandingPage))
12 SYSTEMPATH\CodeIgniter.php(385): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
13 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
14 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
CRITICAL - 2025-06-11 07:22:57 --> Unable to find template "/theme_basilisk/index_v1.twig" (looked into: C:\laragon\www\app.ai-pro.org\btapp\app\Views/twig).
in VENDORPATH\twig\twig\src\Loader\FilesystemLoader.php on line 227.
 1 VENDORPATH\twig\twig\src\Loader\FilesystemLoader.php(131): Twig\Loader\FilesystemLoader->findTemplate('/theme_basilisk/index_v1.twig')
 2 VENDORPATH\twig\twig\src\Environment.php(261): Twig\Loader\FilesystemLoader->getCacheKey('/theme_basilisk/index_v1.twig')
 3 VENDORPATH\twig\twig\src\Environment.php(309): Twig\Environment->getTemplateClass('/theme_basilisk/index_v1.twig')
 4 VENDORPATH\twig\twig\src\Environment.php(277): Twig\Environment->load('/theme_basilisk/index_v1.twig')
 5 VENDORPATH\btapp\btcore\src\TemplateEngine\TwigCustom.php(39): Twig\Environment->render('/theme_basilisk/index_v1.twig', [...])
 6 VENDORPATH\btapp\btcore\src\TemplateEngine.php(28): BTCore\TemplateEngine\TwigCustom->view('/theme_basilisk/index_v1.twig', [...])
 7 APPPATH\Controllers\FlowPages\LandingPage.php(168): BTCore\TemplateEngine->view('/theme_basilisk/index_v1.twig', [...])
 8 APPPATH\Controllers\FlowPages\LandingPage.php(62): App\Controllers\FlowPages\LandingPage->theme_basilisk()
 9 APPPATH\Controllers\FlowPages\LandingPage.php(80): App\Controllers\FlowPages\LandingPage->index()
10 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\FlowPages\LandingPage->show_404()
11 SYSTEMPATH\CodeIgniter.php(961): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\FlowPages\LandingPage))
12 SYSTEMPATH\CodeIgniter.php(385): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
13 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
14 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
CRITICAL - 2025-06-11 07:23:03 --> Unable to find template "/theme_basilisk/index_v1.twig" (looked into: C:\laragon\www\app.ai-pro.org\btapp\app\Views/twig).
in VENDORPATH\twig\twig\src\Loader\FilesystemLoader.php on line 227.
 1 VENDORPATH\twig\twig\src\Loader\FilesystemLoader.php(131): Twig\Loader\FilesystemLoader->findTemplate('/theme_basilisk/index_v1.twig')
 2 VENDORPATH\twig\twig\src\Environment.php(261): Twig\Loader\FilesystemLoader->getCacheKey('/theme_basilisk/index_v1.twig')
 3 VENDORPATH\twig\twig\src\Environment.php(309): Twig\Environment->getTemplateClass('/theme_basilisk/index_v1.twig')
 4 VENDORPATH\twig\twig\src\Environment.php(277): Twig\Environment->load('/theme_basilisk/index_v1.twig')
 5 VENDORPATH\btapp\btcore\src\TemplateEngine\TwigCustom.php(39): Twig\Environment->render('/theme_basilisk/index_v1.twig', [...])
 6 VENDORPATH\btapp\btcore\src\TemplateEngine.php(28): BTCore\TemplateEngine\TwigCustom->view('/theme_basilisk/index_v1.twig', [...])
 7 APPPATH\Controllers\FlowPages\LandingPage.php(168): BTCore\TemplateEngine->view('/theme_basilisk/index_v1.twig', [...])
 8 APPPATH\Controllers\FlowPages\LandingPage.php(62): App\Controllers\FlowPages\LandingPage->theme_basilisk()
 9 APPPATH\Controllers\FlowPages\LandingPage.php(80): App\Controllers\FlowPages\LandingPage->index()
10 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\FlowPages\LandingPage->show_404()
11 SYSTEMPATH\CodeIgniter.php(961): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\FlowPages\LandingPage))
12 SYSTEMPATH\CodeIgniter.php(385): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
13 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
14 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
CRITICAL - 2025-06-11 07:23:09 --> Unable to find template "/theme_basilisk/index_v1.twig" (looked into: C:\laragon\www\app.ai-pro.org\btapp\app\Views/twig).
in VENDORPATH\twig\twig\src\Loader\FilesystemLoader.php on line 227.
 1 VENDORPATH\twig\twig\src\Loader\FilesystemLoader.php(131): Twig\Loader\FilesystemLoader->findTemplate('/theme_basilisk/index_v1.twig')
 2 VENDORPATH\twig\twig\src\Environment.php(261): Twig\Loader\FilesystemLoader->getCacheKey('/theme_basilisk/index_v1.twig')
 3 VENDORPATH\twig\twig\src\Environment.php(309): Twig\Environment->getTemplateClass('/theme_basilisk/index_v1.twig')
 4 VENDORPATH\twig\twig\src\Environment.php(277): Twig\Environment->load('/theme_basilisk/index_v1.twig')
 5 VENDORPATH\btapp\btcore\src\TemplateEngine\TwigCustom.php(39): Twig\Environment->render('/theme_basilisk/index_v1.twig', [...])
 6 VENDORPATH\btapp\btcore\src\TemplateEngine.php(28): BTCore\TemplateEngine\TwigCustom->view('/theme_basilisk/index_v1.twig', [...])
 7 APPPATH\Controllers\FlowPages\LandingPage.php(168): BTCore\TemplateEngine->view('/theme_basilisk/index_v1.twig', [...])
 8 APPPATH\Controllers\FlowPages\LandingPage.php(62): App\Controllers\FlowPages\LandingPage->theme_basilisk()
 9 APPPATH\Controllers\FlowPages\LandingPage.php(80): App\Controllers\FlowPages\LandingPage->index()
10 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\FlowPages\LandingPage->show_404()
11 SYSTEMPATH\CodeIgniter.php(961): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\FlowPages\LandingPage))
12 SYSTEMPATH\CodeIgniter.php(385): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
13 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
14 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
CRITICAL - 2025-06-11 07:23:15 --> Unable to find template "/theme_basilisk/index_v1.twig" (looked into: C:\laragon\www\app.ai-pro.org\btapp\app\Views/twig).
in VENDORPATH\twig\twig\src\Loader\FilesystemLoader.php on line 227.
 1 VENDORPATH\twig\twig\src\Loader\FilesystemLoader.php(131): Twig\Loader\FilesystemLoader->findTemplate('/theme_basilisk/index_v1.twig')
 2 VENDORPATH\twig\twig\src\Environment.php(261): Twig\Loader\FilesystemLoader->getCacheKey('/theme_basilisk/index_v1.twig')
 3 VENDORPATH\twig\twig\src\Environment.php(309): Twig\Environment->getTemplateClass('/theme_basilisk/index_v1.twig')
 4 VENDORPATH\twig\twig\src\Environment.php(277): Twig\Environment->load('/theme_basilisk/index_v1.twig')
 5 VENDORPATH\btapp\btcore\src\TemplateEngine\TwigCustom.php(39): Twig\Environment->render('/theme_basilisk/index_v1.twig', [...])
 6 VENDORPATH\btapp\btcore\src\TemplateEngine.php(28): BTCore\TemplateEngine\TwigCustom->view('/theme_basilisk/index_v1.twig', [...])
 7 APPPATH\Controllers\FlowPages\LandingPage.php(168): BTCore\TemplateEngine->view('/theme_basilisk/index_v1.twig', [...])
 8 APPPATH\Controllers\FlowPages\LandingPage.php(62): App\Controllers\FlowPages\LandingPage->theme_basilisk()
 9 APPPATH\Controllers\FlowPages\LandingPage.php(80): App\Controllers\FlowPages\LandingPage->index()
10 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\FlowPages\LandingPage->show_404()
11 SYSTEMPATH\CodeIgniter.php(961): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\FlowPages\LandingPage))
12 SYSTEMPATH\CodeIgniter.php(385): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
13 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
14 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-06-11 07:23:22 --> REQ ----------->
INFO - 2025-06-11 07:23:22 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
CRITICAL - 2025-06-11 07:23:23 --> Unable to find template "/theme_basilisk/index_v1.twig" (looked into: C:\laragon\www\app.ai-pro.org\btapp\app\Views/twig).
in VENDORPATH\twig\twig\src\Loader\FilesystemLoader.php on line 227.
 1 VENDORPATH\twig\twig\src\Loader\FilesystemLoader.php(131): Twig\Loader\FilesystemLoader->findTemplate('/theme_basilisk/index_v1.twig')
 2 VENDORPATH\twig\twig\src\Environment.php(261): Twig\Loader\FilesystemLoader->getCacheKey('/theme_basilisk/index_v1.twig')
 3 VENDORPATH\twig\twig\src\Environment.php(309): Twig\Environment->getTemplateClass('/theme_basilisk/index_v1.twig')
 4 VENDORPATH\twig\twig\src\Environment.php(277): Twig\Environment->load('/theme_basilisk/index_v1.twig')
 5 VENDORPATH\btapp\btcore\src\TemplateEngine\TwigCustom.php(39): Twig\Environment->render('/theme_basilisk/index_v1.twig', [...])
 6 VENDORPATH\btapp\btcore\src\TemplateEngine.php(28): BTCore\TemplateEngine\TwigCustom->view('/theme_basilisk/index_v1.twig', [...])
 7 APPPATH\Controllers\FlowPages\LandingPage.php(168): BTCore\TemplateEngine->view('/theme_basilisk/index_v1.twig', [...])
 8 APPPATH\Controllers\FlowPages\LandingPage.php(62): App\Controllers\FlowPages\LandingPage->theme_basilisk()
 9 APPPATH\Controllers\FlowPages\LandingPage.php(80): App\Controllers\FlowPages\LandingPage->index()
10 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\FlowPages\LandingPage->show_404()
11 SYSTEMPATH\CodeIgniter.php(961): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\FlowPages\LandingPage))
12 SYSTEMPATH\CodeIgniter.php(385): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
13 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
14 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
INFO - 2025-06-11 07:23:23 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
CRITICAL - 2025-06-11 07:23:23 --> Unable to find template "/theme_basilisk/index_v1.twig" (looked into: C:\laragon\www\app.ai-pro.org\btapp\app\Views/twig).
in VENDORPATH\twig\twig\src\Loader\FilesystemLoader.php on line 227.
 1 VENDORPATH\twig\twig\src\Loader\FilesystemLoader.php(131): Twig\Loader\FilesystemLoader->findTemplate('/theme_basilisk/index_v1.twig')
 2 VENDORPATH\twig\twig\src\Environment.php(261): Twig\Loader\FilesystemLoader->getCacheKey('/theme_basilisk/index_v1.twig')
 3 VENDORPATH\twig\twig\src\Environment.php(309): Twig\Environment->getTemplateClass('/theme_basilisk/index_v1.twig')
 4 VENDORPATH\twig\twig\src\Environment.php(277): Twig\Environment->load('/theme_basilisk/index_v1.twig')
 5 VENDORPATH\btapp\btcore\src\TemplateEngine\TwigCustom.php(39): Twig\Environment->render('/theme_basilisk/index_v1.twig', [...])
 6 VENDORPATH\btapp\btcore\src\TemplateEngine.php(28): BTCore\TemplateEngine\TwigCustom->view('/theme_basilisk/index_v1.twig', [...])
 7 APPPATH\Controllers\FlowPages\Pricing.php(308): BTCore\TemplateEngine->view('/theme_basilisk/index_v1.twig', [...])
 8 APPPATH\Controllers\FlowPages\Pricing.php(274): App\Controllers\FlowPages\Pricing->theme_basilisk()
 9 APPPATH\Controllers\FlowPages\Pricing.php(83): App\Controllers\FlowPages\Pricing->switch_theme()
10 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\FlowPages\Pricing->index()
11 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\FlowPages\Pricing))
12 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
13 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
14 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
CRITICAL - 2025-06-11 07:23:29 --> Unable to find template "/theme_basilisk/index_v1.twig" (looked into: C:\laragon\www\app.ai-pro.org\btapp\app\Views/twig).
in VENDORPATH\twig\twig\src\Loader\FilesystemLoader.php on line 227.
 1 VENDORPATH\twig\twig\src\Loader\FilesystemLoader.php(131): Twig\Loader\FilesystemLoader->findTemplate('/theme_basilisk/index_v1.twig')
 2 VENDORPATH\twig\twig\src\Environment.php(261): Twig\Loader\FilesystemLoader->getCacheKey('/theme_basilisk/index_v1.twig')
 3 VENDORPATH\twig\twig\src\Environment.php(309): Twig\Environment->getTemplateClass('/theme_basilisk/index_v1.twig')
 4 VENDORPATH\twig\twig\src\Environment.php(277): Twig\Environment->load('/theme_basilisk/index_v1.twig')
 5 VENDORPATH\btapp\btcore\src\TemplateEngine\TwigCustom.php(39): Twig\Environment->render('/theme_basilisk/index_v1.twig', [...])
 6 VENDORPATH\btapp\btcore\src\TemplateEngine.php(28): BTCore\TemplateEngine\TwigCustom->view('/theme_basilisk/index_v1.twig', [...])
 7 APPPATH\Controllers\FlowPages\LandingPage.php(168): BTCore\TemplateEngine->view('/theme_basilisk/index_v1.twig', [...])
 8 APPPATH\Controllers\FlowPages\LandingPage.php(62): App\Controllers\FlowPages\LandingPage->theme_basilisk()
 9 APPPATH\Controllers\FlowPages\LandingPage.php(80): App\Controllers\FlowPages\LandingPage->index()
10 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\FlowPages\LandingPage->show_404()
11 SYSTEMPATH\CodeIgniter.php(961): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\FlowPages\LandingPage))
12 SYSTEMPATH\CodeIgniter.php(385): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
13 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
14 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-06-11 07:23:31 --> REQ ----------->
INFO - 2025-06-11 07:23:31 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
CRITICAL - 2025-06-11 07:23:35 --> Unable to find template "/theme_basilisk/index_v1.twig" (looked into: C:\laragon\www\app.ai-pro.org\btapp\app\Views/twig).
in VENDORPATH\twig\twig\src\Loader\FilesystemLoader.php on line 227.
 1 VENDORPATH\twig\twig\src\Loader\FilesystemLoader.php(131): Twig\Loader\FilesystemLoader->findTemplate('/theme_basilisk/index_v1.twig')
 2 VENDORPATH\twig\twig\src\Environment.php(261): Twig\Loader\FilesystemLoader->getCacheKey('/theme_basilisk/index_v1.twig')
 3 VENDORPATH\twig\twig\src\Environment.php(309): Twig\Environment->getTemplateClass('/theme_basilisk/index_v1.twig')
 4 VENDORPATH\twig\twig\src\Environment.php(277): Twig\Environment->load('/theme_basilisk/index_v1.twig')
 5 VENDORPATH\btapp\btcore\src\TemplateEngine\TwigCustom.php(39): Twig\Environment->render('/theme_basilisk/index_v1.twig', [...])
 6 VENDORPATH\btapp\btcore\src\TemplateEngine.php(28): BTCore\TemplateEngine\TwigCustom->view('/theme_basilisk/index_v1.twig', [...])
 7 APPPATH\Controllers\FlowPages\LandingPage.php(168): BTCore\TemplateEngine->view('/theme_basilisk/index_v1.twig', [...])
 8 APPPATH\Controllers\FlowPages\LandingPage.php(62): App\Controllers\FlowPages\LandingPage->theme_basilisk()
 9 APPPATH\Controllers\FlowPages\LandingPage.php(80): App\Controllers\FlowPages\LandingPage->index()
10 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\FlowPages\LandingPage->show_404()
11 SYSTEMPATH\CodeIgniter.php(961): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\FlowPages\LandingPage))
12 SYSTEMPATH\CodeIgniter.php(385): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
13 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
14 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-06-11 07:23:36 --> REQ ----------->
INFO - 2025-06-11 07:23:36 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 07:23:59 --> REQ ----------->
INFO - 2025-06-11 07:23:59 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 07:24:00 --> REQ ----------->
INFO - 2025-06-11 07:24:00 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2025-06-11 07:24:00 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 07:24:02 --> REQ ----------->14
DEBUG - 2025-06-11 07:24:03 --> REQ ----------->
INFO - 2025-06-11 07:24:03 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 07:24:08 --> REQ ----------->
INFO - 2025-06-11 07:24:08 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 07:24:11 --> REQ ----------->
INFO - 2025-06-11 07:24:11 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 07:24:12 --> REQ ----------->
INFO - 2025-06-11 07:24:12 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 07:24:38 --> REQ ----------->
INFO - 2025-06-11 07:24:38 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 07:24:38 --> REQ ----------->
INFO - 2025-06-11 07:24:38 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2025-06-11 07:24:47 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 07:24:48 --> REQ ----------->14
DEBUG - 2025-06-11 07:24:48 --> REQ ----------->
INFO - 2025-06-11 07:24:48 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 13:35:42 --> REQ ----------->
INFO - 2025-06-11 13:35:42 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
