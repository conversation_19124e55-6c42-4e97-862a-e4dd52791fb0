<?php

namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;

class AddSubTextToPlan extends Migration
{
    private $table = "plan";

    public function up()
    {
        if (!$this->db->fieldExists('sub_text', $this->table)){
            $this->db->disableForeignKeyChecks();
            $fields = [
                'sub_text' => [
                    'type'       => 'VARCHAR',
                    'constraint' => 100,
                    'null'       => true,
                    'default'    => '',
                    'after'      => 'label',
                ],
            ];
            $this->forge->addColumn($this->table, $fields);
        }
    }

    public function down()
    {
        // $this->forge->dropColumn($this->table, 'sub_text');
    }
}
