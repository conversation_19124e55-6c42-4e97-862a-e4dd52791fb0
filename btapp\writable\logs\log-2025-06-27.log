ERROR - 2025-06-27 11:03:51 --> mysqli_sql_exception: Operand should contain 1 column(s) in C:\laragon\www\app.ai-pro.org\btapp\vendor\codeigniter4\framework\system\Database\MySQLi\Connection.php:295
Stack trace:
#0 C:\laragon\www\app.ai-pro.org\btapp\vendor\codeigniter4\framework\system\Database\MySQLi\Connection.php(295): mysqli->query('UPDATE `start-p...', 0)
#1 C:\laragon\www\app.ai-pro.org\btapp\vendor\codeigniter4\framework\system\Database\BaseConnection.php(692): CodeIgniter\Database\MySQLi\Connection->execute('UPDATE `start-p...')
#2 C:\laragon\www\app.ai-pro.org\btapp\vendor\codeigniter4\framework\system\Database\BaseConnection.php(606): CodeIgniter\Database\BaseConnection->simpleQuery('UPDATE `start-p...')
#3 C:\laragon\www\app.ai-pro.org\btapp\vendor\codeigniter4\framework\system\Database\BaseBuilder.php(2476): CodeIgniter\Database\BaseConnection->query('UPDATE `start-p...', Array, false)
#4 C:\laragon\www\app.ai-pro.org\btapp\app\Database\Seeds\UpdatePlanDescriptionSeeder.php(65): CodeIgniter\Database\BaseBuilder->update(Array)
#5 C:\laragon\www\app.ai-pro.org\btapp\vendor\codeigniter4\framework\system\Database\Seeder.php(146): App\Database\Seeds\UpdatePlanDescriptionSeeder->run()
#6 C:\laragon\www\app.ai-pro.org\btapp\app\Database\Seeds\InsertPlanTableV2.php(14124): CodeIgniter\Database\Seeder->call('App\\Database\\Se...')
#7 C:\laragon\www\app.ai-pro.org\btapp\vendor\codeigniter4\framework\system\Database\Seeder.php(146): App\Database\Seeds\InsertPlanTableV2->run()
#8 C:\laragon\www\app.ai-pro.org\btapp\vendor\codeigniter4\framework\system\Commands\Database\Seed.php(77): CodeIgniter\Database\Seeder->call('App\\Database\\Se...')
#9 C:\laragon\www\app.ai-pro.org\btapp\vendor\codeigniter4\framework\system\CLI\Commands.php(65): CodeIgniter\Commands\Database\Seed->run(Array)
#10 C:\laragon\www\app.ai-pro.org\btapp\vendor\codeigniter4\framework\system\CLI\Console.php(37): CodeIgniter\CLI\Commands->run('db:seed', Array)
#11 C:\laragon\www\app.ai-pro.org\btapp\spark(97): CodeIgniter\CLI\Console->run()
#12 {main}
ERROR - 2025-06-27 11:03:51 --> mysqli_sql_exception: Operand should contain 1 column(s) in C:\laragon\www\app.ai-pro.org\btapp\vendor\codeigniter4\framework\system\Database\MySQLi\Connection.php:295
Stack trace:
#0 C:\laragon\www\app.ai-pro.org\btapp\vendor\codeigniter4\framework\system\Database\MySQLi\Connection.php(295): mysqli->query('UPDATE `start-p...', 0)
#1 C:\laragon\www\app.ai-pro.org\btapp\vendor\codeigniter4\framework\system\Database\BaseConnection.php(692): CodeIgniter\Database\MySQLi\Connection->execute('UPDATE `start-p...')
#2 C:\laragon\www\app.ai-pro.org\btapp\vendor\codeigniter4\framework\system\Database\BaseConnection.php(606): CodeIgniter\Database\BaseConnection->simpleQuery('UPDATE `start-p...')
#3 C:\laragon\www\app.ai-pro.org\btapp\vendor\codeigniter4\framework\system\Database\BaseBuilder.php(2476): CodeIgniter\Database\BaseConnection->query('UPDATE `start-p...', Array, false)
#4 C:\laragon\www\app.ai-pro.org\btapp\app\Database\Seeds\UpdatePlanDescriptionSeeder.php(65): CodeIgniter\Database\BaseBuilder->update(Array)
#5 C:\laragon\www\app.ai-pro.org\btapp\vendor\codeigniter4\framework\system\Database\Seeder.php(146): App\Database\Seeds\UpdatePlanDescriptionSeeder->run()
#6 C:\laragon\www\app.ai-pro.org\btapp\app\Database\Seeds\InsertPlanTableV2.php(14124): CodeIgniter\Database\Seeder->call('App\\Database\\Se...')
#7 C:\laragon\www\app.ai-pro.org\btapp\vendor\codeigniter4\framework\system\Database\Seeder.php(146): App\Database\Seeds\InsertPlanTableV2->run()
#8 C:\laragon\www\app.ai-pro.org\btapp\vendor\codeigniter4\framework\system\Commands\Database\Seed.php(77): CodeIgniter\Database\Seeder->call('App\\Database\\Se...')
#9 C:\laragon\www\app.ai-pro.org\btapp\vendor\codeigniter4\framework\system\CLI\Commands.php(65): CodeIgniter\Commands\Database\Seed->run(Array)
#10 C:\laragon\www\app.ai-pro.org\btapp\vendor\codeigniter4\framework\system\CLI\Console.php(37): CodeIgniter\CLI\Commands->run('db:seed', Array)
#11 C:\laragon\www\app.ai-pro.org\btapp\spark(97): CodeIgniter\CLI\Console->run()
#12 {main}
INFO - 2025-06-27 11:06:58 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-27 11:07:03 --> REQ ----------->14
DEBUG - 2025-06-27 11:07:03 --> REQ ----------->
INFO - 2025-06-27 11:07:03 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-27 11:07:06 --> REQ ----------->
INFO - 2025-06-27 11:07:06 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2025-06-27 11:07:12 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-27 11:07:12 --> REQ ----------->
INFO - 2025-06-27 11:07:12 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-27 11:07:12 --> REQ ----------->97
DEBUG - 2025-06-27 11:07:12 --> REQ ----------->
INFO - 2025-06-27 11:07:12 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-27 11:07:13 --> REQ ----------->
INFO - 2025-06-27 11:07:13 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-27 11:07:13 --> REQ ----------->
INFO - 2025-06-27 11:07:13 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-27 11:08:04 --> REQ ----------->
INFO - 2025-06-27 11:08:04 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-27 11:08:05 --> REQ ----------->
INFO - 2025-06-27 11:08:05 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-27 11:08:08 --> REQ ----------->
INFO - 2025-06-27 11:08:08 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-27 11:08:11 --> REQ ----------->
INFO - 2025-06-27 11:08:11 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-27 11:08:13 --> REQ ----------->
INFO - 2025-06-27 11:08:13 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2025-06-27 11:08:14 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-27 11:08:15 --> REQ ----------->14
DEBUG - 2025-06-27 11:08:15 --> REQ ----------->
INFO - 2025-06-27 11:08:15 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-27 11:08:52 --> REQ ----------->
INFO - 2025-06-27 11:08:52 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2025-06-27 11:08:53 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-27 11:08:53 --> REQ ----------->14
DEBUG - 2025-06-27 11:08:54 --> REQ ----------->
INFO - 2025-06-27 11:08:54 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-27 11:09:39 --> REQ ----------->
INFO - 2025-06-27 11:09:39 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-27 11:11:20 --> REQ ----------->
INFO - 2025-06-27 11:11:20 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-27 11:11:27 --> REQ ----------->
INFO - 2025-06-27 11:11:27 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2025-06-27 11:11:28 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-27 11:11:29 --> REQ ----------->97
DEBUG - 2025-06-27 11:11:29 --> REQ ----------->
INFO - 2025-06-27 11:11:29 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-27 11:11:32 --> REQ ----------->
INFO - 2025-06-27 11:11:32 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-27 11:11:35 --> REQ ----------->
INFO - 2025-06-27 11:11:35 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2025-06-27 11:12:36 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-27 11:12:37 --> REQ ----------->14
DEBUG - 2025-06-27 11:12:37 --> REQ ----------->
INFO - 2025-06-27 11:12:37 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-27 11:13:23 --> REQ ----------->
INFO - 2025-06-27 11:13:23 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-27 11:14:47 --> REQ ----------->
INFO - 2025-06-27 11:14:47 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2025-06-27 11:14:47 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-27 11:14:48 --> REQ ----------->14
DEBUG - 2025-06-27 11:14:48 --> REQ ----------->
INFO - 2025-06-27 11:14:48 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-27 11:14:56 --> REQ ----------->
INFO - 2025-06-27 11:14:56 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-27 11:15:30 --> REQ ----------->
INFO - 2025-06-27 11:15:30 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2025-06-27 11:15:30 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-27 11:15:31 --> REQ ----------->14
DEBUG - 2025-06-27 11:15:31 --> REQ ----------->
INFO - 2025-06-27 11:15:31 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-27 11:16:30 --> REQ ----------->
INFO - 2025-06-27 11:16:30 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-27 11:16:43 --> REQ ----------->
INFO - 2025-06-27 11:16:43 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2025-06-27 11:16:43 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-27 11:16:44 --> REQ ----------->14
DEBUG - 2025-06-27 11:16:44 --> REQ ----------->
INFO - 2025-06-27 11:16:44 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-27 11:17:15 --> REQ ----------->
INFO - 2025-06-27 11:17:15 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2025-06-27 11:17:15 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-27 11:17:16 --> REQ ----------->14
DEBUG - 2025-06-27 11:17:16 --> REQ ----------->
INFO - 2025-06-27 11:17:16 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-27 11:17:28 --> REQ ----------->14
DEBUG - 2025-06-27 11:17:29 --> REQ ----------->
INFO - 2025-06-27 11:17:29 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2025-06-27 11:17:29 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-27 11:17:30 --> REQ ----------->14
DEBUG - 2025-06-27 11:17:30 --> REQ ----------->
INFO - 2025-06-27 11:17:30 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-27 11:18:09 --> REQ ----------->14
DEBUG - 2025-06-27 11:18:09 --> REQ ----------->
INFO - 2025-06-27 11:18:09 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2025-06-27 11:18:09 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-27 11:18:10 --> REQ ----------->14
DEBUG - 2025-06-27 11:18:10 --> REQ ----------->
INFO - 2025-06-27 11:18:10 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-27 11:18:49 --> REQ ----------->14
DEBUG - 2025-06-27 11:18:49 --> REQ ----------->
INFO - 2025-06-27 11:18:49 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2025-06-27 11:18:50 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-27 11:18:51 --> REQ ----------->14
DEBUG - 2025-06-27 11:18:51 --> REQ ----------->
INFO - 2025-06-27 11:18:51 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-27 11:19:54 --> REQ ----------->14
DEBUG - 2025-06-27 11:19:54 --> REQ ----------->
INFO - 2025-06-27 11:19:54 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2025-06-27 11:19:55 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-27 11:19:56 --> REQ ----------->14
DEBUG - 2025-06-27 11:19:56 --> REQ ----------->
INFO - 2025-06-27 11:19:56 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-27 11:20:08 --> REQ ----------->14
DEBUG - 2025-06-27 11:20:08 --> REQ ----------->
INFO - 2025-06-27 11:20:08 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2025-06-27 11:20:08 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-27 11:20:09 --> REQ ----------->14
DEBUG - 2025-06-27 11:20:10 --> REQ ----------->
INFO - 2025-06-27 11:20:10 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-27 11:20:46 --> REQ ----------->14
DEBUG - 2025-06-27 11:20:46 --> REQ ----------->
INFO - 2025-06-27 11:20:46 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
ERROR - 2025-06-27 11:21:11 --> mysqli_sql_exception: Operand should contain 1 column(s) in C:\laragon\www\app.ai-pro.org\btapp\vendor\codeigniter4\framework\system\Database\MySQLi\Connection.php:295
Stack trace:
#0 C:\laragon\www\app.ai-pro.org\btapp\vendor\codeigniter4\framework\system\Database\MySQLi\Connection.php(295): mysqli->query('UPDATE `start-p...', 0)
#1 C:\laragon\www\app.ai-pro.org\btapp\vendor\codeigniter4\framework\system\Database\BaseConnection.php(692): CodeIgniter\Database\MySQLi\Connection->execute('UPDATE `start-p...')
#2 C:\laragon\www\app.ai-pro.org\btapp\vendor\codeigniter4\framework\system\Database\BaseConnection.php(606): CodeIgniter\Database\BaseConnection->simpleQuery('UPDATE `start-p...')
#3 C:\laragon\www\app.ai-pro.org\btapp\vendor\codeigniter4\framework\system\Database\BaseBuilder.php(2476): CodeIgniter\Database\BaseConnection->query('UPDATE `start-p...', Array, false)
#4 C:\laragon\www\app.ai-pro.org\btapp\app\Database\Seeds\UpdatePlanDescriptionSeeder.php(63): CodeIgniter\Database\BaseBuilder->update(Array)
#5 C:\laragon\www\app.ai-pro.org\btapp\vendor\codeigniter4\framework\system\Database\Seeder.php(146): App\Database\Seeds\UpdatePlanDescriptionSeeder->run()
#6 C:\laragon\www\app.ai-pro.org\btapp\app\Database\Seeds\InsertPlanTableV2.php(14124): CodeIgniter\Database\Seeder->call('App\\Database\\Se...')
#7 C:\laragon\www\app.ai-pro.org\btapp\vendor\codeigniter4\framework\system\Database\Seeder.php(146): App\Database\Seeds\InsertPlanTableV2->run()
#8 C:\laragon\www\app.ai-pro.org\btapp\vendor\codeigniter4\framework\system\Commands\Database\Seed.php(77): CodeIgniter\Database\Seeder->call('App\\Database\\Se...')
#9 C:\laragon\www\app.ai-pro.org\btapp\vendor\codeigniter4\framework\system\CLI\Commands.php(65): CodeIgniter\Commands\Database\Seed->run(Array)
#10 C:\laragon\www\app.ai-pro.org\btapp\vendor\codeigniter4\framework\system\CLI\Console.php(37): CodeIgniter\CLI\Commands->run('db:seed', Array)
#11 C:\laragon\www\app.ai-pro.org\btapp\spark(97): CodeIgniter\CLI\Console->run()
#12 {main}
ERROR - 2025-06-27 11:21:11 --> mysqli_sql_exception: Operand should contain 1 column(s) in C:\laragon\www\app.ai-pro.org\btapp\vendor\codeigniter4\framework\system\Database\MySQLi\Connection.php:295
Stack trace:
#0 C:\laragon\www\app.ai-pro.org\btapp\vendor\codeigniter4\framework\system\Database\MySQLi\Connection.php(295): mysqli->query('UPDATE `start-p...', 0)
#1 C:\laragon\www\app.ai-pro.org\btapp\vendor\codeigniter4\framework\system\Database\BaseConnection.php(692): CodeIgniter\Database\MySQLi\Connection->execute('UPDATE `start-p...')
#2 C:\laragon\www\app.ai-pro.org\btapp\vendor\codeigniter4\framework\system\Database\BaseConnection.php(606): CodeIgniter\Database\BaseConnection->simpleQuery('UPDATE `start-p...')
#3 C:\laragon\www\app.ai-pro.org\btapp\vendor\codeigniter4\framework\system\Database\BaseBuilder.php(2476): CodeIgniter\Database\BaseConnection->query('UPDATE `start-p...', Array, false)
#4 C:\laragon\www\app.ai-pro.org\btapp\app\Database\Seeds\UpdatePlanDescriptionSeeder.php(63): CodeIgniter\Database\BaseBuilder->update(Array)
#5 C:\laragon\www\app.ai-pro.org\btapp\vendor\codeigniter4\framework\system\Database\Seeder.php(146): App\Database\Seeds\UpdatePlanDescriptionSeeder->run()
#6 C:\laragon\www\app.ai-pro.org\btapp\app\Database\Seeds\InsertPlanTableV2.php(14124): CodeIgniter\Database\Seeder->call('App\\Database\\Se...')
#7 C:\laragon\www\app.ai-pro.org\btapp\vendor\codeigniter4\framework\system\Database\Seeder.php(146): App\Database\Seeds\InsertPlanTableV2->run()
#8 C:\laragon\www\app.ai-pro.org\btapp\vendor\codeigniter4\framework\system\Commands\Database\Seed.php(77): CodeIgniter\Database\Seeder->call('App\\Database\\Se...')
#9 C:\laragon\www\app.ai-pro.org\btapp\vendor\codeigniter4\framework\system\CLI\Commands.php(65): CodeIgniter\Commands\Database\Seed->run(Array)
#10 C:\laragon\www\app.ai-pro.org\btapp\vendor\codeigniter4\framework\system\CLI\Console.php(37): CodeIgniter\CLI\Commands->run('db:seed', Array)
#11 C:\laragon\www\app.ai-pro.org\btapp\spark(97): CodeIgniter\CLI\Console->run()
#12 {main}
DEBUG - 2025-06-27 11:21:17 --> REQ ----------->14
DEBUG - 2025-06-27 11:21:17 --> REQ ----------->
INFO - 2025-06-27 11:21:17 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
ERROR - 2025-06-27 11:21:43 --> mysqli_sql_exception: Operand should contain 1 column(s) in C:\laragon\www\app.ai-pro.org\btapp\vendor\codeigniter4\framework\system\Database\MySQLi\Connection.php:295
Stack trace:
#0 C:\laragon\www\app.ai-pro.org\btapp\vendor\codeigniter4\framework\system\Database\MySQLi\Connection.php(295): mysqli->query('UPDATE `start-p...', 0)
#1 C:\laragon\www\app.ai-pro.org\btapp\vendor\codeigniter4\framework\system\Database\BaseConnection.php(692): CodeIgniter\Database\MySQLi\Connection->execute('UPDATE `start-p...')
#2 C:\laragon\www\app.ai-pro.org\btapp\vendor\codeigniter4\framework\system\Database\BaseConnection.php(606): CodeIgniter\Database\BaseConnection->simpleQuery('UPDATE `start-p...')
#3 C:\laragon\www\app.ai-pro.org\btapp\vendor\codeigniter4\framework\system\Database\BaseBuilder.php(2476): CodeIgniter\Database\BaseConnection->query('UPDATE `start-p...', Array, false)
#4 C:\laragon\www\app.ai-pro.org\btapp\app\Database\Seeds\UpdatePlanDescriptionSeeder.php(63): CodeIgniter\Database\BaseBuilder->update(Array)
#5 C:\laragon\www\app.ai-pro.org\btapp\vendor\codeigniter4\framework\system\Database\Seeder.php(146): App\Database\Seeds\UpdatePlanDescriptionSeeder->run()
#6 C:\laragon\www\app.ai-pro.org\btapp\vendor\codeigniter4\framework\system\Commands\Database\Seed.php(77): CodeIgniter\Database\Seeder->call('App\\Database\\Se...')
#7 C:\laragon\www\app.ai-pro.org\btapp\vendor\codeigniter4\framework\system\CLI\Commands.php(65): CodeIgniter\Commands\Database\Seed->run(Array)
#8 C:\laragon\www\app.ai-pro.org\btapp\vendor\codeigniter4\framework\system\CLI\Console.php(37): CodeIgniter\CLI\Commands->run('db:seed', Array)
#9 C:\laragon\www\app.ai-pro.org\btapp\spark(97): CodeIgniter\CLI\Console->run()
#10 {main}
ERROR - 2025-06-27 11:21:43 --> mysqli_sql_exception: Operand should contain 1 column(s) in C:\laragon\www\app.ai-pro.org\btapp\vendor\codeigniter4\framework\system\Database\MySQLi\Connection.php:295
Stack trace:
#0 C:\laragon\www\app.ai-pro.org\btapp\vendor\codeigniter4\framework\system\Database\MySQLi\Connection.php(295): mysqli->query('UPDATE `start-p...', 0)
#1 C:\laragon\www\app.ai-pro.org\btapp\vendor\codeigniter4\framework\system\Database\BaseConnection.php(692): CodeIgniter\Database\MySQLi\Connection->execute('UPDATE `start-p...')
#2 C:\laragon\www\app.ai-pro.org\btapp\vendor\codeigniter4\framework\system\Database\BaseConnection.php(606): CodeIgniter\Database\BaseConnection->simpleQuery('UPDATE `start-p...')
#3 C:\laragon\www\app.ai-pro.org\btapp\vendor\codeigniter4\framework\system\Database\BaseBuilder.php(2476): CodeIgniter\Database\BaseConnection->query('UPDATE `start-p...', Array, false)
#4 C:\laragon\www\app.ai-pro.org\btapp\app\Database\Seeds\UpdatePlanDescriptionSeeder.php(63): CodeIgniter\Database\BaseBuilder->update(Array)
#5 C:\laragon\www\app.ai-pro.org\btapp\vendor\codeigniter4\framework\system\Database\Seeder.php(146): App\Database\Seeds\UpdatePlanDescriptionSeeder->run()
#6 C:\laragon\www\app.ai-pro.org\btapp\vendor\codeigniter4\framework\system\Commands\Database\Seed.php(77): CodeIgniter\Database\Seeder->call('App\\Database\\Se...')
#7 C:\laragon\www\app.ai-pro.org\btapp\vendor\codeigniter4\framework\system\CLI\Commands.php(65): CodeIgniter\Commands\Database\Seed->run(Array)
#8 C:\laragon\www\app.ai-pro.org\btapp\vendor\codeigniter4\framework\system\CLI\Console.php(37): CodeIgniter\CLI\Commands->run('db:seed', Array)
#9 C:\laragon\www\app.ai-pro.org\btapp\spark(97): CodeIgniter\CLI\Console->run()
#10 {main}
ERROR - 2025-06-27 11:21:57 --> mysqli_sql_exception: Operand should contain 1 column(s) in C:\laragon\www\app.ai-pro.org\btapp\vendor\codeigniter4\framework\system\Database\MySQLi\Connection.php:295
Stack trace:
#0 C:\laragon\www\app.ai-pro.org\btapp\vendor\codeigniter4\framework\system\Database\MySQLi\Connection.php(295): mysqli->query('UPDATE `start-p...', 0)
#1 C:\laragon\www\app.ai-pro.org\btapp\vendor\codeigniter4\framework\system\Database\BaseConnection.php(692): CodeIgniter\Database\MySQLi\Connection->execute('UPDATE `start-p...')
#2 C:\laragon\www\app.ai-pro.org\btapp\vendor\codeigniter4\framework\system\Database\BaseConnection.php(606): CodeIgniter\Database\BaseConnection->simpleQuery('UPDATE `start-p...')
#3 C:\laragon\www\app.ai-pro.org\btapp\vendor\codeigniter4\framework\system\Database\BaseBuilder.php(2476): CodeIgniter\Database\BaseConnection->query('UPDATE `start-p...', Array, false)
#4 C:\laragon\www\app.ai-pro.org\btapp\app\Database\Seeds\UpdatePlanDescriptionSeeder.php(63): CodeIgniter\Database\BaseBuilder->update(Array)
#5 C:\laragon\www\app.ai-pro.org\btapp\vendor\codeigniter4\framework\system\Database\Seeder.php(146): App\Database\Seeds\UpdatePlanDescriptionSeeder->run()
#6 C:\laragon\www\app.ai-pro.org\btapp\vendor\codeigniter4\framework\system\Commands\Database\Seed.php(77): CodeIgniter\Database\Seeder->call('App\\Database\\Se...')
#7 C:\laragon\www\app.ai-pro.org\btapp\vendor\codeigniter4\framework\system\CLI\Commands.php(65): CodeIgniter\Commands\Database\Seed->run(Array)
#8 C:\laragon\www\app.ai-pro.org\btapp\vendor\codeigniter4\framework\system\CLI\Console.php(37): CodeIgniter\CLI\Commands->run('db:seed', Array)
#9 C:\laragon\www\app.ai-pro.org\btapp\spark(97): CodeIgniter\CLI\Console->run()
#10 {main}
ERROR - 2025-06-27 11:21:57 --> mysqli_sql_exception: Operand should contain 1 column(s) in C:\laragon\www\app.ai-pro.org\btapp\vendor\codeigniter4\framework\system\Database\MySQLi\Connection.php:295
Stack trace:
#0 C:\laragon\www\app.ai-pro.org\btapp\vendor\codeigniter4\framework\system\Database\MySQLi\Connection.php(295): mysqli->query('UPDATE `start-p...', 0)
#1 C:\laragon\www\app.ai-pro.org\btapp\vendor\codeigniter4\framework\system\Database\BaseConnection.php(692): CodeIgniter\Database\MySQLi\Connection->execute('UPDATE `start-p...')
#2 C:\laragon\www\app.ai-pro.org\btapp\vendor\codeigniter4\framework\system\Database\BaseConnection.php(606): CodeIgniter\Database\BaseConnection->simpleQuery('UPDATE `start-p...')
#3 C:\laragon\www\app.ai-pro.org\btapp\vendor\codeigniter4\framework\system\Database\BaseBuilder.php(2476): CodeIgniter\Database\BaseConnection->query('UPDATE `start-p...', Array, false)
#4 C:\laragon\www\app.ai-pro.org\btapp\app\Database\Seeds\UpdatePlanDescriptionSeeder.php(63): CodeIgniter\Database\BaseBuilder->update(Array)
#5 C:\laragon\www\app.ai-pro.org\btapp\vendor\codeigniter4\framework\system\Database\Seeder.php(146): App\Database\Seeds\UpdatePlanDescriptionSeeder->run()
#6 C:\laragon\www\app.ai-pro.org\btapp\vendor\codeigniter4\framework\system\Commands\Database\Seed.php(77): CodeIgniter\Database\Seeder->call('App\\Database\\Se...')
#7 C:\laragon\www\app.ai-pro.org\btapp\vendor\codeigniter4\framework\system\CLI\Commands.php(65): CodeIgniter\Commands\Database\Seed->run(Array)
#8 C:\laragon\www\app.ai-pro.org\btapp\vendor\codeigniter4\framework\system\CLI\Console.php(37): CodeIgniter\CLI\Commands->run('db:seed', Array)
#9 C:\laragon\www\app.ai-pro.org\btapp\spark(97): CodeIgniter\CLI\Console->run()
#10 {main}
DEBUG - 2025-06-27 11:24:58 --> REQ ----------->14
DEBUG - 2025-06-27 11:24:58 --> REQ ----------->
INFO - 2025-06-27 11:24:58 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-27 11:33:54 --> REQ ----------->14
DEBUG - 2025-06-27 11:33:54 --> REQ ----------->
INFO - 2025-06-27 11:33:54 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-27 11:58:45 --> REQ ----------->14
DEBUG - 2025-06-27 11:58:45 --> REQ ----------->
INFO - 2025-06-27 11:58:45 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-27 11:58:48 --> REQ ----------->14
DEBUG - 2025-06-27 11:58:48 --> REQ ----------->
INFO - 2025-06-27 11:58:48 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-27 11:59:19 --> REQ ----------->14
DEBUG - 2025-06-27 11:59:19 --> REQ ----------->
INFO - 2025-06-27 11:59:19 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-27 12:07:21 --> REQ ----------->14
DEBUG - 2025-06-27 12:07:21 --> REQ ----------->
INFO - 2025-06-27 12:07:21 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2025-06-27 12:07:21 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-27 12:07:22 --> REQ ----------->14
DEBUG - 2025-06-27 12:07:22 --> REQ ----------->
INFO - 2025-06-27 12:07:22 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-27 12:08:29 --> REQ ----------->14
DEBUG - 2025-06-27 12:08:29 --> REQ ----------->
INFO - 2025-06-27 12:08:29 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2025-06-27 12:08:29 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-27 12:08:31 --> REQ ----------->14
DEBUG - 2025-06-27 12:08:31 --> REQ ----------->
INFO - 2025-06-27 12:08:31 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2025-06-27 12:08:39 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2025-06-27 12:08:42 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-27 12:08:43 --> REQ ----------->14
DEBUG - 2025-06-27 12:08:43 --> REQ ----------->
INFO - 2025-06-27 12:08:43 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-27 12:08:45 --> REQ ----------->
INFO - 2025-06-27 12:08:45 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-27 12:08:45 --> REQ ----------->
INFO - 2025-06-27 12:08:45 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2025-06-27 12:08:46 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-27 12:08:46 --> REQ ----------->97
DEBUG - 2025-06-27 12:08:47 --> REQ ----------->
INFO - 2025-06-27 12:08:47 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-27 12:09:45 --> REQ ----------->
INFO - 2025-06-27 12:09:45 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2025-06-27 12:09:45 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-27 12:09:46 --> REQ ----------->97
DEBUG - 2025-06-27 12:09:46 --> REQ ----------->
INFO - 2025-06-27 12:09:46 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-27 12:09:50 --> REQ ----------->
INFO - 2025-06-27 12:09:50 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2025-06-27 12:09:54 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-27 12:09:54 --> REQ ----------->
INFO - 2025-06-27 12:09:54 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-27 12:09:55 --> REQ ----------->14
DEBUG - 2025-06-27 12:09:55 --> REQ ----------->
INFO - 2025-06-27 12:09:55 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-27 12:09:55 --> REQ ----------->
INFO - 2025-06-27 12:09:55 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-27 12:09:56 --> REQ ----------->
INFO - 2025-06-27 12:09:56 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-27 12:09:57 --> REQ ----------->
INFO - 2025-06-27 12:09:57 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-27 12:09:59 --> REQ ----------->
INFO - 2025-06-27 12:09:59 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-27 12:10:06 --> REQ ----------->
INFO - 2025-06-27 12:10:06 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-27 12:10:06 --> REQ ----------->
INFO - 2025-06-27 12:10:06 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-27 12:10:07 --> REQ ----------->
INFO - 2025-06-27 12:10:07 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2025-06-27 12:16:21 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-27 12:16:21 --> REQ ----------->14
DEBUG - 2025-06-27 12:16:22 --> REQ ----------->
INFO - 2025-06-27 12:16:22 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
