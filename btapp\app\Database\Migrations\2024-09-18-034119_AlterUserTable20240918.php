<?php

namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;

class AlterUserTable20240918 extends Migration
{
    private $table = "user";
    public function up()
    {
        //
        if ($this->db->fieldExists('cookies', $this->table)) return;
        $this->db->disableForeignKeyChecks();
        $fields = [
            'cookies' => [
                'type' => 'TEXT',
                'default' => '',
                'null' => true,
                'after' => 'verify',
            ]
        ];

        $this->forge->addColumn($this->table, $fields);

        $this->db->enableForeignKeyChecks();
    }

    public function down()
    {
        //
    }
}
