<?php

namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;

class AddValueToPlanType extends Migration
{
    private $table = "plan";

    public function up()
    {
        $fields = [
            'plan_type' => [
                'type' => 'SET',
                'constraint' => ['Pro', 'ProMax', 'Basic', 'Enterprise', 'Advanced'],
                'null' => true,
                'default' => null,
                'after' => 'plan_name',
                'collation' => 'utf8_general_ci',
            ],
        ];

        $this->forge->modifyColumn($this->table, $fields);
    }

    public function down()
    {
        $fields = [
            'plan_type' => [
                'type' => 'SET',
                'constraint' => ['Pro', 'ProMax', 'Basic', 'Enterprise'], // Original set options
                'null' => true,
                'default' => null,
                'after' => 'plan_name',
                'collation' => 'utf8_general_ci',
            ],
        ];

        $this->forge->modifyColumn($this->table, $fields);
    }
}
