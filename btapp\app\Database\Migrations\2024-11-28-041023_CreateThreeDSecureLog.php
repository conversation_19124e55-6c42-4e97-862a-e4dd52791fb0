<?php

namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;

class CreateThreeDSecureLog extends Migration
{
    private $table = "threed_secure_log";

    public function up()
    {
		$this->db->disableForeignKeyChecks();

		$this->forge->addField([
			'log_id' => [
				'type' => 'INT',
				'constraint' => 11,
				'unsigned' => false,
				'auto_increment' => true,
			],
			'user_id' => [
				'type' => 'INT',
				'constraint' => 11,
				'null' => true,
            ],
			'email' => [
				'type' => 'VARCHAR',
                'constraint' => 250,
				'null' => true,
            ],
			'mode' => [
				'type' => 'VARCHAR',
				'constraint' => 10,
				'null' => true,
			],
			'payment_intent' => [
				'type' => 'VARCHAR',
				'constraint' => 100,
				'null' => true,
			],
			'subscription_id' => [
				'type' => 'VARCHAR',
				'constraint' => 100,
				'null' => true,
			],
			'plan_id' => [
				'type' => 'VARCHAR',
				'constraint' => 100,
				'null' => true,
			],
			'cc_num' => [
				'type' => 'VARCHAR',
				'constraint' => 100,
				'null' => true,
			],
			'cc_num_encrypt' => [
				'type' => 'VARCHAR',
				'constraint' => 100,
				'null' => true,
			],
			'site' => [
				'type' => 'VARCHAR',
				'constraint' => 100,
				'null' => true,
			],
			'processed' => [
				'type' => 'VARCHAR',
				'constraint' => 10,
				'null' => true,
			],
            'created_at' => [
				'type' => 'DATETIME',
				'null' => false,
			],
			'updated_at' => [
				'type' => 'DATETIME',
				'null' => false,
			],
			'deleted_at' => [
				'type' => 'DATETIME',
				'null' => false,
			],
		]);

		$this->forge->addKey('log_id', true);

		$this->forge->createTable($this->table);
		$this->db->enableForeignKeyChecks();
    }

    public function down()
    {
        //
    }
}
