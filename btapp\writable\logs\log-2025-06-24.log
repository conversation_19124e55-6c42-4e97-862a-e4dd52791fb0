DEBUG - 2025-06-24 00:00:59 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-24 00:00:59 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
WARNING - 2025-06-24 00:00:59 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('aiproStart', null, 1753315259, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(401): btflag_set('aiproStart', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-24 00:00:59 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('user_email', null, 1753315259, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(402): btflag_set('user_email', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-24 00:00:59 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('chatpro_KJLF4XgSL8wjlGm', null, 1753315259, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(403): btflag_set('chatpro_KJLF4XgSL8wjlGm', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-06-24 00:00:59 --> REQ ----------->
INFO - 2025-06-24 00:00:59 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2025-06-24 00:01:01 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 00:01:01 --> btdbFindBy ---> 
DEBUG - 2025-06-24 00:01:01 --> btdbFindBy ---> SELECT `start-plan`.*, `start-account`.*, DATE_FORMAT(resumed_at , "%M %e, %Y") as "resumed_at_formatted", `start-user`.`mode`, case when DATE_ADD(end_date, INTERVAL 1 DAY) < now() then 'yes' else 'no' end as 'expired'
FROM `start-account`
JOIN `start-user` ON `start-user`.`user_id` = `start-account`.`user_id`
JOIN `start-plan` ON `start-plan`.`plan_id` = `start-account`.`plan_id`
WHERE `start-account`.`user_id` = '2'
ORDER BY `start-account`.`end_date` DESC
DEBUG - 2025-06-24 00:01:03 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-24 00:01:03 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
WARNING - 2025-06-24 00:01:03 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('aiproStart', null, **********, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(401): btflag_set('aiproStart', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-24 00:01:03 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('user_email', null, **********, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(402): btflag_set('user_email', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-24 00:01:03 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('chatpro_KJLF4XgSL8wjlGm', null, **********, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(403): btflag_set('chatpro_KJLF4XgSL8wjlGm', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-06-24 00:01:03 --> REQ ----------->
INFO - 2025-06-24 00:01:03 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 00:01:04 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
DEBUG - 2025-06-24 00:01:04 --> btdbFindBy ---> 
INFO - 2025-06-24 00:01:04 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 00:01:05 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
DEBUG - 2025-06-24 00:01:05 --> btdbFindBy ---> 
INFO - 2025-06-24 00:01:05 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 00:01:05 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-24 00:01:05 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
WARNING - 2025-06-24 00:01:05 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('aiproStart', null, 1753315265, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(401): btflag_set('aiproStart', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-24 00:01:05 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('user_email', null, 1753315265, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(402): btflag_set('user_email', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-24 00:01:05 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('chatpro_KJLF4XgSL8wjlGm', null, 1753315265, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(403): btflag_set('chatpro_KJLF4XgSL8wjlGm', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-06-24 00:01:05 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-24 00:01:05 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 00:01:41 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-24 00:01:41 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
WARNING - 2025-06-24 00:01:41 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('aiproStart', null, 1753315301, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(401): btflag_set('aiproStart', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-24 00:01:41 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('user_email', null, 1753315301, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(402): btflag_set('user_email', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-24 00:01:41 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('chatpro_KJLF4XgSL8wjlGm', null, 1753315301, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(403): btflag_set('chatpro_KJLF4XgSL8wjlGm', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-06-24 00:01:41 --> REQ ----------->
INFO - 2025-06-24 00:01:41 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 00:01:48 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-24 00:01:48 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
WARNING - 2025-06-24 00:01:48 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('aiproStart', null, 1753315308, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(401): btflag_set('aiproStart', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-24 00:01:48 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('user_email', null, 1753315308, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(402): btflag_set('user_email', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-24 00:01:48 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('chatpro_KJLF4XgSL8wjlGm', null, 1753315308, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(403): btflag_set('chatpro_KJLF4XgSL8wjlGm', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-06-24 00:01:48 --> REQ ----------->
INFO - 2025-06-24 00:01:48 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2025-06-24 00:01:53 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 00:01:53 --> btdbFindBy ---> 
DEBUG - 2025-06-24 00:01:53 --> btdbFindBy ---> SELECT `start-plan`.*, `start-account`.*, DATE_FORMAT(resumed_at , "%M %e, %Y") as "resumed_at_formatted", `start-user`.`mode`, case when DATE_ADD(end_date, INTERVAL 1 DAY) < now() then 'yes' else 'no' end as 'expired'
FROM `start-account`
JOIN `start-user` ON `start-user`.`user_id` = `start-account`.`user_id`
JOIN `start-plan` ON `start-plan`.`plan_id` = `start-account`.`plan_id`
WHERE `start-account`.`user_id` = '2'
ORDER BY `start-account`.`end_date` DESC
DEBUG - 2025-06-24 00:01:54 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-24 00:01:54 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
WARNING - 2025-06-24 00:01:54 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('aiproStart', null, **********, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(401): btflag_set('aiproStart', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-24 00:01:54 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('user_email', null, **********, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(402): btflag_set('user_email', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-24 00:01:54 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('chatpro_KJLF4XgSL8wjlGm', null, **********, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(403): btflag_set('chatpro_KJLF4XgSL8wjlGm', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-06-24 00:01:54 --> REQ ----------->
INFO - 2025-06-24 00:01:54 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 00:01:56 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
DEBUG - 2025-06-24 00:01:56 --> btdbFindBy ---> 
INFO - 2025-06-24 00:01:56 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 00:01:56 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
DEBUG - 2025-06-24 00:01:56 --> btdbFindBy ---> 
INFO - 2025-06-24 00:01:56 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 00:01:56 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-24 00:01:56 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
WARNING - 2025-06-24 00:01:56 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('aiproStart', null, 1753315316, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(401): btflag_set('aiproStart', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-24 00:01:56 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('user_email', null, 1753315316, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(402): btflag_set('user_email', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-24 00:01:56 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('chatpro_KJLF4XgSL8wjlGm', null, 1753315316, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(403): btflag_set('chatpro_KJLF4XgSL8wjlGm', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-06-24 00:01:56 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-24 00:01:56 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 00:03:39 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-24 00:03:39 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
WARNING - 2025-06-24 00:03:39 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('aiproStart', null, 1753315419, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(401): btflag_set('aiproStart', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-24 00:03:39 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('user_email', null, 1753315419, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(402): btflag_set('user_email', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-24 00:03:39 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('chatpro_KJLF4XgSL8wjlGm', null, 1753315419, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(403): btflag_set('chatpro_KJLF4XgSL8wjlGm', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-06-24 00:03:40 --> REQ ----------->
INFO - 2025-06-24 00:03:40 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 00:04:05 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-24 00:04:05 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
WARNING - 2025-06-24 00:04:05 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('aiproStart', null, 1753315445, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(401): btflag_set('aiproStart', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-24 00:04:05 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('user_email', null, 1753315445, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(402): btflag_set('user_email', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-24 00:04:05 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('chatpro_KJLF4XgSL8wjlGm', null, 1753315445, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(403): btflag_set('chatpro_KJLF4XgSL8wjlGm', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-06-24 00:04:05 --> REQ ----------->
INFO - 2025-06-24 00:04:05 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2025-06-24 00:04:09 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 00:04:09 --> btdbFindBy ---> 
DEBUG - 2025-06-24 00:04:09 --> btdbFindBy ---> SELECT `start-plan`.*, `start-account`.*, DATE_FORMAT(resumed_at , "%M %e, %Y") as "resumed_at_formatted", `start-user`.`mode`, case when DATE_ADD(end_date, INTERVAL 1 DAY) < now() then 'yes' else 'no' end as 'expired'
FROM `start-account`
JOIN `start-user` ON `start-user`.`user_id` = `start-account`.`user_id`
JOIN `start-plan` ON `start-plan`.`plan_id` = `start-account`.`plan_id`
WHERE `start-account`.`user_id` = '2'
ORDER BY `start-account`.`end_date` DESC
DEBUG - 2025-06-24 00:04:09 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-24 00:04:09 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
WARNING - 2025-06-24 00:04:09 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('aiproStart', null, **********, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(401): btflag_set('aiproStart', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-24 00:04:09 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('user_email', null, **********, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(402): btflag_set('user_email', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-24 00:04:09 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('chatpro_KJLF4XgSL8wjlGm', null, **********, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(403): btflag_set('chatpro_KJLF4XgSL8wjlGm', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-06-24 00:04:09 --> REQ ----------->
INFO - 2025-06-24 00:04:09 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 00:04:12 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
DEBUG - 2025-06-24 00:04:12 --> btdbFindBy ---> 
INFO - 2025-06-24 00:04:12 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 00:04:13 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
DEBUG - 2025-06-24 00:04:13 --> btdbFindBy ---> 
INFO - 2025-06-24 00:04:13 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 00:04:13 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-24 00:04:13 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
WARNING - 2025-06-24 00:04:13 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('aiproStart', null, 1753315453, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(401): btflag_set('aiproStart', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-24 00:04:13 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('user_email', null, 1753315453, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(402): btflag_set('user_email', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-24 00:04:13 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('chatpro_KJLF4XgSL8wjlGm', null, 1753315453, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(403): btflag_set('chatpro_KJLF4XgSL8wjlGm', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-06-24 00:04:13 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-24 00:04:13 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 00:08:03 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-24 00:08:03 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
WARNING - 2025-06-24 00:08:03 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('aiproStart', null, 1753315683, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(401): btflag_set('aiproStart', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-24 00:08:03 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('user_email', null, 1753315683, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(402): btflag_set('user_email', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-24 00:08:03 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('chatpro_KJLF4XgSL8wjlGm', null, 1753315683, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(403): btflag_set('chatpro_KJLF4XgSL8wjlGm', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-06-24 00:08:04 --> REQ ----------->
INFO - 2025-06-24 00:08:04 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 00:09:27 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-24 00:09:27 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
WARNING - 2025-06-24 00:09:27 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('aiproStart', null, 1753315767, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(401): btflag_set('aiproStart', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-24 00:09:27 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('user_email', null, 1753315767, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(402): btflag_set('user_email', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-24 00:09:27 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('chatpro_KJLF4XgSL8wjlGm', null, 1753315767, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(403): btflag_set('chatpro_KJLF4XgSL8wjlGm', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-06-24 00:09:27 --> REQ ----------->
INFO - 2025-06-24 00:09:27 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 00:09:32 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-24 00:09:32 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
WARNING - 2025-06-24 00:09:32 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('aiproStart', null, 1753315772, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(401): btflag_set('aiproStart', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-24 00:09:32 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('user_email', null, 1753315772, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(402): btflag_set('user_email', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-24 00:09:32 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('chatpro_KJLF4XgSL8wjlGm', null, 1753315772, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(403): btflag_set('chatpro_KJLF4XgSL8wjlGm', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-06-24 00:09:33 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-24 00:09:33 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
WARNING - 2025-06-24 00:09:33 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('aiproStart', null, 1753315773, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(401): btflag_set('aiproStart', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-24 00:09:33 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('user_email', null, 1753315773, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(402): btflag_set('user_email', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-24 00:09:33 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('chatpro_KJLF4XgSL8wjlGm', null, 1753315773, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(403): btflag_set('chatpro_KJLF4XgSL8wjlGm', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-06-24 00:09:33 --> REQ ----------->
INFO - 2025-06-24 00:09:33 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 01:28:45 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-24 01:28:45 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
WARNING - 2025-06-24 01:28:45 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('aiproStart', null, 1753320525, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(401): btflag_set('aiproStart', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-24 01:28:45 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('user_email', null, 1753320525, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(402): btflag_set('user_email', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-24 01:28:45 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('chatpro_KJLF4XgSL8wjlGm', null, 1753320525, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(403): btflag_set('chatpro_KJLF4XgSL8wjlGm', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-06-24 01:28:45 --> REQ ----------->
INFO - 2025-06-24 01:28:45 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2025-06-24 01:28:57 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 01:28:57 --> btdbFindBy ---> 
DEBUG - 2025-06-24 01:28:57 --> btdbFindBy ---> SELECT `start-plan`.*, `start-account`.*, DATE_FORMAT(resumed_at , "%M %e, %Y") as "resumed_at_formatted", `start-user`.`mode`, case when DATE_ADD(end_date, INTERVAL 1 DAY) < now() then 'yes' else 'no' end as 'expired'
FROM `start-account`
JOIN `start-user` ON `start-user`.`user_id` = `start-account`.`user_id`
JOIN `start-plan` ON `start-plan`.`plan_id` = `start-account`.`plan_id`
WHERE `start-account`.`user_id` = '2'
ORDER BY `start-account`.`end_date` DESC
WARNING - 2025-06-24 01:28:57 --> [DEPRECATED] strtolower(): Passing null to parameter #1 ($string) of type string is deprecated in APPPATH\Controllers\User\Settings.php on line 163.
 1 APPPATH\Controllers\User\Settings.php(163): strtolower(null)
 2 APPPATH\Controllers\User\Settings.php(59): App\Controllers\User\Settings->get_subs_data()
 3 APPPATH\Controllers\User\Settings.php(45): App\Controllers\User\Settings->getDiscountPricing()
 4 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\User\Settings->index()
 5 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\User\Settings))
 6 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 7 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 8 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-24 01:28:57 --> [DEPRECATED] strtolower(): Passing null to parameter #1 ($string) of type string is deprecated in APPPATH\Controllers\User\Settings.php on line 164.
 1 APPPATH\Controllers\User\Settings.php(164): strtolower(null)
 2 APPPATH\Controllers\User\Settings.php(59): App\Controllers\User\Settings->get_subs_data()
 3 APPPATH\Controllers\User\Settings.php(45): App\Controllers\User\Settings->getDiscountPricing()
 4 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\User\Settings->index()
 5 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\User\Settings))
 6 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 7 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 8 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-24 01:28:57 --> [DEPRECATED] strtolower(): Passing null to parameter #1 ($string) of type string is deprecated in APPPATH\Controllers\User\Settings.php on line 87.
 1 APPPATH\Controllers\User\Settings.php(87): strtolower(null)
 2 APPPATH\Controllers\User\Settings.php(45): App\Controllers\User\Settings->getDiscountPricing()
 3 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\User\Settings->index()
 4 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\User\Settings))
 5 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 6 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 7 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
INFO - 2025-06-24 01:29:59 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 01:30:00 --> REQ ----------->14
DEBUG - 2025-06-24 01:30:00 --> REQ ----------->
INFO - 2025-06-24 01:30:00 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 01:30:01 --> REQ ----------->
INFO - 2025-06-24 01:30:01 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 01:30:02 --> REQ ----------->
INFO - 2025-06-24 01:30:02 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 01:30:29 --> REQ ----------->
INFO - 2025-06-24 01:30:29 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 01:35:12 --> REQ ----------->
INFO - 2025-06-24 01:35:12 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 01:36:00 --> REQ ----------->
INFO - 2025-06-24 01:36:00 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 01:36:01 --> REQ ----------->
INFO - 2025-06-24 01:36:01 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 01:36:03 --> REQ ----------->
INFO - 2025-06-24 01:36:03 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 01:36:03 --> REQ ----------->
INFO - 2025-06-24 01:36:03 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 01:36:06 --> REQ ----------->testuser061125_01@mailinator.com123123
DEBUG - 2025-06-24 01:36:06 --> btdbFindBy ---> 
DEBUG - 2025-06-24 01:36:06 --> btdbFindBy ---> SELECT *
FROM `start-user`
WHERE `email` = '<EMAIL>'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
INFO - 2025-06-24 01:36:06 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 01:36:06 --> btdbFindBy ---> SELECT `start-plan`.*, `start-account`.*, DATE_FORMAT(resumed_at , "%M %e, %Y") as "resumed_at_formatted", `start-user`.`mode`, case when DATE_ADD(end_date, INTERVAL 1 DAY) < now() then 'yes' else 'no' end as 'expired'
FROM `start-account`
JOIN `start-user` ON `start-user`.`user_id` = `start-account`.`user_id`
JOIN `start-plan` ON `start-plan`.`plan_id` = `start-account`.`plan_id`
WHERE `start-account`.`user_id` = '2'
ORDER BY `start-account`.`end_date` DESC
DEBUG - 2025-06-24 01:36:06 --> btdbFindBy ---> SELECT *
FROM `start-plan`
WHERE `plan_id` = '143'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-24 01:36:06 --> login-------> <EMAIL>
DEBUG - 2025-06-24 01:36:06 --> btdbFindBy ---> SELECT *
FROM `start-user_app`
WHERE `email` = '<EMAIL>'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
INFO - 2025-06-24 01:36:06 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 01:36:06 --> btdbFindBy ---> 
DEBUG - 2025-06-24 01:36:06 --> btdbFindBy ---> SELECT `start-plan`.*, `start-account`.*, DATE_FORMAT(resumed_at , "%M %e, %Y") as "resumed_at_formatted", `start-user`.`mode`, case when DATE_ADD(end_date, INTERVAL 1 DAY) < now() then 'yes' else 'no' end as 'expired'
FROM `start-account`
JOIN `start-user` ON `start-user`.`user_id` = `start-account`.`user_id`
JOIN `start-plan` ON `start-plan`.`plan_id` = `start-account`.`plan_id`
WHERE `start-account`.`user_id` = '2'
ORDER BY `start-account`.`end_date` DESC
DEBUG - 2025-06-24 01:36:06 --> btdbFindBy ---> SELECT *
FROM `start-plan`
WHERE `plan_id` = '143'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-24 01:36:07 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-24 01:36:07 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
WARNING - 2025-06-24 01:36:07 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('aiproStart', null, **********, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(401): btflag_set('aiproStart', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-24 01:36:07 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('user_email', null, **********, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(402): btflag_set('user_email', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-24 01:36:07 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('chatpro_KJLF4XgSL8wjlGm', null, **********, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(403): btflag_set('chatpro_KJLF4XgSL8wjlGm', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-06-24 01:36:07 --> REQ ----------->
DEBUG - 2025-06-24 01:36:08 --> REQ ----------->
INFO - 2025-06-24 01:36:08 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 01:36:10 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
DEBUG - 2025-06-24 01:36:10 --> btdbFindBy ---> 
INFO - 2025-06-24 01:36:10 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
WARNING - 2025-06-24 01:36:10 --> [DEPRECATED] strtolower(): Passing null to parameter #1 ($string) of type string is deprecated in APPPATH\Controllers\Api.php on line 1534.
 1 APPPATH\Controllers\Api.php(1534): strtolower(null)
 2 APPPATH\Controllers\Api.php(189): App\Controllers\Api->getSubscription()
 3 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('get-subscription')
 4 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 5 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 6 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 7 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-24 01:36:10 --> [DEPRECATED] strtolower(): Passing null to parameter #1 ($string) of type string is deprecated in APPPATH\Controllers\Api.php on line 1535.
 1 APPPATH\Controllers\Api.php(1535): strtolower(null)
 2 APPPATH\Controllers\Api.php(189): App\Controllers\Api->getSubscription()
 3 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('get-subscription')
 4 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 5 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 6 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 7 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-06-24 01:36:10 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-24 01:36:10 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
WARNING - 2025-06-24 01:36:10 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('aiproStart', null, 1753320970, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(401): btflag_set('aiproStart', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-24 01:36:10 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('user_email', null, 1753320970, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(402): btflag_set('user_email', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-24 01:36:10 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('chatpro_KJLF4XgSL8wjlGm', null, 1753320970, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(403): btflag_set('chatpro_KJLF4XgSL8wjlGm', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
INFO - 2025-06-24 01:36:10 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 01:36:10 --> btdbFindBy ---> 
DEBUG - 2025-06-24 01:36:10 --> btdbFindBy ---> SELECT `start-plan`.*, `start-account`.*, DATE_FORMAT(resumed_at , "%M %e, %Y") as "resumed_at_formatted", `start-user`.`mode`, case when DATE_ADD(end_date, INTERVAL 1 DAY) < now() then 'yes' else 'no' end as 'expired'
FROM `start-account`
JOIN `start-user` ON `start-user`.`user_id` = `start-account`.`user_id`
JOIN `start-plan` ON `start-plan`.`plan_id` = `start-account`.`plan_id`
WHERE `start-account`.`user_id` = '2'
ORDER BY `start-account`.`end_date` DESC
WARNING - 2025-06-24 01:36:10 --> [DEPRECATED] strtolower(): Passing null to parameter #1 ($string) of type string is deprecated in APPPATH\Controllers\User\Settings.php on line 163.
 1 APPPATH\Controllers\User\Settings.php(163): strtolower(null)
 2 APPPATH\Controllers\User\Settings.php(59): App\Controllers\User\Settings->get_subs_data()
 3 APPPATH\Controllers\User\Settings.php(45): App\Controllers\User\Settings->getDiscountPricing()
 4 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\User\Settings->index()
 5 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\User\Settings))
 6 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 7 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 8 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-24 01:36:10 --> [DEPRECATED] strtolower(): Passing null to parameter #1 ($string) of type string is deprecated in APPPATH\Controllers\User\Settings.php on line 164.
 1 APPPATH\Controllers\User\Settings.php(164): strtolower(null)
 2 APPPATH\Controllers\User\Settings.php(59): App\Controllers\User\Settings->get_subs_data()
 3 APPPATH\Controllers\User\Settings.php(45): App\Controllers\User\Settings->getDiscountPricing()
 4 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\User\Settings->index()
 5 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\User\Settings))
 6 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 7 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 8 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-24 01:36:10 --> [DEPRECATED] strtolower(): Passing null to parameter #1 ($string) of type string is deprecated in APPPATH\Controllers\User\Settings.php on line 87.
 1 APPPATH\Controllers\User\Settings.php(87): strtolower(null)
 2 APPPATH\Controllers\User\Settings.php(45): App\Controllers\User\Settings->getDiscountPricing()
 3 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\User\Settings->index()
 4 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\User\Settings))
 5 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 6 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 7 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-06-24 01:36:11 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-24 01:36:11 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
WARNING - 2025-06-24 01:36:11 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('aiproStart', null, 1753320971, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(401): btflag_set('aiproStart', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-24 01:36:11 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('user_email', null, 1753320971, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(402): btflag_set('user_email', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-24 01:36:11 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('chatpro_KJLF4XgSL8wjlGm', null, 1753320971, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(403): btflag_set('chatpro_KJLF4XgSL8wjlGm', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-06-24 01:36:11 --> REQ ----------->
INFO - 2025-06-24 01:36:11 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 01:36:13 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
DEBUG - 2025-06-24 01:36:13 --> btdbFindBy ---> 
INFO - 2025-06-24 01:36:13 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 01:36:13 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
DEBUG - 2025-06-24 01:36:13 --> btdbFindBy ---> 
INFO - 2025-06-24 01:36:13 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
WARNING - 2025-06-24 01:36:13 --> [DEPRECATED] strtolower(): Passing null to parameter #1 ($string) of type string is deprecated in APPPATH\Controllers\Api.php on line 1534.
 1 APPPATH\Controllers\Api.php(1534): strtolower(null)
 2 APPPATH\Controllers\Api.php(189): App\Controllers\Api->getSubscription()
 3 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('get-subscription')
 4 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 5 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 6 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 7 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-24 01:36:13 --> [DEPRECATED] strtolower(): Passing null to parameter #1 ($string) of type string is deprecated in APPPATH\Controllers\Api.php on line 1535.
 1 APPPATH\Controllers\Api.php(1535): strtolower(null)
 2 APPPATH\Controllers\Api.php(189): App\Controllers\Api->getSubscription()
 3 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('get-subscription')
 4 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 5 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 6 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 7 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-06-24 01:36:14 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-24 01:36:14 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
WARNING - 2025-06-24 01:36:14 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('aiproStart', null, 1753320974, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(401): btflag_set('aiproStart', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-24 01:36:14 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('user_email', null, 1753320974, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(402): btflag_set('user_email', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-24 01:36:14 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('chatpro_KJLF4XgSL8wjlGm', null, 1753320974, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(403): btflag_set('chatpro_KJLF4XgSL8wjlGm', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-06-24 01:36:14 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-24 01:36:14 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 01:36:36 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-24 01:36:36 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
WARNING - 2025-06-24 01:36:36 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('aiproStart', null, 1753320996, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(401): btflag_set('aiproStart', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-24 01:36:36 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('user_email', null, 1753320996, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(402): btflag_set('user_email', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-24 01:36:36 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('chatpro_KJLF4XgSL8wjlGm', null, 1753320996, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(403): btflag_set('chatpro_KJLF4XgSL8wjlGm', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-06-24 01:36:36 --> REQ ----------->
INFO - 2025-06-24 01:36:36 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2025-06-24 01:36:39 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 01:36:39 --> btdbFindBy ---> 
DEBUG - 2025-06-24 01:36:39 --> btdbFindBy ---> SELECT `start-plan`.*, `start-account`.*, DATE_FORMAT(resumed_at , "%M %e, %Y") as "resumed_at_formatted", `start-user`.`mode`, case when DATE_ADD(end_date, INTERVAL 1 DAY) < now() then 'yes' else 'no' end as 'expired'
FROM `start-account`
JOIN `start-user` ON `start-user`.`user_id` = `start-account`.`user_id`
JOIN `start-plan` ON `start-plan`.`plan_id` = `start-account`.`plan_id`
WHERE `start-account`.`user_id` = '2'
ORDER BY `start-account`.`end_date` DESC
WARNING - 2025-06-24 01:36:39 --> [DEPRECATED] strtolower(): Passing null to parameter #1 ($string) of type string is deprecated in APPPATH\Controllers\User\Settings.php on line 163.
 1 APPPATH\Controllers\User\Settings.php(163): strtolower(null)
 2 APPPATH\Controllers\User\Settings.php(59): App\Controllers\User\Settings->get_subs_data()
 3 APPPATH\Controllers\User\Settings.php(45): App\Controllers\User\Settings->getDiscountPricing()
 4 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\User\Settings->index()
 5 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\User\Settings))
 6 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 7 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 8 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-24 01:36:39 --> [DEPRECATED] strtolower(): Passing null to parameter #1 ($string) of type string is deprecated in APPPATH\Controllers\User\Settings.php on line 164.
 1 APPPATH\Controllers\User\Settings.php(164): strtolower(null)
 2 APPPATH\Controllers\User\Settings.php(59): App\Controllers\User\Settings->get_subs_data()
 3 APPPATH\Controllers\User\Settings.php(45): App\Controllers\User\Settings->getDiscountPricing()
 4 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\User\Settings->index()
 5 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\User\Settings))
 6 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 7 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 8 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-24 01:36:39 --> [DEPRECATED] strtolower(): Passing null to parameter #1 ($string) of type string is deprecated in APPPATH\Controllers\User\Settings.php on line 87.
 1 APPPATH\Controllers\User\Settings.php(87): strtolower(null)
 2 APPPATH\Controllers\User\Settings.php(45): App\Controllers\User\Settings->getDiscountPricing()
 3 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\User\Settings->index()
 4 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\User\Settings))
 5 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 6 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 7 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-06-24 01:36:39 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-24 01:36:39 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
WARNING - 2025-06-24 01:36:39 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('aiproStart', null, 1753320999, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(401): btflag_set('aiproStart', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-24 01:36:39 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('user_email', null, 1753320999, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(402): btflag_set('user_email', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-24 01:36:39 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('chatpro_KJLF4XgSL8wjlGm', null, 1753320999, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(403): btflag_set('chatpro_KJLF4XgSL8wjlGm', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-06-24 01:36:39 --> REQ ----------->
INFO - 2025-06-24 01:36:39 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 01:36:42 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
DEBUG - 2025-06-24 01:36:42 --> btdbFindBy ---> 
INFO - 2025-06-24 01:36:42 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 01:36:42 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
DEBUG - 2025-06-24 01:36:42 --> btdbFindBy ---> 
INFO - 2025-06-24 01:36:42 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
WARNING - 2025-06-24 01:36:42 --> [DEPRECATED] strtolower(): Passing null to parameter #1 ($string) of type string is deprecated in APPPATH\Controllers\Api.php on line 1536.
 1 APPPATH\Controllers\Api.php(1536): strtolower(null)
 2 APPPATH\Controllers\Api.php(189): App\Controllers\Api->getSubscription()
 3 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('get-subscription')
 4 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 5 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 6 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 7 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-24 01:36:42 --> [DEPRECATED] strtolower(): Passing null to parameter #1 ($string) of type string is deprecated in APPPATH\Controllers\Api.php on line 1537.
 1 APPPATH\Controllers\Api.php(1537): strtolower(null)
 2 APPPATH\Controllers\Api.php(189): App\Controllers\Api->getSubscription()
 3 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('get-subscription')
 4 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 5 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 6 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 7 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-06-24 01:36:43 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-24 01:36:43 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
WARNING - 2025-06-24 01:36:43 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('aiproStart', null, 1753321003, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(401): btflag_set('aiproStart', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-24 01:36:43 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('user_email', null, 1753321003, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(402): btflag_set('user_email', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-24 01:36:43 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('chatpro_KJLF4XgSL8wjlGm', null, 1753321003, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(403): btflag_set('chatpro_KJLF4XgSL8wjlGm', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-06-24 01:36:43 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-24 01:36:43 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 01:36:54 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-24 01:36:54 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
WARNING - 2025-06-24 01:36:54 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('aiproStart', null, 1753321014, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(401): btflag_set('aiproStart', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-24 01:36:54 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('user_email', null, 1753321014, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(402): btflag_set('user_email', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-24 01:36:54 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('chatpro_KJLF4XgSL8wjlGm', null, 1753321014, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(403): btflag_set('chatpro_KJLF4XgSL8wjlGm', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-06-24 01:36:54 --> REQ ----------->
INFO - 2025-06-24 01:36:54 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 01:37:51 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-24 01:37:51 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
WARNING - 2025-06-24 01:37:51 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('aiproStart', null, 1753321071, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(401): btflag_set('aiproStart', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-24 01:37:51 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('user_email', null, 1753321071, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(402): btflag_set('user_email', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-24 01:37:51 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('chatpro_KJLF4XgSL8wjlGm', null, 1753321071, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(403): btflag_set('chatpro_KJLF4XgSL8wjlGm', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-06-24 01:37:51 --> REQ ----------->
INFO - 2025-06-24 01:37:51 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 01:41:22 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-24 01:41:22 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
WARNING - 2025-06-24 01:41:22 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('aiproStart', null, 1753321282, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(401): btflag_set('aiproStart', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-24 01:41:22 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('user_email', null, 1753321282, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(402): btflag_set('user_email', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-24 01:41:22 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('chatpro_KJLF4XgSL8wjlGm', null, 1753321282, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(403): btflag_set('chatpro_KJLF4XgSL8wjlGm', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-06-24 01:41:22 --> REQ ----------->
INFO - 2025-06-24 01:41:22 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 01:41:24 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-24 01:41:24 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
WARNING - 2025-06-24 01:41:24 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('aiproStart', null, 1753321284, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(401): btflag_set('aiproStart', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-24 01:41:24 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('user_email', null, 1753321284, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(402): btflag_set('user_email', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-24 01:41:24 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('chatpro_KJLF4XgSL8wjlGm', null, 1753321284, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(403): btflag_set('chatpro_KJLF4XgSL8wjlGm', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
INFO - 2025-06-24 01:41:25 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 01:41:25 --> btdbFindBy ---> 
DEBUG - 2025-06-24 01:41:25 --> btdbFindBy ---> SELECT `start-plan`.*, `start-account`.*, DATE_FORMAT(resumed_at , "%M %e, %Y") as "resumed_at_formatted", `start-user`.`mode`, case when DATE_ADD(end_date, INTERVAL 1 DAY) < now() then 'yes' else 'no' end as 'expired'
FROM `start-account`
JOIN `start-user` ON `start-user`.`user_id` = `start-account`.`user_id`
JOIN `start-plan` ON `start-plan`.`plan_id` = `start-account`.`plan_id`
WHERE `start-account`.`user_id` = '2'
ORDER BY `start-account`.`end_date` DESC
WARNING - 2025-06-24 01:41:25 --> [DEPRECATED] strtolower(): Passing null to parameter #1 ($string) of type string is deprecated in APPPATH\Controllers\User\Settings.php on line 163.
 1 APPPATH\Controllers\User\Settings.php(163): strtolower(null)
 2 APPPATH\Controllers\User\Settings.php(59): App\Controllers\User\Settings->get_subs_data()
 3 APPPATH\Controllers\User\Settings.php(45): App\Controllers\User\Settings->getDiscountPricing()
 4 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\User\Settings->index()
 5 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\User\Settings))
 6 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 7 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 8 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-24 01:41:25 --> [DEPRECATED] strtolower(): Passing null to parameter #1 ($string) of type string is deprecated in APPPATH\Controllers\User\Settings.php on line 164.
 1 APPPATH\Controllers\User\Settings.php(164): strtolower(null)
 2 APPPATH\Controllers\User\Settings.php(59): App\Controllers\User\Settings->get_subs_data()
 3 APPPATH\Controllers\User\Settings.php(45): App\Controllers\User\Settings->getDiscountPricing()
 4 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\User\Settings->index()
 5 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\User\Settings))
 6 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 7 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 8 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-24 01:41:25 --> [DEPRECATED] strtolower(): Passing null to parameter #1 ($string) of type string is deprecated in APPPATH\Controllers\User\Settings.php on line 87.
 1 APPPATH\Controllers\User\Settings.php(87): strtolower(null)
 2 APPPATH\Controllers\User\Settings.php(45): App\Controllers\User\Settings->getDiscountPricing()
 3 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\User\Settings->index()
 4 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\User\Settings))
 5 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 6 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 7 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-06-24 01:41:25 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-24 01:41:25 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
WARNING - 2025-06-24 01:41:25 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('aiproStart', null, 1753321285, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(401): btflag_set('aiproStart', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-24 01:41:25 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('user_email', null, 1753321285, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(402): btflag_set('user_email', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-24 01:41:25 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('chatpro_KJLF4XgSL8wjlGm', null, 1753321285, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(403): btflag_set('chatpro_KJLF4XgSL8wjlGm', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-06-24 01:41:25 --> REQ ----------->
INFO - 2025-06-24 01:41:25 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 01:41:28 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
DEBUG - 2025-06-24 01:41:28 --> btdbFindBy ---> 
INFO - 2025-06-24 01:41:28 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 01:41:28 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
DEBUG - 2025-06-24 01:41:28 --> btdbFindBy ---> 
INFO - 2025-06-24 01:41:28 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
WARNING - 2025-06-24 01:41:28 --> [DEPRECATED] strtolower(): Passing null to parameter #1 ($string) of type string is deprecated in APPPATH\Controllers\Api.php on line 1534.
 1 APPPATH\Controllers\Api.php(1534): strtolower(null)
 2 APPPATH\Controllers\Api.php(189): App\Controllers\Api->getSubscription()
 3 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('get-subscription')
 4 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 5 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 6 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 7 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-24 01:41:28 --> [DEPRECATED] strtolower(): Passing null to parameter #1 ($string) of type string is deprecated in APPPATH\Controllers\Api.php on line 1535.
 1 APPPATH\Controllers\Api.php(1535): strtolower(null)
 2 APPPATH\Controllers\Api.php(189): App\Controllers\Api->getSubscription()
 3 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('get-subscription')
 4 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 5 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 6 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 7 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-06-24 01:41:28 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-24 01:41:28 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
WARNING - 2025-06-24 01:41:28 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('aiproStart', null, 1753321288, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(401): btflag_set('aiproStart', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-24 01:41:28 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('user_email', null, 1753321288, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(402): btflag_set('user_email', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-24 01:41:28 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('chatpro_KJLF4XgSL8wjlGm', null, 1753321288, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(403): btflag_set('chatpro_KJLF4XgSL8wjlGm', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-06-24 01:41:28 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-24 01:41:28 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 01:41:43 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-24 01:41:43 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
WARNING - 2025-06-24 01:41:43 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('aiproStart', null, 1753321303, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(401): btflag_set('aiproStart', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-24 01:41:43 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('user_email', null, 1753321303, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(402): btflag_set('user_email', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-24 01:41:43 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('chatpro_KJLF4XgSL8wjlGm', null, 1753321303, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(403): btflag_set('chatpro_KJLF4XgSL8wjlGm', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-06-24 01:41:43 --> REQ ----------->
INFO - 2025-06-24 01:41:43 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 01:41:50 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-24 01:41:50 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
WARNING - 2025-06-24 01:41:50 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('aiproStart', null, 1753321310, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(401): btflag_set('aiproStart', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-24 01:41:50 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('user_email', null, 1753321310, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(402): btflag_set('user_email', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-24 01:41:50 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('chatpro_KJLF4XgSL8wjlGm', null, 1753321310, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(403): btflag_set('chatpro_KJLF4XgSL8wjlGm', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-06-24 01:41:50 --> REQ ----------->
INFO - 2025-06-24 01:41:50 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2025-06-24 01:41:52 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 01:41:52 --> btdbFindBy ---> 
DEBUG - 2025-06-24 01:41:52 --> btdbFindBy ---> SELECT `start-plan`.*, `start-account`.*, DATE_FORMAT(resumed_at , "%M %e, %Y") as "resumed_at_formatted", `start-user`.`mode`, case when DATE_ADD(end_date, INTERVAL 1 DAY) < now() then 'yes' else 'no' end as 'expired'
FROM `start-account`
JOIN `start-user` ON `start-user`.`user_id` = `start-account`.`user_id`
JOIN `start-plan` ON `start-plan`.`plan_id` = `start-account`.`plan_id`
WHERE `start-account`.`user_id` = '2'
ORDER BY `start-account`.`end_date` DESC
WARNING - 2025-06-24 01:41:52 --> [DEPRECATED] strtolower(): Passing null to parameter #1 ($string) of type string is deprecated in APPPATH\Controllers\User\Settings.php on line 163.
 1 APPPATH\Controllers\User\Settings.php(163): strtolower(null)
 2 APPPATH\Controllers\User\Settings.php(59): App\Controllers\User\Settings->get_subs_data()
 3 APPPATH\Controllers\User\Settings.php(45): App\Controllers\User\Settings->getDiscountPricing()
 4 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\User\Settings->index()
 5 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\User\Settings))
 6 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 7 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 8 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-24 01:41:52 --> [DEPRECATED] strtolower(): Passing null to parameter #1 ($string) of type string is deprecated in APPPATH\Controllers\User\Settings.php on line 164.
 1 APPPATH\Controllers\User\Settings.php(164): strtolower(null)
 2 APPPATH\Controllers\User\Settings.php(59): App\Controllers\User\Settings->get_subs_data()
 3 APPPATH\Controllers\User\Settings.php(45): App\Controllers\User\Settings->getDiscountPricing()
 4 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\User\Settings->index()
 5 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\User\Settings))
 6 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 7 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 8 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-24 01:41:52 --> [DEPRECATED] strtolower(): Passing null to parameter #1 ($string) of type string is deprecated in APPPATH\Controllers\User\Settings.php on line 87.
 1 APPPATH\Controllers\User\Settings.php(87): strtolower(null)
 2 APPPATH\Controllers\User\Settings.php(45): App\Controllers\User\Settings->getDiscountPricing()
 3 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\User\Settings->index()
 4 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\User\Settings))
 5 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 6 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 7 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-06-24 01:41:53 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-24 01:41:53 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
WARNING - 2025-06-24 01:41:53 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('aiproStart', null, 1753321313, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(401): btflag_set('aiproStart', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-24 01:41:53 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('user_email', null, 1753321313, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(402): btflag_set('user_email', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-24 01:41:53 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('chatpro_KJLF4XgSL8wjlGm', null, 1753321313, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(403): btflag_set('chatpro_KJLF4XgSL8wjlGm', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-06-24 01:41:53 --> REQ ----------->
INFO - 2025-06-24 01:41:53 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 01:41:55 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
DEBUG - 2025-06-24 01:41:55 --> btdbFindBy ---> 
INFO - 2025-06-24 01:41:55 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 01:41:56 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
DEBUG - 2025-06-24 01:41:56 --> btdbFindBy ---> 
INFO - 2025-06-24 01:41:56 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
WARNING - 2025-06-24 01:41:56 --> [DEPRECATED] strtolower(): Passing null to parameter #1 ($string) of type string is deprecated in APPPATH\Controllers\Api.php on line 1534.
 1 APPPATH\Controllers\Api.php(1534): strtolower(null)
 2 APPPATH\Controllers\Api.php(189): App\Controllers\Api->getSubscription()
 3 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('get-subscription')
 4 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 5 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 6 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 7 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-24 01:41:56 --> [DEPRECATED] strtolower(): Passing null to parameter #1 ($string) of type string is deprecated in APPPATH\Controllers\Api.php on line 1535.
 1 APPPATH\Controllers\Api.php(1535): strtolower(null)
 2 APPPATH\Controllers\Api.php(189): App\Controllers\Api->getSubscription()
 3 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('get-subscription')
 4 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 5 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 6 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 7 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-06-24 01:41:56 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-24 01:41:56 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
WARNING - 2025-06-24 01:41:56 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('aiproStart', null, 1753321316, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(401): btflag_set('aiproStart', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-24 01:41:56 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('user_email', null, 1753321316, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(402): btflag_set('user_email', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-24 01:41:56 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('chatpro_KJLF4XgSL8wjlGm', null, 1753321316, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(403): btflag_set('chatpro_KJLF4XgSL8wjlGm', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-06-24 01:41:56 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-24 01:41:56 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 01:42:36 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-24 01:42:36 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
WARNING - 2025-06-24 01:42:36 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('aiproStart', null, 1753321356, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(401): btflag_set('aiproStart', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-24 01:42:36 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('user_email', null, 1753321356, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(402): btflag_set('user_email', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-24 01:42:36 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('chatpro_KJLF4XgSL8wjlGm', null, 1753321356, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(403): btflag_set('chatpro_KJLF4XgSL8wjlGm', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-06-24 01:42:36 --> REQ ----------->
INFO - 2025-06-24 01:42:36 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 01:43:23 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-24 01:43:23 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
WARNING - 2025-06-24 01:43:23 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('aiproStart', null, 1753321403, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(401): btflag_set('aiproStart', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-24 01:43:23 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('user_email', null, 1753321403, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(402): btflag_set('user_email', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-24 01:43:23 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('chatpro_KJLF4XgSL8wjlGm', null, 1753321403, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(403): btflag_set('chatpro_KJLF4XgSL8wjlGm', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-06-24 01:43:24 --> REQ ----------->
INFO - 2025-06-24 01:43:24 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2025-06-24 01:43:27 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 01:43:27 --> btdbFindBy ---> 
DEBUG - 2025-06-24 01:43:27 --> btdbFindBy ---> SELECT `start-plan`.*, `start-account`.*, DATE_FORMAT(resumed_at , "%M %e, %Y") as "resumed_at_formatted", `start-user`.`mode`, case when DATE_ADD(end_date, INTERVAL 1 DAY) < now() then 'yes' else 'no' end as 'expired'
FROM `start-account`
JOIN `start-user` ON `start-user`.`user_id` = `start-account`.`user_id`
JOIN `start-plan` ON `start-plan`.`plan_id` = `start-account`.`plan_id`
WHERE `start-account`.`user_id` = '2'
ORDER BY `start-account`.`end_date` DESC
WARNING - 2025-06-24 01:43:27 --> [DEPRECATED] strtolower(): Passing null to parameter #1 ($string) of type string is deprecated in APPPATH\Controllers\User\Settings.php on line 163.
 1 APPPATH\Controllers\User\Settings.php(163): strtolower(null)
 2 APPPATH\Controllers\User\Settings.php(59): App\Controllers\User\Settings->get_subs_data()
 3 APPPATH\Controllers\User\Settings.php(45): App\Controllers\User\Settings->getDiscountPricing()
 4 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\User\Settings->index()
 5 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\User\Settings))
 6 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 7 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 8 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-24 01:43:27 --> [DEPRECATED] strtolower(): Passing null to parameter #1 ($string) of type string is deprecated in APPPATH\Controllers\User\Settings.php on line 164.
 1 APPPATH\Controllers\User\Settings.php(164): strtolower(null)
 2 APPPATH\Controllers\User\Settings.php(59): App\Controllers\User\Settings->get_subs_data()
 3 APPPATH\Controllers\User\Settings.php(45): App\Controllers\User\Settings->getDiscountPricing()
 4 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\User\Settings->index()
 5 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\User\Settings))
 6 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 7 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 8 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-24 01:43:27 --> [DEPRECATED] strtolower(): Passing null to parameter #1 ($string) of type string is deprecated in APPPATH\Controllers\User\Settings.php on line 87.
 1 APPPATH\Controllers\User\Settings.php(87): strtolower(null)
 2 APPPATH\Controllers\User\Settings.php(45): App\Controllers\User\Settings->getDiscountPricing()
 3 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\User\Settings->index()
 4 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\User\Settings))
 5 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 6 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 7 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-06-24 01:43:28 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-24 01:43:28 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
WARNING - 2025-06-24 01:43:28 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('aiproStart', null, 1753321408, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(401): btflag_set('aiproStart', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-24 01:43:28 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('user_email', null, 1753321408, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(402): btflag_set('user_email', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-24 01:43:28 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('chatpro_KJLF4XgSL8wjlGm', null, 1753321408, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(403): btflag_set('chatpro_KJLF4XgSL8wjlGm', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-06-24 01:43:28 --> REQ ----------->
INFO - 2025-06-24 01:43:28 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 01:43:30 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
DEBUG - 2025-06-24 01:43:30 --> btdbFindBy ---> 
INFO - 2025-06-24 01:43:30 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 01:43:30 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
DEBUG - 2025-06-24 01:43:30 --> btdbFindBy ---> 
INFO - 2025-06-24 01:43:30 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
WARNING - 2025-06-24 01:43:30 --> [DEPRECATED] strtolower(): Passing null to parameter #1 ($string) of type string is deprecated in APPPATH\Controllers\Api.php on line 1534.
 1 APPPATH\Controllers\Api.php(1534): strtolower(null)
 2 APPPATH\Controllers\Api.php(189): App\Controllers\Api->getSubscription()
 3 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('get-subscription')
 4 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 5 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 6 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 7 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-24 01:43:30 --> [DEPRECATED] strtolower(): Passing null to parameter #1 ($string) of type string is deprecated in APPPATH\Controllers\Api.php on line 1535.
 1 APPPATH\Controllers\Api.php(1535): strtolower(null)
 2 APPPATH\Controllers\Api.php(189): App\Controllers\Api->getSubscription()
 3 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('get-subscription')
 4 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 5 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 6 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 7 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-06-24 01:43:31 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-24 01:43:31 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
WARNING - 2025-06-24 01:43:31 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('aiproStart', null, 1753321411, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(401): btflag_set('aiproStart', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-24 01:43:31 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('user_email', null, 1753321411, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(402): btflag_set('user_email', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-24 01:43:31 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('chatpro_KJLF4XgSL8wjlGm', null, 1753321411, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(403): btflag_set('chatpro_KJLF4XgSL8wjlGm', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-06-24 01:43:31 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-24 01:43:31 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 01:43:38 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-24 01:43:38 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
WARNING - 2025-06-24 01:43:38 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('aiproStart', null, 1753321418, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(401): btflag_set('aiproStart', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-24 01:43:38 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('user_email', null, 1753321418, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(402): btflag_set('user_email', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-24 01:43:38 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('chatpro_KJLF4XgSL8wjlGm', null, 1753321418, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(403): btflag_set('chatpro_KJLF4XgSL8wjlGm', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-06-24 01:43:38 --> REQ ----------->
INFO - 2025-06-24 01:43:38 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 01:44:51 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-24 01:44:51 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
WARNING - 2025-06-24 01:44:51 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('aiproStart', null, 1753321491, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(401): btflag_set('aiproStart', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-24 01:44:51 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('user_email', null, 1753321491, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(402): btflag_set('user_email', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-24 01:44:51 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('chatpro_KJLF4XgSL8wjlGm', null, 1753321491, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(403): btflag_set('chatpro_KJLF4XgSL8wjlGm', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-06-24 01:44:51 --> REQ ----------->
INFO - 2025-06-24 01:44:51 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2025-06-24 01:44:53 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 01:44:53 --> btdbFindBy ---> 
DEBUG - 2025-06-24 01:44:53 --> btdbFindBy ---> SELECT `start-plan`.*, `start-account`.*, DATE_FORMAT(resumed_at , "%M %e, %Y") as "resumed_at_formatted", `start-user`.`mode`, case when DATE_ADD(end_date, INTERVAL 1 DAY) < now() then 'yes' else 'no' end as 'expired'
FROM `start-account`
JOIN `start-user` ON `start-user`.`user_id` = `start-account`.`user_id`
JOIN `start-plan` ON `start-plan`.`plan_id` = `start-account`.`plan_id`
WHERE `start-account`.`user_id` = '2'
ORDER BY `start-account`.`end_date` DESC
WARNING - 2025-06-24 01:44:53 --> [DEPRECATED] strtolower(): Passing null to parameter #1 ($string) of type string is deprecated in APPPATH\Controllers\User\Settings.php on line 163.
 1 APPPATH\Controllers\User\Settings.php(163): strtolower(null)
 2 APPPATH\Controllers\User\Settings.php(59): App\Controllers\User\Settings->get_subs_data()
 3 APPPATH\Controllers\User\Settings.php(45): App\Controllers\User\Settings->getDiscountPricing()
 4 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\User\Settings->index()
 5 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\User\Settings))
 6 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 7 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 8 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-24 01:44:53 --> [DEPRECATED] strtolower(): Passing null to parameter #1 ($string) of type string is deprecated in APPPATH\Controllers\User\Settings.php on line 164.
 1 APPPATH\Controllers\User\Settings.php(164): strtolower(null)
 2 APPPATH\Controllers\User\Settings.php(59): App\Controllers\User\Settings->get_subs_data()
 3 APPPATH\Controllers\User\Settings.php(45): App\Controllers\User\Settings->getDiscountPricing()
 4 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\User\Settings->index()
 5 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\User\Settings))
 6 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 7 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 8 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-24 01:44:53 --> [DEPRECATED] strtolower(): Passing null to parameter #1 ($string) of type string is deprecated in APPPATH\Controllers\User\Settings.php on line 87.
 1 APPPATH\Controllers\User\Settings.php(87): strtolower(null)
 2 APPPATH\Controllers\User\Settings.php(45): App\Controllers\User\Settings->getDiscountPricing()
 3 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\User\Settings->index()
 4 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\User\Settings))
 5 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 6 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 7 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-06-24 01:44:54 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-24 01:44:54 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
WARNING - 2025-06-24 01:44:54 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('aiproStart', null, 1753321494, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(401): btflag_set('aiproStart', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-24 01:44:54 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('user_email', null, 1753321494, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(402): btflag_set('user_email', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-24 01:44:54 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('chatpro_KJLF4XgSL8wjlGm', null, 1753321494, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(403): btflag_set('chatpro_KJLF4XgSL8wjlGm', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-06-24 01:44:54 --> REQ ----------->
INFO - 2025-06-24 01:44:54 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 01:44:57 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
DEBUG - 2025-06-24 01:44:57 --> btdbFindBy ---> 
INFO - 2025-06-24 01:44:57 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 01:44:57 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
DEBUG - 2025-06-24 01:44:57 --> btdbFindBy ---> 
INFO - 2025-06-24 01:44:57 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
WARNING - 2025-06-24 01:44:57 --> [DEPRECATED] strtolower(): Passing null to parameter #1 ($string) of type string is deprecated in APPPATH\Controllers\Api.php on line 1534.
 1 APPPATH\Controllers\Api.php(1534): strtolower(null)
 2 APPPATH\Controllers\Api.php(189): App\Controllers\Api->getSubscription()
 3 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('get-subscription')
 4 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 5 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 6 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 7 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-24 01:44:57 --> [DEPRECATED] strtolower(): Passing null to parameter #1 ($string) of type string is deprecated in APPPATH\Controllers\Api.php on line 1535.
 1 APPPATH\Controllers\Api.php(1535): strtolower(null)
 2 APPPATH\Controllers\Api.php(189): App\Controllers\Api->getSubscription()
 3 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('get-subscription')
 4 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 5 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 6 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 7 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-06-24 01:44:57 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-24 01:44:57 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
WARNING - 2025-06-24 01:44:57 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('aiproStart', null, 1753321497, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(401): btflag_set('aiproStart', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-24 01:44:57 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('user_email', null, 1753321497, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(402): btflag_set('user_email', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-24 01:44:57 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('chatpro_KJLF4XgSL8wjlGm', null, 1753321497, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(403): btflag_set('chatpro_KJLF4XgSL8wjlGm', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-06-24 01:44:57 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-24 01:44:57 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 01:45:00 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-24 01:45:00 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
WARNING - 2025-06-24 01:45:00 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('aiproStart', null, 1753321500, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(401): btflag_set('aiproStart', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-24 01:45:00 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('user_email', null, 1753321500, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(402): btflag_set('user_email', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-24 01:45:00 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('chatpro_KJLF4XgSL8wjlGm', null, 1753321500, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(403): btflag_set('chatpro_KJLF4XgSL8wjlGm', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-06-24 01:45:00 --> REQ ----------->
INFO - 2025-06-24 01:45:00 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 01:47:05 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-24 01:47:05 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
WARNING - 2025-06-24 01:47:05 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('aiproStart', null, 1753321625, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(401): btflag_set('aiproStart', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-24 01:47:05 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('user_email', null, 1753321625, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(402): btflag_set('user_email', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-24 01:47:05 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('chatpro_KJLF4XgSL8wjlGm', null, 1753321625, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(403): btflag_set('chatpro_KJLF4XgSL8wjlGm', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-06-24 01:47:06 --> REQ ----------->
INFO - 2025-06-24 01:47:06 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 01:47:10 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-24 01:47:10 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
WARNING - 2025-06-24 01:47:10 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('aiproStart', null, 1753321630, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(401): btflag_set('aiproStart', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-24 01:47:10 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('user_email', null, 1753321630, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(402): btflag_set('user_email', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-24 01:47:10 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('chatpro_KJLF4XgSL8wjlGm', null, 1753321630, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(403): btflag_set('chatpro_KJLF4XgSL8wjlGm', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-06-24 01:47:10 --> REQ ----------->
INFO - 2025-06-24 01:47:10 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 01:47:13 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-24 01:47:13 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
WARNING - 2025-06-24 01:47:13 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('aiproStart', null, 1753321633, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(401): btflag_set('aiproStart', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-24 01:47:13 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('user_email', null, 1753321633, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(402): btflag_set('user_email', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-24 01:47:13 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('chatpro_KJLF4XgSL8wjlGm', null, 1753321633, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(403): btflag_set('chatpro_KJLF4XgSL8wjlGm', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-06-24 01:47:13 --> REQ ----------->
INFO - 2025-06-24 01:47:13 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2025-06-24 01:47:16 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 01:47:16 --> btdbFindBy ---> 
DEBUG - 2025-06-24 01:47:16 --> btdbFindBy ---> SELECT `start-plan`.*, `start-account`.*, DATE_FORMAT(resumed_at , "%M %e, %Y") as "resumed_at_formatted", `start-user`.`mode`, case when DATE_ADD(end_date, INTERVAL 1 DAY) < now() then 'yes' else 'no' end as 'expired'
FROM `start-account`
JOIN `start-user` ON `start-user`.`user_id` = `start-account`.`user_id`
JOIN `start-plan` ON `start-plan`.`plan_id` = `start-account`.`plan_id`
WHERE `start-account`.`user_id` = '2'
ORDER BY `start-account`.`end_date` DESC
WARNING - 2025-06-24 01:47:16 --> [DEPRECATED] strtolower(): Passing null to parameter #1 ($string) of type string is deprecated in APPPATH\Controllers\User\Settings.php on line 163.
 1 APPPATH\Controllers\User\Settings.php(163): strtolower(null)
 2 APPPATH\Controllers\User\Settings.php(59): App\Controllers\User\Settings->get_subs_data()
 3 APPPATH\Controllers\User\Settings.php(45): App\Controllers\User\Settings->getDiscountPricing()
 4 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\User\Settings->index()
 5 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\User\Settings))
 6 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 7 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 8 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-24 01:47:16 --> [DEPRECATED] strtolower(): Passing null to parameter #1 ($string) of type string is deprecated in APPPATH\Controllers\User\Settings.php on line 164.
 1 APPPATH\Controllers\User\Settings.php(164): strtolower(null)
 2 APPPATH\Controllers\User\Settings.php(59): App\Controllers\User\Settings->get_subs_data()
 3 APPPATH\Controllers\User\Settings.php(45): App\Controllers\User\Settings->getDiscountPricing()
 4 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\User\Settings->index()
 5 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\User\Settings))
 6 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 7 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 8 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-24 01:47:16 --> [DEPRECATED] strtolower(): Passing null to parameter #1 ($string) of type string is deprecated in APPPATH\Controllers\User\Settings.php on line 87.
 1 APPPATH\Controllers\User\Settings.php(87): strtolower(null)
 2 APPPATH\Controllers\User\Settings.php(45): App\Controllers\User\Settings->getDiscountPricing()
 3 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\User\Settings->index()
 4 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\User\Settings))
 5 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 6 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 7 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-06-24 01:47:17 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-24 01:47:17 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
WARNING - 2025-06-24 01:47:17 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('aiproStart', null, 1753321637, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(401): btflag_set('aiproStart', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-24 01:47:17 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('user_email', null, 1753321637, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(402): btflag_set('user_email', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-24 01:47:17 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('chatpro_KJLF4XgSL8wjlGm', null, 1753321637, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(403): btflag_set('chatpro_KJLF4XgSL8wjlGm', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-06-24 01:47:17 --> REQ ----------->
INFO - 2025-06-24 01:47:17 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 01:47:23 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
DEBUG - 2025-06-24 01:47:23 --> btdbFindBy ---> 
INFO - 2025-06-24 01:47:23 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 01:47:23 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
DEBUG - 2025-06-24 01:47:23 --> btdbFindBy ---> 
INFO - 2025-06-24 01:47:23 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
WARNING - 2025-06-24 01:47:23 --> [DEPRECATED] strtolower(): Passing null to parameter #1 ($string) of type string is deprecated in APPPATH\Controllers\Api.php on line 1534.
 1 APPPATH\Controllers\Api.php(1534): strtolower(null)
 2 APPPATH\Controllers\Api.php(189): App\Controllers\Api->getSubscription()
 3 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('get-subscription')
 4 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 5 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 6 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 7 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-24 01:47:23 --> [DEPRECATED] strtolower(): Passing null to parameter #1 ($string) of type string is deprecated in APPPATH\Controllers\Api.php on line 1535.
 1 APPPATH\Controllers\Api.php(1535): strtolower(null)
 2 APPPATH\Controllers\Api.php(189): App\Controllers\Api->getSubscription()
 3 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('get-subscription')
 4 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 5 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 6 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 7 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-06-24 01:47:23 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-24 01:47:23 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
WARNING - 2025-06-24 01:47:23 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('aiproStart', null, 1753321643, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(401): btflag_set('aiproStart', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-24 01:47:23 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('user_email', null, 1753321643, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(402): btflag_set('user_email', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-24 01:47:23 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('chatpro_KJLF4XgSL8wjlGm', null, 1753321643, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(403): btflag_set('chatpro_KJLF4XgSL8wjlGm', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-06-24 01:47:24 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-24 01:47:24 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 01:48:10 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-24 01:48:10 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
WARNING - 2025-06-24 01:48:10 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('aiproStart', null, 1753321690, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(401): btflag_set('aiproStart', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-24 01:48:10 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('user_email', null, 1753321690, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(402): btflag_set('user_email', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-24 01:48:10 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('chatpro_KJLF4XgSL8wjlGm', null, 1753321690, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(403): btflag_set('chatpro_KJLF4XgSL8wjlGm', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-06-24 01:48:10 --> REQ ----------->
INFO - 2025-06-24 01:48:10 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 01:48:36 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-24 01:48:36 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
WARNING - 2025-06-24 01:48:36 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('aiproStart', null, 1753321716, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(401): btflag_set('aiproStart', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-24 01:48:36 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('user_email', null, 1753321716, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(402): btflag_set('user_email', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-24 01:48:36 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('chatpro_KJLF4XgSL8wjlGm', null, 1753321716, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(403): btflag_set('chatpro_KJLF4XgSL8wjlGm', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-06-24 01:48:36 --> REQ ----------->
INFO - 2025-06-24 01:48:36 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2025-06-24 01:48:38 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 01:48:38 --> btdbFindBy ---> 
DEBUG - 2025-06-24 01:48:38 --> btdbFindBy ---> SELECT `start-plan`.*, `start-account`.*, DATE_FORMAT(resumed_at , "%M %e, %Y") as "resumed_at_formatted", `start-user`.`mode`, case when DATE_ADD(end_date, INTERVAL 1 DAY) < now() then 'yes' else 'no' end as 'expired'
FROM `start-account`
JOIN `start-user` ON `start-user`.`user_id` = `start-account`.`user_id`
JOIN `start-plan` ON `start-plan`.`plan_id` = `start-account`.`plan_id`
WHERE `start-account`.`user_id` = '2'
ORDER BY `start-account`.`end_date` DESC
WARNING - 2025-06-24 01:48:38 --> [DEPRECATED] strtolower(): Passing null to parameter #1 ($string) of type string is deprecated in APPPATH\Controllers\User\Settings.php on line 163.
 1 APPPATH\Controllers\User\Settings.php(163): strtolower(null)
 2 APPPATH\Controllers\User\Settings.php(59): App\Controllers\User\Settings->get_subs_data()
 3 APPPATH\Controllers\User\Settings.php(45): App\Controllers\User\Settings->getDiscountPricing()
 4 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\User\Settings->index()
 5 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\User\Settings))
 6 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 7 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 8 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-24 01:48:38 --> [DEPRECATED] strtolower(): Passing null to parameter #1 ($string) of type string is deprecated in APPPATH\Controllers\User\Settings.php on line 164.
 1 APPPATH\Controllers\User\Settings.php(164): strtolower(null)
 2 APPPATH\Controllers\User\Settings.php(59): App\Controllers\User\Settings->get_subs_data()
 3 APPPATH\Controllers\User\Settings.php(45): App\Controllers\User\Settings->getDiscountPricing()
 4 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\User\Settings->index()
 5 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\User\Settings))
 6 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 7 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 8 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-24 01:48:38 --> [DEPRECATED] strtolower(): Passing null to parameter #1 ($string) of type string is deprecated in APPPATH\Controllers\User\Settings.php on line 87.
 1 APPPATH\Controllers\User\Settings.php(87): strtolower(null)
 2 APPPATH\Controllers\User\Settings.php(45): App\Controllers\User\Settings->getDiscountPricing()
 3 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\User\Settings->index()
 4 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\User\Settings))
 5 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 6 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 7 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-06-24 01:48:40 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-24 01:48:40 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
WARNING - 2025-06-24 01:48:40 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('aiproStart', null, 1753321720, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(401): btflag_set('aiproStart', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-24 01:48:40 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('user_email', null, 1753321720, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(402): btflag_set('user_email', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-24 01:48:40 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('chatpro_KJLF4XgSL8wjlGm', null, 1753321720, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(403): btflag_set('chatpro_KJLF4XgSL8wjlGm', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-06-24 01:48:40 --> REQ ----------->
INFO - 2025-06-24 01:48:40 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 01:48:44 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
DEBUG - 2025-06-24 01:48:44 --> btdbFindBy ---> 
INFO - 2025-06-24 01:48:44 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2025-06-24 01:48:44 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 01:48:44 --> btdbFindBy ---> 
DEBUG - 2025-06-24 01:48:44 --> btdbFindBy ---> SELECT `start-plan`.*, `start-account`.*, DATE_FORMAT(resumed_at , "%M %e, %Y") as "resumed_at_formatted", `start-user`.`mode`, case when DATE_ADD(end_date, INTERVAL 1 DAY) < now() then 'yes' else 'no' end as 'expired'
FROM `start-account`
JOIN `start-user` ON `start-user`.`user_id` = `start-account`.`user_id`
JOIN `start-plan` ON `start-plan`.`plan_id` = `start-account`.`plan_id`
WHERE `start-account`.`user_id` = '2'
ORDER BY `start-account`.`end_date` DESC
WARNING - 2025-06-24 01:48:44 --> [DEPRECATED] strtolower(): Passing null to parameter #1 ($string) of type string is deprecated in APPPATH\Controllers\User\Settings.php on line 163.
 1 APPPATH\Controllers\User\Settings.php(163): strtolower(null)
 2 APPPATH\Controllers\User\Settings.php(59): App\Controllers\User\Settings->get_subs_data()
 3 APPPATH\Controllers\User\Settings.php(45): App\Controllers\User\Settings->getDiscountPricing()
 4 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\User\Settings->index()
 5 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\User\Settings))
 6 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 7 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 8 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-24 01:48:44 --> [DEPRECATED] strtolower(): Passing null to parameter #1 ($string) of type string is deprecated in APPPATH\Controllers\User\Settings.php on line 164.
 1 APPPATH\Controllers\User\Settings.php(164): strtolower(null)
 2 APPPATH\Controllers\User\Settings.php(59): App\Controllers\User\Settings->get_subs_data()
 3 APPPATH\Controllers\User\Settings.php(45): App\Controllers\User\Settings->getDiscountPricing()
 4 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\User\Settings->index()
 5 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\User\Settings))
 6 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 7 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 8 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-24 01:48:44 --> [DEPRECATED] strtolower(): Passing null to parameter #1 ($string) of type string is deprecated in APPPATH\Controllers\User\Settings.php on line 87.
 1 APPPATH\Controllers\User\Settings.php(87): strtolower(null)
 2 APPPATH\Controllers\User\Settings.php(45): App\Controllers\User\Settings->getDiscountPricing()
 3 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\User\Settings->index()
 4 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\User\Settings))
 5 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 6 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 7 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-06-24 01:48:44 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
DEBUG - 2025-06-24 01:48:44 --> btdbFindBy ---> 
INFO - 2025-06-24 01:48:44 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
WARNING - 2025-06-24 01:48:44 --> [DEPRECATED] strtolower(): Passing null to parameter #1 ($string) of type string is deprecated in APPPATH\Controllers\Api.php on line 1536.
 1 APPPATH\Controllers\Api.php(1536): strtolower(null)
 2 APPPATH\Controllers\Api.php(189): App\Controllers\Api->getSubscription()
 3 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('get-subscription')
 4 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 5 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 6 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 7 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-24 01:48:44 --> [DEPRECATED] strtolower(): Passing null to parameter #1 ($string) of type string is deprecated in APPPATH\Controllers\Api.php on line 1537.
 1 APPPATH\Controllers\Api.php(1537): strtolower(null)
 2 APPPATH\Controllers\Api.php(189): App\Controllers\Api->getSubscription()
 3 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('get-subscription')
 4 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 5 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 6 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 7 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-06-24 01:48:44 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-24 01:48:44 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
WARNING - 2025-06-24 01:48:44 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('aiproStart', null, 1753321724, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(401): btflag_set('aiproStart', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-24 01:48:44 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('user_email', null, 1753321724, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(402): btflag_set('user_email', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-24 01:48:44 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('chatpro_KJLF4XgSL8wjlGm', null, 1753321724, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(403): btflag_set('chatpro_KJLF4XgSL8wjlGm', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-06-24 01:48:44 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-24 01:48:45 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
CRITICAL - 2025-06-24 01:48:50 --> A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond [tcp://************:6379]
in VENDORPATH\predis\predis\src\Connection\AbstractConnection.php on line 155.
 1 VENDORPATH\predis\predis\src\Connection\StreamConnection.php(128): Predis\Connection\AbstractConnection->onConnectionError('A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond', 10060)
 2 VENDORPATH\predis\predis\src\Connection\StreamConnection.php(178): Predis\Connection\StreamConnection->createStreamSocket(Object(Predis\Connection\Parameters), 'tcp://************:6379', 4)
 3 VENDORPATH\predis\predis\src\Connection\StreamConnection.php(100): Predis\Connection\StreamConnection->tcpStreamInitializer(Object(Predis\Connection\Parameters))
 4 VENDORPATH\predis\predis\src\Connection\AbstractConnection.php(81): Predis\Connection\StreamConnection->createResource()
 5 VENDORPATH\predis\predis\src\Connection\StreamConnection.php(258): Predis\Connection\AbstractConnection->connect()
 6 VENDORPATH\predis\predis\src\Connection\AbstractConnection.php(180): Predis\Connection\StreamConnection->connect()
 7 VENDORPATH\predis\predis\src\Connection\StreamConnection.php(288): Predis\Connection\AbstractConnection->getResource()
 8 VENDORPATH\predis\predis\src\Connection\StreamConnection.php(394): Predis\Connection\StreamConnection->write('*2
$7
HGETALL
$16
DalleCreditLimit
')
 9 VENDORPATH\predis\predis\src\Connection\AbstractConnection.php(110): Predis\Connection\StreamConnection->writeRequest(Object(Predis\Command\HashGetAll))
10 VENDORPATH\predis\predis\src\Client.php(331): Predis\Connection\AbstractConnection->executeCommand(Object(Predis\Command\HashGetAll))
11 VENDORPATH\predis\predis\src\Client.php(314): Predis\Client->executeCommand(Object(Predis\Command\HashGetAll))
12 APPPATH\Controllers\Api.php(982): Predis\Client->__call('hgetall', [...])
13 APPPATH\Controllers\Api.php(240): App\Controllers\Api->getTokenUsage()
14 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('get-token-usage')
15 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
16 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
17 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
18 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-06-24 01:48:50 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-24 01:48:50 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
WARNING - 2025-06-24 01:48:50 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('aiproStart', null, 1753321730, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(401): btflag_set('aiproStart', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-24 01:48:50 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('user_email', null, 1753321730, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(402): btflag_set('user_email', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-24 01:48:50 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('chatpro_KJLF4XgSL8wjlGm', null, 1753321730, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(403): btflag_set('chatpro_KJLF4XgSL8wjlGm', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-06-24 01:48:50 --> REQ ----------->
INFO - 2025-06-24 01:48:50 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 01:48:55 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
DEBUG - 2025-06-24 01:48:55 --> btdbFindBy ---> 
INFO - 2025-06-24 01:48:55 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 01:48:55 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
DEBUG - 2025-06-24 01:48:55 --> btdbFindBy ---> 
INFO - 2025-06-24 01:48:55 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
WARNING - 2025-06-24 01:48:55 --> [DEPRECATED] strtolower(): Passing null to parameter #1 ($string) of type string is deprecated in APPPATH\Controllers\Api.php on line 1536.
 1 APPPATH\Controllers\Api.php(1536): strtolower(null)
 2 APPPATH\Controllers\Api.php(189): App\Controllers\Api->getSubscription()
 3 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('get-subscription')
 4 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 5 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 6 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 7 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-24 01:48:55 --> [DEPRECATED] strtolower(): Passing null to parameter #1 ($string) of type string is deprecated in APPPATH\Controllers\Api.php on line 1537.
 1 APPPATH\Controllers\Api.php(1537): strtolower(null)
 2 APPPATH\Controllers\Api.php(189): App\Controllers\Api->getSubscription()
 3 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('get-subscription')
 4 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 5 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 6 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 7 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
INFO - 2025-06-24 01:48:55 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 01:48:55 --> btdbFindBy ---> 
DEBUG - 2025-06-24 01:48:55 --> btdbFindBy ---> SELECT `start-plan`.*, `start-account`.*, DATE_FORMAT(resumed_at , "%M %e, %Y") as "resumed_at_formatted", `start-user`.`mode`, case when DATE_ADD(end_date, INTERVAL 1 DAY) < now() then 'yes' else 'no' end as 'expired'
FROM `start-account`
JOIN `start-user` ON `start-user`.`user_id` = `start-account`.`user_id`
JOIN `start-plan` ON `start-plan`.`plan_id` = `start-account`.`plan_id`
WHERE `start-account`.`user_id` = '2'
ORDER BY `start-account`.`end_date` DESC
WARNING - 2025-06-24 01:48:55 --> [DEPRECATED] strtolower(): Passing null to parameter #1 ($string) of type string is deprecated in APPPATH\Controllers\User\Settings.php on line 163.
 1 APPPATH\Controllers\User\Settings.php(163): strtolower(null)
 2 APPPATH\Controllers\User\Settings.php(59): App\Controllers\User\Settings->get_subs_data()
 3 APPPATH\Controllers\User\Settings.php(45): App\Controllers\User\Settings->getDiscountPricing()
 4 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\User\Settings->index()
 5 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\User\Settings))
 6 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 7 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 8 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-24 01:48:55 --> [DEPRECATED] strtolower(): Passing null to parameter #1 ($string) of type string is deprecated in APPPATH\Controllers\User\Settings.php on line 164.
 1 APPPATH\Controllers\User\Settings.php(164): strtolower(null)
 2 APPPATH\Controllers\User\Settings.php(59): App\Controllers\User\Settings->get_subs_data()
 3 APPPATH\Controllers\User\Settings.php(45): App\Controllers\User\Settings->getDiscountPricing()
 4 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\User\Settings->index()
 5 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\User\Settings))
 6 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 7 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 8 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-24 01:48:55 --> [DEPRECATED] strtolower(): Passing null to parameter #1 ($string) of type string is deprecated in APPPATH\Controllers\User\Settings.php on line 87.
 1 APPPATH\Controllers\User\Settings.php(87): strtolower(null)
 2 APPPATH\Controllers\User\Settings.php(45): App\Controllers\User\Settings->getDiscountPricing()
 3 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\User\Settings->index()
 4 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\User\Settings))
 5 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 6 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 7 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-06-24 01:48:55 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-24 01:48:55 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
WARNING - 2025-06-24 01:48:55 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('aiproStart', null, 1753321735, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(401): btflag_set('aiproStart', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-24 01:48:55 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('user_email', null, 1753321735, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(402): btflag_set('user_email', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-24 01:48:55 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('chatpro_KJLF4XgSL8wjlGm', null, 1753321735, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(403): btflag_set('chatpro_KJLF4XgSL8wjlGm', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-06-24 01:48:56 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-24 01:48:56 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 01:48:58 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-24 01:48:58 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
WARNING - 2025-06-24 01:48:58 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('aiproStart', null, 1753321738, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(401): btflag_set('aiproStart', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-24 01:48:58 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('user_email', null, 1753321738, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(402): btflag_set('user_email', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-24 01:48:58 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('chatpro_KJLF4XgSL8wjlGm', null, 1753321738, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(403): btflag_set('chatpro_KJLF4XgSL8wjlGm', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-06-24 01:48:58 --> REQ ----------->
INFO - 2025-06-24 01:48:58 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 01:49:01 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
DEBUG - 2025-06-24 01:49:01 --> btdbFindBy ---> 
INFO - 2025-06-24 01:49:01 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 01:49:01 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
DEBUG - 2025-06-24 01:49:01 --> btdbFindBy ---> 
INFO - 2025-06-24 01:49:01 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
WARNING - 2025-06-24 01:49:01 --> [DEPRECATED] strtolower(): Passing null to parameter #1 ($string) of type string is deprecated in APPPATH\Controllers\Api.php on line 1536.
 1 APPPATH\Controllers\Api.php(1536): strtolower(null)
 2 APPPATH\Controllers\Api.php(189): App\Controllers\Api->getSubscription()
 3 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('get-subscription')
 4 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 5 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 6 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 7 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-24 01:49:01 --> [DEPRECATED] strtolower(): Passing null to parameter #1 ($string) of type string is deprecated in APPPATH\Controllers\Api.php on line 1537.
 1 APPPATH\Controllers\Api.php(1537): strtolower(null)
 2 APPPATH\Controllers\Api.php(189): App\Controllers\Api->getSubscription()
 3 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('get-subscription')
 4 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 5 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 6 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 7 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-06-24 01:49:01 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-24 01:49:01 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
WARNING - 2025-06-24 01:49:01 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('aiproStart', null, 1753321741, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(401): btflag_set('aiproStart', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-24 01:49:01 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('user_email', null, 1753321741, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(402): btflag_set('user_email', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-24 01:49:01 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('chatpro_KJLF4XgSL8wjlGm', null, 1753321741, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(403): btflag_set('chatpro_KJLF4XgSL8wjlGm', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-06-24 01:49:01 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-24 01:49:01 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 01:49:10 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-24 01:49:10 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
WARNING - 2025-06-24 01:49:10 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('aiproStart', null, 1753321750, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(401): btflag_set('aiproStart', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-24 01:49:10 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('user_email', null, 1753321750, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(402): btflag_set('user_email', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-24 01:49:10 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('chatpro_KJLF4XgSL8wjlGm', null, 1753321750, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(403): btflag_set('chatpro_KJLF4XgSL8wjlGm', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-06-24 01:49:11 --> REQ ----------->
INFO - 2025-06-24 01:49:11 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2025-06-24 01:49:13 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 01:49:13 --> btdbFindBy ---> 
DEBUG - 2025-06-24 01:49:13 --> btdbFindBy ---> SELECT `start-plan`.*, `start-account`.*, DATE_FORMAT(resumed_at , "%M %e, %Y") as "resumed_at_formatted", `start-user`.`mode`, case when DATE_ADD(end_date, INTERVAL 1 DAY) < now() then 'yes' else 'no' end as 'expired'
FROM `start-account`
JOIN `start-user` ON `start-user`.`user_id` = `start-account`.`user_id`
JOIN `start-plan` ON `start-plan`.`plan_id` = `start-account`.`plan_id`
WHERE `start-account`.`user_id` = '2'
ORDER BY `start-account`.`end_date` DESC
WARNING - 2025-06-24 01:49:13 --> [DEPRECATED] strtolower(): Passing null to parameter #1 ($string) of type string is deprecated in APPPATH\Controllers\User\Settings.php on line 163.
 1 APPPATH\Controllers\User\Settings.php(163): strtolower(null)
 2 APPPATH\Controllers\User\Settings.php(59): App\Controllers\User\Settings->get_subs_data()
 3 APPPATH\Controllers\User\Settings.php(45): App\Controllers\User\Settings->getDiscountPricing()
 4 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\User\Settings->index()
 5 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\User\Settings))
 6 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 7 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 8 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-24 01:49:13 --> [DEPRECATED] strtolower(): Passing null to parameter #1 ($string) of type string is deprecated in APPPATH\Controllers\User\Settings.php on line 164.
 1 APPPATH\Controllers\User\Settings.php(164): strtolower(null)
 2 APPPATH\Controllers\User\Settings.php(59): App\Controllers\User\Settings->get_subs_data()
 3 APPPATH\Controllers\User\Settings.php(45): App\Controllers\User\Settings->getDiscountPricing()
 4 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\User\Settings->index()
 5 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\User\Settings))
 6 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 7 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 8 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-24 01:49:13 --> [DEPRECATED] strtolower(): Passing null to parameter #1 ($string) of type string is deprecated in APPPATH\Controllers\User\Settings.php on line 87.
 1 APPPATH\Controllers\User\Settings.php(87): strtolower(null)
 2 APPPATH\Controllers\User\Settings.php(45): App\Controllers\User\Settings->getDiscountPricing()
 3 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\User\Settings->index()
 4 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\User\Settings))
 5 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 6 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 7 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-06-24 01:49:14 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-24 01:49:14 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
WARNING - 2025-06-24 01:49:14 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('aiproStart', null, 1753321754, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(401): btflag_set('aiproStart', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-24 01:49:14 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('user_email', null, 1753321754, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(402): btflag_set('user_email', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-24 01:49:14 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('chatpro_KJLF4XgSL8wjlGm', null, 1753321754, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(403): btflag_set('chatpro_KJLF4XgSL8wjlGm', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-06-24 01:49:14 --> REQ ----------->
INFO - 2025-06-24 01:49:14 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 01:49:16 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
DEBUG - 2025-06-24 01:49:16 --> btdbFindBy ---> 
INFO - 2025-06-24 01:49:16 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 01:49:16 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
DEBUG - 2025-06-24 01:49:16 --> btdbFindBy ---> 
INFO - 2025-06-24 01:49:16 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
WARNING - 2025-06-24 01:49:16 --> [DEPRECATED] strtolower(): Passing null to parameter #1 ($string) of type string is deprecated in APPPATH\Controllers\Api.php on line 1536.
 1 APPPATH\Controllers\Api.php(1536): strtolower(null)
 2 APPPATH\Controllers\Api.php(189): App\Controllers\Api->getSubscription()
 3 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('get-subscription')
 4 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 5 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 6 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 7 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-24 01:49:16 --> [DEPRECATED] strtolower(): Passing null to parameter #1 ($string) of type string is deprecated in APPPATH\Controllers\Api.php on line 1537.
 1 APPPATH\Controllers\Api.php(1537): strtolower(null)
 2 APPPATH\Controllers\Api.php(189): App\Controllers\Api->getSubscription()
 3 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('get-subscription')
 4 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 5 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 6 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 7 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-06-24 01:49:17 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-24 01:49:17 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
WARNING - 2025-06-24 01:49:17 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('aiproStart', null, 1753321757, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(401): btflag_set('aiproStart', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-24 01:49:17 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('user_email', null, 1753321757, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(402): btflag_set('user_email', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-24 01:49:17 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('chatpro_KJLF4XgSL8wjlGm', null, 1753321757, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(403): btflag_set('chatpro_KJLF4XgSL8wjlGm', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-06-24 01:49:17 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-24 01:49:17 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 01:49:53 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-24 01:49:53 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
WARNING - 2025-06-24 01:49:53 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('aiproStart', null, 1753321793, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(401): btflag_set('aiproStart', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-24 01:49:53 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('user_email', null, 1753321793, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(402): btflag_set('user_email', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-24 01:49:53 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('chatpro_KJLF4XgSL8wjlGm', null, 1753321793, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(403): btflag_set('chatpro_KJLF4XgSL8wjlGm', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-06-24 01:49:53 --> REQ ----------->
INFO - 2025-06-24 01:49:53 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 01:49:57 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-24 01:49:57 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
WARNING - 2025-06-24 01:49:57 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('aiproStart', null, 1753321797, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(401): btflag_set('aiproStart', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-24 01:49:57 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('user_email', null, 1753321797, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(402): btflag_set('user_email', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-24 01:49:57 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('chatpro_KJLF4XgSL8wjlGm', null, 1753321797, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(403): btflag_set('chatpro_KJLF4XgSL8wjlGm', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-06-24 01:49:57 --> REQ ----------->
INFO - 2025-06-24 01:49:57 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2025-06-24 01:50:01 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 01:50:01 --> btdbFindBy ---> 
DEBUG - 2025-06-24 01:50:01 --> btdbFindBy ---> SELECT `start-plan`.*, `start-account`.*, DATE_FORMAT(resumed_at , "%M %e, %Y") as "resumed_at_formatted", `start-user`.`mode`, case when DATE_ADD(end_date, INTERVAL 1 DAY) < now() then 'yes' else 'no' end as 'expired'
FROM `start-account`
JOIN `start-user` ON `start-user`.`user_id` = `start-account`.`user_id`
JOIN `start-plan` ON `start-plan`.`plan_id` = `start-account`.`plan_id`
WHERE `start-account`.`user_id` = '2'
ORDER BY `start-account`.`end_date` DESC
WARNING - 2025-06-24 01:50:01 --> [DEPRECATED] strtolower(): Passing null to parameter #1 ($string) of type string is deprecated in APPPATH\Controllers\User\Settings.php on line 163.
 1 APPPATH\Controllers\User\Settings.php(163): strtolower(null)
 2 APPPATH\Controllers\User\Settings.php(59): App\Controllers\User\Settings->get_subs_data()
 3 APPPATH\Controllers\User\Settings.php(45): App\Controllers\User\Settings->getDiscountPricing()
 4 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\User\Settings->index()
 5 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\User\Settings))
 6 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 7 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 8 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-24 01:50:01 --> [DEPRECATED] strtolower(): Passing null to parameter #1 ($string) of type string is deprecated in APPPATH\Controllers\User\Settings.php on line 164.
 1 APPPATH\Controllers\User\Settings.php(164): strtolower(null)
 2 APPPATH\Controllers\User\Settings.php(59): App\Controllers\User\Settings->get_subs_data()
 3 APPPATH\Controllers\User\Settings.php(45): App\Controllers\User\Settings->getDiscountPricing()
 4 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\User\Settings->index()
 5 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\User\Settings))
 6 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 7 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 8 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-24 01:50:01 --> [DEPRECATED] strtolower(): Passing null to parameter #1 ($string) of type string is deprecated in APPPATH\Controllers\User\Settings.php on line 87.
 1 APPPATH\Controllers\User\Settings.php(87): strtolower(null)
 2 APPPATH\Controllers\User\Settings.php(45): App\Controllers\User\Settings->getDiscountPricing()
 3 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\User\Settings->index()
 4 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\User\Settings))
 5 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 6 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 7 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-06-24 01:50:02 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-24 01:50:02 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
WARNING - 2025-06-24 01:50:02 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('aiproStart', null, 1753321802, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(401): btflag_set('aiproStart', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-24 01:50:02 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('user_email', null, 1753321802, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(402): btflag_set('user_email', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-24 01:50:02 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('chatpro_KJLF4XgSL8wjlGm', null, 1753321802, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(403): btflag_set('chatpro_KJLF4XgSL8wjlGm', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-06-24 01:50:02 --> REQ ----------->
INFO - 2025-06-24 01:50:02 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 01:50:04 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
DEBUG - 2025-06-24 01:50:04 --> btdbFindBy ---> 
INFO - 2025-06-24 01:50:04 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 01:50:04 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
DEBUG - 2025-06-24 01:50:04 --> btdbFindBy ---> 
INFO - 2025-06-24 01:50:04 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
WARNING - 2025-06-24 01:50:04 --> [DEPRECATED] strtolower(): Passing null to parameter #1 ($string) of type string is deprecated in APPPATH\Controllers\Api.php on line 1534.
 1 APPPATH\Controllers\Api.php(1534): strtolower(null)
 2 APPPATH\Controllers\Api.php(189): App\Controllers\Api->getSubscription()
 3 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('get-subscription')
 4 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 5 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 6 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 7 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-24 01:50:04 --> [DEPRECATED] strtolower(): Passing null to parameter #1 ($string) of type string is deprecated in APPPATH\Controllers\Api.php on line 1535.
 1 APPPATH\Controllers\Api.php(1535): strtolower(null)
 2 APPPATH\Controllers\Api.php(189): App\Controllers\Api->getSubscription()
 3 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('get-subscription')
 4 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 5 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 6 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 7 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-06-24 01:50:05 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-24 01:50:05 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
WARNING - 2025-06-24 01:50:05 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('aiproStart', null, 1753321805, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(401): btflag_set('aiproStart', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-24 01:50:05 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('user_email', null, 1753321805, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(402): btflag_set('user_email', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-24 01:50:05 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('chatpro_KJLF4XgSL8wjlGm', null, 1753321805, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(403): btflag_set('chatpro_KJLF4XgSL8wjlGm', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-06-24 01:50:05 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-24 01:50:05 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 02:38:57 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-24 02:38:57 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
WARNING - 2025-06-24 02:38:57 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('aiproStart', null, 1753324737, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(401): btflag_set('aiproStart', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-24 02:38:57 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('user_email', null, 1753324737, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(402): btflag_set('user_email', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-24 02:38:57 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('chatpro_KJLF4XgSL8wjlGm', null, 1753324737, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(403): btflag_set('chatpro_KJLF4XgSL8wjlGm', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-06-24 02:38:58 --> REQ ----------->
INFO - 2025-06-24 02:38:58 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2025-06-24 02:41:27 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 02:41:30 --> REQ ----------->14
DEBUG - 2025-06-24 02:41:30 --> REQ ----------->
INFO - 2025-06-24 02:41:30 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 02:43:58 --> REQ ----------->
INFO - 2025-06-24 02:43:58 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2025-06-24 02:44:03 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 02:44:04 --> REQ ----------->
INFO - 2025-06-24 02:44:04 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 02:44:04 --> REQ ----------->14
DEBUG - 2025-06-24 02:44:04 --> REQ ----------->
INFO - 2025-06-24 02:44:04 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 02:44:05 --> REQ ----------->
INFO - 2025-06-24 02:44:05 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 02:44:05 --> REQ ----------->
INFO - 2025-06-24 02:44:05 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2025-06-24 02:44:15 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 02:44:16 --> REQ ----------->14
DEBUG - 2025-06-24 02:44:16 --> REQ ----------->
INFO - 2025-06-24 02:44:16 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 02:44:17 --> REQ ----------->
INFO - 2025-06-24 02:44:17 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 02:44:28 --> REQ ----------->
INFO - 2025-06-24 02:44:28 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2025-06-24 02:45:23 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 02:45:24 --> REQ ----------->14
DEBUG - 2025-06-24 02:45:24 --> REQ ----------->
INFO - 2025-06-24 02:45:24 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 02:45:25 --> REQ ----------->
INFO - 2025-06-24 02:45:25 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 02:45:26 --> REQ ----------->
INFO - 2025-06-24 02:45:26 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2025-06-24 02:45:30 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 02:45:31 --> REQ ----------->
INFO - 2025-06-24 02:45:31 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 02:45:31 --> REQ ----------->14
DEBUG - 2025-06-24 02:45:31 --> REQ ----------->
INFO - 2025-06-24 02:45:31 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 02:45:32 --> REQ ----------->
INFO - 2025-06-24 02:45:32 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 02:45:32 --> REQ ----------->
INFO - 2025-06-24 02:45:32 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 02:45:34 --> REQ ----------->
INFO - 2025-06-24 02:45:34 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 02:45:34 --> REQ ----------->
INFO - 2025-06-24 02:45:34 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 02:45:35 --> REQ ----------->
INFO - 2025-06-24 02:45:35 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 02:45:38 --> REQ ----------->
INFO - 2025-06-24 02:45:38 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 02:45:38 --> REQ ----------->
INFO - 2025-06-24 02:45:38 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 02:45:38 --> REQ ----------->
INFO - 2025-06-24 02:45:38 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 02:46:00 --> REQ ----------->
INFO - 2025-06-24 02:46:00 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2025-06-24 02:46:00 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 02:46:00 --> REQ ----------->14
DEBUG - 2025-06-24 02:46:01 --> REQ ----------->
INFO - 2025-06-24 02:46:01 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2025-06-24 02:46:06 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 02:46:07 --> REQ ----------->
INFO - 2025-06-24 02:46:07 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 02:46:07 --> REQ ----------->14
DEBUG - 2025-06-24 02:46:07 --> REQ ----------->
INFO - 2025-06-24 02:46:07 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 02:46:32 --> REQ ----------->
INFO - 2025-06-24 02:46:32 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2025-06-24 02:46:34 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 02:46:35 --> REQ ----------->
INFO - 2025-06-24 02:46:35 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 02:46:35 --> REQ ----------->01
DEBUG - 2025-06-24 02:46:35 --> REQ ----------->
INFO - 2025-06-24 02:46:35 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 02:46:42 --> REQ ----------->
INFO - 2025-06-24 02:46:42 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 02:46:47 --> REQ ----------->
INFO - 2025-06-24 02:46:47 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 03:35:48 --> REQ ----------->
INFO - 2025-06-24 03:35:48 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 03:35:49 --> REQ ----------->
INFO - 2025-06-24 03:35:49 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 03:35:51 --> REQ ----------->
INFO - 2025-06-24 03:35:51 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2025-06-24 03:35:52 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 03:35:52 --> REQ ----------->14
DEBUG - 2025-06-24 03:35:52 --> REQ ----------->
INFO - 2025-06-24 03:35:53 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 03:35:55 --> REQ ----------->
INFO - 2025-06-24 03:35:55 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 03:35:55 --> REQ ----------->
INFO - 2025-06-24 03:35:55 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 03:35:56 --> REQ ----------->
INFO - 2025-06-24 03:35:56 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 03:35:56 --> REQ ----------->
INFO - 2025-06-24 03:35:56 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 03:35:59 --> REQ ----------->testuser061125_02@mailinator.com123123
DEBUG - 2025-06-24 03:35:59 --> btdbFindBy ---> 
DEBUG - 2025-06-24 03:35:59 --> btdbFindBy ---> SELECT *
FROM `start-user`
WHERE `email` = '<EMAIL>'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
INFO - 2025-06-24 03:35:59 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 03:35:59 --> btdbFindBy ---> SELECT `start-plan`.*, `start-account`.*, DATE_FORMAT(resumed_at , "%M %e, %Y") as "resumed_at_formatted", `start-user`.`mode`, case when DATE_ADD(end_date, INTERVAL 1 DAY) < now() then 'yes' else 'no' end as 'expired'
FROM `start-account`
JOIN `start-user` ON `start-user`.`user_id` = `start-account`.`user_id`
JOIN `start-plan` ON `start-plan`.`plan_id` = `start-account`.`plan_id`
WHERE `start-account`.`user_id` = '3'
ORDER BY `start-account`.`end_date` DESC
DEBUG - 2025-06-24 03:35:59 --> btdbFindBy ---> SELECT *
FROM `start-plan`
WHERE `plan_id` = '144'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-24 03:35:59 --> login-------> <EMAIL>
DEBUG - 2025-06-24 03:35:59 --> btdbFindBy ---> SELECT *
FROM `start-user_app`
WHERE `email` = '<EMAIL>'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
INFO - 2025-06-24 03:35:59 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 03:35:59 --> btdbFindBy ---> 
DEBUG - 2025-06-24 03:35:59 --> btdbFindBy ---> SELECT `start-plan`.*, `start-account`.*, DATE_FORMAT(resumed_at , "%M %e, %Y") as "resumed_at_formatted", `start-user`.`mode`, case when DATE_ADD(end_date, INTERVAL 1 DAY) < now() then 'yes' else 'no' end as 'expired'
FROM `start-account`
JOIN `start-user` ON `start-user`.`user_id` = `start-account`.`user_id`
JOIN `start-plan` ON `start-plan`.`plan_id` = `start-account`.`plan_id`
WHERE `start-account`.`user_id` = '3'
ORDER BY `start-account`.`end_date` DESC
DEBUG - 2025-06-24 03:35:59 --> btdbFindBy ---> SELECT *
FROM `start-plan`
WHERE `plan_id` = '144'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-24 03:36:00 --> REQ ----------->$2y$09$5NQl9xDYdIbEUIpx946W9uSLz
INFO - 2025-06-24 03:36:00 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
WARNING - 2025-06-24 03:36:00 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('aiproStart', null, **********, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(401): btflag_set('aiproStart', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-24 03:36:00 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('user_email', null, **********, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(402): btflag_set('user_email', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-24 03:36:00 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('chatpro_KJLF4XgSL8wjlGm', null, **********, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(403): btflag_set('chatpro_KJLF4XgSL8wjlGm', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-06-24 03:36:00 --> REQ ----------->
DEBUG - 2025-06-24 03:36:00 --> REQ ----------->
INFO - 2025-06-24 03:36:00 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 03:36:03 --> REQ ----------->$2y$09$5NQl9xDYdIbEUIpx946W9uSLz
DEBUG - 2025-06-24 03:36:04 --> btdbFindBy ---> 
INFO - 2025-06-24 03:36:04 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
WARNING - 2025-06-24 03:36:04 --> [DEPRECATED] strtolower(): Passing null to parameter #1 ($string) of type string is deprecated in APPPATH\Controllers\Api.php on line 1534.
 1 APPPATH\Controllers\Api.php(1534): strtolower(null)
 2 APPPATH\Controllers\Api.php(189): App\Controllers\Api->getSubscription()
 3 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('get-subscription')
 4 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 5 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 6 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 7 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-24 03:36:04 --> [DEPRECATED] strtolower(): Passing null to parameter #1 ($string) of type string is deprecated in APPPATH\Controllers\Api.php on line 1535.
 1 APPPATH\Controllers\Api.php(1535): strtolower(null)
 2 APPPATH\Controllers\Api.php(189): App\Controllers\Api->getSubscription()
 3 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('get-subscription')
 4 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 5 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 6 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 7 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
INFO - 2025-06-24 03:36:04 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 03:36:04 --> btdbFindBy ---> 
DEBUG - 2025-06-24 03:36:04 --> btdbFindBy ---> SELECT `start-plan`.*, `start-account`.*, DATE_FORMAT(resumed_at , "%M %e, %Y") as "resumed_at_formatted", `start-user`.`mode`, case when DATE_ADD(end_date, INTERVAL 1 DAY) < now() then 'yes' else 'no' end as 'expired'
FROM `start-account`
JOIN `start-user` ON `start-user`.`user_id` = `start-account`.`user_id`
JOIN `start-plan` ON `start-plan`.`plan_id` = `start-account`.`plan_id`
WHERE `start-account`.`user_id` = '3'
ORDER BY `start-account`.`end_date` DESC
WARNING - 2025-06-24 03:36:04 --> [DEPRECATED] strtolower(): Passing null to parameter #1 ($string) of type string is deprecated in APPPATH\Controllers\User\Settings.php on line 163.
 1 APPPATH\Controllers\User\Settings.php(163): strtolower(null)
 2 APPPATH\Controllers\User\Settings.php(59): App\Controllers\User\Settings->get_subs_data()
 3 APPPATH\Controllers\User\Settings.php(45): App\Controllers\User\Settings->getDiscountPricing()
 4 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\User\Settings->index()
 5 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\User\Settings))
 6 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 7 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 8 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-24 03:36:04 --> [DEPRECATED] strtolower(): Passing null to parameter #1 ($string) of type string is deprecated in APPPATH\Controllers\User\Settings.php on line 164.
 1 APPPATH\Controllers\User\Settings.php(164): strtolower(null)
 2 APPPATH\Controllers\User\Settings.php(59): App\Controllers\User\Settings->get_subs_data()
 3 APPPATH\Controllers\User\Settings.php(45): App\Controllers\User\Settings->getDiscountPricing()
 4 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\User\Settings->index()
 5 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\User\Settings))
 6 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 7 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 8 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-24 03:36:04 --> [DEPRECATED] strtolower(): Passing null to parameter #1 ($string) of type string is deprecated in APPPATH\Controllers\User\Settings.php on line 87.
 1 APPPATH\Controllers\User\Settings.php(87): strtolower(null)
 2 APPPATH\Controllers\User\Settings.php(45): App\Controllers\User\Settings->getDiscountPricing()
 3 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\User\Settings->index()
 4 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\User\Settings))
 5 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 6 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 7 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-06-24 03:36:05 --> REQ ----------->$2y$09$5NQl9xDYdIbEUIpx946W9uSLz
INFO - 2025-06-24 03:36:05 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
WARNING - 2025-06-24 03:36:05 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('aiproStart', null, 1753328165, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(401): btflag_set('aiproStart', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-24 03:36:05 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('user_email', null, 1753328165, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(402): btflag_set('user_email', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-24 03:36:05 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('chatpro_KJLF4XgSL8wjlGm', null, 1753328165, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(403): btflag_set('chatpro_KJLF4XgSL8wjlGm', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-06-24 03:36:05 --> REQ ----------->
INFO - 2025-06-24 03:36:05 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 03:36:07 --> REQ ----------->$2y$09$5NQl9xDYdIbEUIpx946W9uSLz
INFO - 2025-06-24 03:36:07 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 03:36:07 --> REQ ----------->$2y$09$5NQl9xDYdIbEUIpx946W9uSLz
INFO - 2025-06-24 03:36:07 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 03:36:10 --> REQ ----------->$2y$09$5NQl9xDYdIbEUIpx946W9uSLz
DEBUG - 2025-06-24 03:36:10 --> btdbFindBy ---> 
INFO - 2025-06-24 03:36:10 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 03:36:10 --> REQ ----------->$2y$09$5NQl9xDYdIbEUIpx946W9uSLz
DEBUG - 2025-06-24 03:36:10 --> btdbFindBy ---> 
INFO - 2025-06-24 03:36:10 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
WARNING - 2025-06-24 03:36:10 --> [DEPRECATED] strtolower(): Passing null to parameter #1 ($string) of type string is deprecated in APPPATH\Controllers\Api.php on line 1534.
 1 APPPATH\Controllers\Api.php(1534): strtolower(null)
 2 APPPATH\Controllers\Api.php(189): App\Controllers\Api->getSubscription()
 3 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('get-subscription')
 4 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 5 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 6 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 7 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-24 03:36:10 --> [DEPRECATED] strtolower(): Passing null to parameter #1 ($string) of type string is deprecated in APPPATH\Controllers\Api.php on line 1535.
 1 APPPATH\Controllers\Api.php(1535): strtolower(null)
 2 APPPATH\Controllers\Api.php(189): App\Controllers\Api->getSubscription()
 3 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('get-subscription')
 4 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 5 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 6 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 7 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-06-24 03:36:11 --> REQ ----------->$2y$09$5NQl9xDYdIbEUIpx946W9uSLz
INFO - 2025-06-24 03:36:11 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
WARNING - 2025-06-24 03:36:11 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('aiproStart', null, 1753328171, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(401): btflag_set('aiproStart', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-24 03:36:11 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('user_email', null, 1753328171, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(402): btflag_set('user_email', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-24 03:36:11 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('chatpro_KJLF4XgSL8wjlGm', null, 1753328171, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(403): btflag_set('chatpro_KJLF4XgSL8wjlGm', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-06-24 03:36:11 --> REQ ----------->$2y$09$5NQl9xDYdIbEUIpx946W9uSLz
DEBUG - 2025-06-24 03:36:11 --> btdbFindBy ---> 
INFO - 2025-06-24 03:36:11 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
WARNING - 2025-06-24 03:36:11 --> [DEPRECATED] strtolower(): Passing null to parameter #1 ($string) of type string is deprecated in APPPATH\Controllers\Api.php on line 1534.
 1 APPPATH\Controllers\Api.php(1534): strtolower(null)
 2 APPPATH\Controllers\Api.php(189): App\Controllers\Api->getSubscription()
 3 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('get-subscription')
 4 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 5 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 6 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 7 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-24 03:36:11 --> [DEPRECATED] strtolower(): Passing null to parameter #1 ($string) of type string is deprecated in APPPATH\Controllers\Api.php on line 1535.
 1 APPPATH\Controllers\Api.php(1535): strtolower(null)
 2 APPPATH\Controllers\Api.php(189): App\Controllers\Api->getSubscription()
 3 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('get-subscription')
 4 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 5 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 6 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 7 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-06-24 03:36:52 --> REQ ----------->$2y$09$5NQl9xDYdIbEUIpx946W9uSLz
INFO - 2025-06-24 03:36:52 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
WARNING - 2025-06-24 03:36:52 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('aiproStart', null, 1753328212, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(401): btflag_set('aiproStart', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-24 03:36:52 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('user_email', null, 1753328212, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(402): btflag_set('user_email', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-24 03:36:52 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('chatpro_KJLF4XgSL8wjlGm', null, 1753328212, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(403): btflag_set('chatpro_KJLF4XgSL8wjlGm', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-06-24 03:36:52 --> REQ ----------->
INFO - 2025-06-24 03:36:52 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 03:37:14 --> REQ ----------->$2y$09$5NQl9xDYdIbEUIpx946W9uSLz
INFO - 2025-06-24 03:37:14 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
WARNING - 2025-06-24 03:37:14 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('aiproStart', null, 1753328234, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(401): btflag_set('aiproStart', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-24 03:37:14 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('user_email', null, 1753328234, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(402): btflag_set('user_email', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-24 03:37:14 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('chatpro_KJLF4XgSL8wjlGm', null, 1753328234, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(403): btflag_set('chatpro_KJLF4XgSL8wjlGm', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-06-24 03:37:14 --> REQ ----------->
INFO - 2025-06-24 03:37:14 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2025-06-24 03:37:23 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 03:37:23 --> btdbFindBy ---> 
DEBUG - 2025-06-24 03:37:23 --> btdbFindBy ---> SELECT `start-plan`.*, `start-account`.*, DATE_FORMAT(resumed_at , "%M %e, %Y") as "resumed_at_formatted", `start-user`.`mode`, case when DATE_ADD(end_date, INTERVAL 1 DAY) < now() then 'yes' else 'no' end as 'expired'
FROM `start-account`
JOIN `start-user` ON `start-user`.`user_id` = `start-account`.`user_id`
JOIN `start-plan` ON `start-plan`.`plan_id` = `start-account`.`plan_id`
WHERE `start-account`.`user_id` = '3'
ORDER BY `start-account`.`end_date` DESC
WARNING - 2025-06-24 03:37:23 --> [DEPRECATED] strtolower(): Passing null to parameter #1 ($string) of type string is deprecated in APPPATH\Controllers\User\Settings.php on line 163.
 1 APPPATH\Controllers\User\Settings.php(163): strtolower(null)
 2 APPPATH\Controllers\User\Settings.php(59): App\Controllers\User\Settings->get_subs_data()
 3 APPPATH\Controllers\User\Settings.php(45): App\Controllers\User\Settings->getDiscountPricing()
 4 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\User\Settings->index()
 5 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\User\Settings))
 6 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 7 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 8 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-24 03:37:23 --> [DEPRECATED] strtolower(): Passing null to parameter #1 ($string) of type string is deprecated in APPPATH\Controllers\User\Settings.php on line 164.
 1 APPPATH\Controllers\User\Settings.php(164): strtolower(null)
 2 APPPATH\Controllers\User\Settings.php(59): App\Controllers\User\Settings->get_subs_data()
 3 APPPATH\Controllers\User\Settings.php(45): App\Controllers\User\Settings->getDiscountPricing()
 4 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\User\Settings->index()
 5 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\User\Settings))
 6 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 7 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 8 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-24 03:37:23 --> [DEPRECATED] strtolower(): Passing null to parameter #1 ($string) of type string is deprecated in APPPATH\Controllers\User\Settings.php on line 87.
 1 APPPATH\Controllers\User\Settings.php(87): strtolower(null)
 2 APPPATH\Controllers\User\Settings.php(45): App\Controllers\User\Settings->getDiscountPricing()
 3 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\User\Settings->index()
 4 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\User\Settings))
 5 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 6 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 7 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-06-24 03:37:23 --> REQ ----------->$2y$09$5NQl9xDYdIbEUIpx946W9uSLz
INFO - 2025-06-24 03:37:23 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
WARNING - 2025-06-24 03:37:23 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('aiproStart', null, 1753328243, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(401): btflag_set('aiproStart', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-24 03:37:23 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('user_email', null, 1753328243, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(402): btflag_set('user_email', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-24 03:37:23 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('chatpro_KJLF4XgSL8wjlGm', null, 1753328243, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(403): btflag_set('chatpro_KJLF4XgSL8wjlGm', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-06-24 03:37:23 --> REQ ----------->
INFO - 2025-06-24 03:37:23 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 03:37:26 --> REQ ----------->$2y$09$5NQl9xDYdIbEUIpx946W9uSLz
DEBUG - 2025-06-24 03:37:26 --> btdbFindBy ---> 
INFO - 2025-06-24 03:37:26 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 03:37:26 --> REQ ----------->$2y$09$5NQl9xDYdIbEUIpx946W9uSLz
DEBUG - 2025-06-24 03:37:26 --> btdbFindBy ---> 
INFO - 2025-06-24 03:37:26 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
WARNING - 2025-06-24 03:37:26 --> [DEPRECATED] strtolower(): Passing null to parameter #1 ($string) of type string is deprecated in APPPATH\Controllers\Api.php on line 1534.
 1 APPPATH\Controllers\Api.php(1534): strtolower(null)
 2 APPPATH\Controllers\Api.php(189): App\Controllers\Api->getSubscription()
 3 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('get-subscription')
 4 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 5 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 6 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 7 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-24 03:37:26 --> [DEPRECATED] strtolower(): Passing null to parameter #1 ($string) of type string is deprecated in APPPATH\Controllers\Api.php on line 1535.
 1 APPPATH\Controllers\Api.php(1535): strtolower(null)
 2 APPPATH\Controllers\Api.php(189): App\Controllers\Api->getSubscription()
 3 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('get-subscription')
 4 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 5 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 6 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 7 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-06-24 03:37:26 --> REQ ----------->$2y$09$5NQl9xDYdIbEUIpx946W9uSLz
INFO - 2025-06-24 03:37:26 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
WARNING - 2025-06-24 03:37:26 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('aiproStart', null, 1753328246, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(401): btflag_set('aiproStart', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-24 03:37:26 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('user_email', null, 1753328246, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(402): btflag_set('user_email', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-24 03:37:26 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('chatpro_KJLF4XgSL8wjlGm', null, 1753328246, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(403): btflag_set('chatpro_KJLF4XgSL8wjlGm', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-06-24 03:37:27 --> REQ ----------->$2y$09$5NQl9xDYdIbEUIpx946W9uSLz
INFO - 2025-06-24 03:37:27 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 03:38:07 --> REQ ----------->$2y$09$5NQl9xDYdIbEUIpx946W9uSLz
INFO - 2025-06-24 03:38:07 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
WARNING - 2025-06-24 03:38:07 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('aiproStart', null, 1753328287, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(401): btflag_set('aiproStart', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-24 03:38:07 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('user_email', null, 1753328287, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(402): btflag_set('user_email', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-24 03:38:07 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('chatpro_KJLF4XgSL8wjlGm', null, 1753328287, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(403): btflag_set('chatpro_KJLF4XgSL8wjlGm', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-06-24 03:38:07 --> REQ ----------->
INFO - 2025-06-24 03:38:07 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2025-06-24 03:38:10 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 03:38:10 --> btdbFindBy ---> 
DEBUG - 2025-06-24 03:38:10 --> btdbFindBy ---> SELECT `start-plan`.*, `start-account`.*, DATE_FORMAT(resumed_at , "%M %e, %Y") as "resumed_at_formatted", `start-user`.`mode`, case when DATE_ADD(end_date, INTERVAL 1 DAY) < now() then 'yes' else 'no' end as 'expired'
FROM `start-account`
JOIN `start-user` ON `start-user`.`user_id` = `start-account`.`user_id`
JOIN `start-plan` ON `start-plan`.`plan_id` = `start-account`.`plan_id`
WHERE `start-account`.`user_id` = '3'
ORDER BY `start-account`.`end_date` DESC
WARNING - 2025-06-24 03:38:10 --> [DEPRECATED] strtolower(): Passing null to parameter #1 ($string) of type string is deprecated in APPPATH\Controllers\User\Settings.php on line 163.
 1 APPPATH\Controllers\User\Settings.php(163): strtolower(null)
 2 APPPATH\Controllers\User\Settings.php(59): App\Controllers\User\Settings->get_subs_data()
 3 APPPATH\Controllers\User\Settings.php(45): App\Controllers\User\Settings->getDiscountPricing()
 4 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\User\Settings->index()
 5 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\User\Settings))
 6 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 7 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 8 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-24 03:38:10 --> [DEPRECATED] strtolower(): Passing null to parameter #1 ($string) of type string is deprecated in APPPATH\Controllers\User\Settings.php on line 164.
 1 APPPATH\Controllers\User\Settings.php(164): strtolower(null)
 2 APPPATH\Controllers\User\Settings.php(59): App\Controllers\User\Settings->get_subs_data()
 3 APPPATH\Controllers\User\Settings.php(45): App\Controllers\User\Settings->getDiscountPricing()
 4 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\User\Settings->index()
 5 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\User\Settings))
 6 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 7 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 8 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-24 03:38:10 --> [DEPRECATED] strtolower(): Passing null to parameter #1 ($string) of type string is deprecated in APPPATH\Controllers\User\Settings.php on line 87.
 1 APPPATH\Controllers\User\Settings.php(87): strtolower(null)
 2 APPPATH\Controllers\User\Settings.php(45): App\Controllers\User\Settings->getDiscountPricing()
 3 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\User\Settings->index()
 4 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\User\Settings))
 5 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 6 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 7 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-06-24 03:38:11 --> REQ ----------->$2y$09$5NQl9xDYdIbEUIpx946W9uSLz
INFO - 2025-06-24 03:38:11 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
WARNING - 2025-06-24 03:38:11 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('aiproStart', null, 1753328291, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(401): btflag_set('aiproStart', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-24 03:38:11 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('user_email', null, 1753328291, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(402): btflag_set('user_email', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-24 03:38:11 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('chatpro_KJLF4XgSL8wjlGm', null, 1753328291, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(403): btflag_set('chatpro_KJLF4XgSL8wjlGm', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-06-24 03:38:11 --> REQ ----------->
INFO - 2025-06-24 03:38:11 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 03:38:13 --> REQ ----------->$2y$09$5NQl9xDYdIbEUIpx946W9uSLz
DEBUG - 2025-06-24 03:38:13 --> btdbFindBy ---> 
INFO - 2025-06-24 03:38:13 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 03:38:13 --> REQ ----------->$2y$09$5NQl9xDYdIbEUIpx946W9uSLz
DEBUG - 2025-06-24 03:38:13 --> btdbFindBy ---> 
INFO - 2025-06-24 03:38:13 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
WARNING - 2025-06-24 03:38:13 --> [DEPRECATED] strtolower(): Passing null to parameter #1 ($string) of type string is deprecated in APPPATH\Controllers\Api.php on line 1534.
 1 APPPATH\Controllers\Api.php(1534): strtolower(null)
 2 APPPATH\Controllers\Api.php(189): App\Controllers\Api->getSubscription()
 3 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('get-subscription')
 4 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 5 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 6 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 7 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-24 03:38:13 --> [DEPRECATED] strtolower(): Passing null to parameter #1 ($string) of type string is deprecated in APPPATH\Controllers\Api.php on line 1535.
 1 APPPATH\Controllers\Api.php(1535): strtolower(null)
 2 APPPATH\Controllers\Api.php(189): App\Controllers\Api->getSubscription()
 3 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('get-subscription')
 4 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 5 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 6 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 7 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-06-24 03:38:13 --> REQ ----------->$2y$09$5NQl9xDYdIbEUIpx946W9uSLz
INFO - 2025-06-24 03:38:13 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
WARNING - 2025-06-24 03:38:13 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('aiproStart', null, 1753328293, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(401): btflag_set('aiproStart', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-24 03:38:13 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('user_email', null, 1753328293, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(402): btflag_set('user_email', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-24 03:38:13 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('chatpro_KJLF4XgSL8wjlGm', null, 1753328293, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(403): btflag_set('chatpro_KJLF4XgSL8wjlGm', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-06-24 03:38:13 --> REQ ----------->$2y$09$5NQl9xDYdIbEUIpx946W9uSLz
INFO - 2025-06-24 03:38:13 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 03:39:17 --> REQ ----------->$2y$09$5NQl9xDYdIbEUIpx946W9uSLz
INFO - 2025-06-24 03:39:17 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
WARNING - 2025-06-24 03:39:17 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('aiproStart', null, 1753328357, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(401): btflag_set('aiproStart', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-24 03:39:17 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('user_email', null, 1753328357, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(402): btflag_set('user_email', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-24 03:39:17 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('chatpro_KJLF4XgSL8wjlGm', null, 1753328357, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(403): btflag_set('chatpro_KJLF4XgSL8wjlGm', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-06-24 03:39:17 --> REQ ----------->
INFO - 2025-06-24 03:39:17 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2025-06-24 03:39:20 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 03:39:20 --> btdbFindBy ---> 
DEBUG - 2025-06-24 03:39:20 --> btdbFindBy ---> SELECT `start-plan`.*, `start-account`.*, DATE_FORMAT(resumed_at , "%M %e, %Y") as "resumed_at_formatted", `start-user`.`mode`, case when DATE_ADD(end_date, INTERVAL 1 DAY) < now() then 'yes' else 'no' end as 'expired'
FROM `start-account`
JOIN `start-user` ON `start-user`.`user_id` = `start-account`.`user_id`
JOIN `start-plan` ON `start-plan`.`plan_id` = `start-account`.`plan_id`
WHERE `start-account`.`user_id` = '3'
ORDER BY `start-account`.`end_date` DESC
WARNING - 2025-06-24 03:39:20 --> [DEPRECATED] strtolower(): Passing null to parameter #1 ($string) of type string is deprecated in APPPATH\Controllers\User\Settings.php on line 163.
 1 APPPATH\Controllers\User\Settings.php(163): strtolower(null)
 2 APPPATH\Controllers\User\Settings.php(59): App\Controllers\User\Settings->get_subs_data()
 3 APPPATH\Controllers\User\Settings.php(45): App\Controllers\User\Settings->getDiscountPricing()
 4 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\User\Settings->index()
 5 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\User\Settings))
 6 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 7 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 8 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-24 03:39:20 --> [DEPRECATED] strtolower(): Passing null to parameter #1 ($string) of type string is deprecated in APPPATH\Controllers\User\Settings.php on line 164.
 1 APPPATH\Controllers\User\Settings.php(164): strtolower(null)
 2 APPPATH\Controllers\User\Settings.php(59): App\Controllers\User\Settings->get_subs_data()
 3 APPPATH\Controllers\User\Settings.php(45): App\Controllers\User\Settings->getDiscountPricing()
 4 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\User\Settings->index()
 5 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\User\Settings))
 6 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 7 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 8 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-24 03:39:20 --> [DEPRECATED] strtolower(): Passing null to parameter #1 ($string) of type string is deprecated in APPPATH\Controllers\User\Settings.php on line 87.
 1 APPPATH\Controllers\User\Settings.php(87): strtolower(null)
 2 APPPATH\Controllers\User\Settings.php(45): App\Controllers\User\Settings->getDiscountPricing()
 3 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\User\Settings->index()
 4 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\User\Settings))
 5 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 6 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 7 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-06-24 03:39:21 --> REQ ----------->$2y$09$5NQl9xDYdIbEUIpx946W9uSLz
INFO - 2025-06-24 03:39:21 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
WARNING - 2025-06-24 03:39:21 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('aiproStart', null, 1753328361, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(401): btflag_set('aiproStart', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-24 03:39:21 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('user_email', null, 1753328361, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(402): btflag_set('user_email', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-24 03:39:21 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('chatpro_KJLF4XgSL8wjlGm', null, 1753328361, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(403): btflag_set('chatpro_KJLF4XgSL8wjlGm', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-06-24 03:39:21 --> REQ ----------->
INFO - 2025-06-24 03:39:21 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 03:39:24 --> REQ ----------->$2y$09$5NQl9xDYdIbEUIpx946W9uSLz
DEBUG - 2025-06-24 03:39:24 --> btdbFindBy ---> 
INFO - 2025-06-24 03:39:24 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 03:39:24 --> REQ ----------->$2y$09$5NQl9xDYdIbEUIpx946W9uSLz
DEBUG - 2025-06-24 03:39:24 --> btdbFindBy ---> 
INFO - 2025-06-24 03:39:24 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
WARNING - 2025-06-24 03:39:24 --> [DEPRECATED] strtolower(): Passing null to parameter #1 ($string) of type string is deprecated in APPPATH\Controllers\Api.php on line 1534.
 1 APPPATH\Controllers\Api.php(1534): strtolower(null)
 2 APPPATH\Controllers\Api.php(189): App\Controllers\Api->getSubscription()
 3 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('get-subscription')
 4 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 5 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 6 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 7 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-24 03:39:24 --> [DEPRECATED] strtolower(): Passing null to parameter #1 ($string) of type string is deprecated in APPPATH\Controllers\Api.php on line 1535.
 1 APPPATH\Controllers\Api.php(1535): strtolower(null)
 2 APPPATH\Controllers\Api.php(189): App\Controllers\Api->getSubscription()
 3 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('get-subscription')
 4 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 5 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 6 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 7 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-06-24 03:39:24 --> REQ ----------->$2y$09$5NQl9xDYdIbEUIpx946W9uSLz
INFO - 2025-06-24 03:39:24 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
WARNING - 2025-06-24 03:39:24 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('aiproStart', null, 1753328364, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(401): btflag_set('aiproStart', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-24 03:39:24 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('user_email', null, 1753328364, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(402): btflag_set('user_email', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-24 03:39:24 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('chatpro_KJLF4XgSL8wjlGm', null, 1753328364, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(403): btflag_set('chatpro_KJLF4XgSL8wjlGm', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-06-24 03:39:24 --> REQ ----------->$2y$09$5NQl9xDYdIbEUIpx946W9uSLz
INFO - 2025-06-24 03:39:25 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2025-06-24 03:41:12 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 03:41:12 --> btdbFindBy ---> 
DEBUG - 2025-06-24 03:41:12 --> btdbFindBy ---> SELECT `start-plan`.*, `start-account`.*, DATE_FORMAT(resumed_at , "%M %e, %Y") as "resumed_at_formatted", `start-user`.`mode`, case when DATE_ADD(end_date, INTERVAL 1 DAY) < now() then 'yes' else 'no' end as 'expired'
FROM `start-account`
JOIN `start-user` ON `start-user`.`user_id` = `start-account`.`user_id`
JOIN `start-plan` ON `start-plan`.`plan_id` = `start-account`.`plan_id`
WHERE `start-account`.`user_id` = '3'
ORDER BY `start-account`.`end_date` DESC
WARNING - 2025-06-24 03:41:12 --> [DEPRECATED] strtolower(): Passing null to parameter #1 ($string) of type string is deprecated in APPPATH\Controllers\User\Settings.php on line 163.
 1 APPPATH\Controllers\User\Settings.php(163): strtolower(null)
 2 APPPATH\Controllers\User\Settings.php(59): App\Controllers\User\Settings->get_subs_data()
 3 APPPATH\Controllers\User\Settings.php(45): App\Controllers\User\Settings->getDiscountPricing()
 4 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\User\Settings->index()
 5 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\User\Settings))
 6 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 7 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 8 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-24 03:41:12 --> [DEPRECATED] strtolower(): Passing null to parameter #1 ($string) of type string is deprecated in APPPATH\Controllers\User\Settings.php on line 164.
 1 APPPATH\Controllers\User\Settings.php(164): strtolower(null)
 2 APPPATH\Controllers\User\Settings.php(59): App\Controllers\User\Settings->get_subs_data()
 3 APPPATH\Controllers\User\Settings.php(45): App\Controllers\User\Settings->getDiscountPricing()
 4 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\User\Settings->index()
 5 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\User\Settings))
 6 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 7 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 8 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-24 03:41:12 --> [DEPRECATED] strtolower(): Passing null to parameter #1 ($string) of type string is deprecated in APPPATH\Controllers\User\Settings.php on line 87.
 1 APPPATH\Controllers\User\Settings.php(87): strtolower(null)
 2 APPPATH\Controllers\User\Settings.php(45): App\Controllers\User\Settings->getDiscountPricing()
 3 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\User\Settings->index()
 4 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\User\Settings))
 5 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 6 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 7 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-06-24 03:41:12 --> REQ ----------->$2y$09$5NQl9xDYdIbEUIpx946W9uSLz
INFO - 2025-06-24 03:41:12 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
WARNING - 2025-06-24 03:41:12 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('aiproStart', null, 1753328472, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(401): btflag_set('aiproStart', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-24 03:41:12 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('user_email', null, 1753328472, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(402): btflag_set('user_email', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-24 03:41:12 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('chatpro_KJLF4XgSL8wjlGm', null, 1753328472, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(403): btflag_set('chatpro_KJLF4XgSL8wjlGm', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-06-24 03:41:12 --> REQ ----------->
INFO - 2025-06-24 03:41:12 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 03:41:15 --> REQ ----------->$2y$09$5NQl9xDYdIbEUIpx946W9uSLz
DEBUG - 2025-06-24 03:41:15 --> btdbFindBy ---> 
INFO - 2025-06-24 03:41:15 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 03:41:15 --> REQ ----------->$2y$09$5NQl9xDYdIbEUIpx946W9uSLz
DEBUG - 2025-06-24 03:41:15 --> btdbFindBy ---> 
INFO - 2025-06-24 03:41:15 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
WARNING - 2025-06-24 03:41:15 --> [DEPRECATED] strtolower(): Passing null to parameter #1 ($string) of type string is deprecated in APPPATH\Controllers\Api.php on line 1534.
 1 APPPATH\Controllers\Api.php(1534): strtolower(null)
 2 APPPATH\Controllers\Api.php(189): App\Controllers\Api->getSubscription()
 3 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('get-subscription')
 4 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 5 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 6 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 7 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-24 03:41:15 --> [DEPRECATED] strtolower(): Passing null to parameter #1 ($string) of type string is deprecated in APPPATH\Controllers\Api.php on line 1535.
 1 APPPATH\Controllers\Api.php(1535): strtolower(null)
 2 APPPATH\Controllers\Api.php(189): App\Controllers\Api->getSubscription()
 3 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('get-subscription')
 4 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 5 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 6 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 7 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-06-24 03:41:15 --> REQ ----------->$2y$09$5NQl9xDYdIbEUIpx946W9uSLz
INFO - 2025-06-24 03:41:15 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
WARNING - 2025-06-24 03:41:15 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('aiproStart', null, 1753328475, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(401): btflag_set('aiproStart', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-24 03:41:15 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('user_email', null, 1753328475, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(402): btflag_set('user_email', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-24 03:41:15 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('chatpro_KJLF4XgSL8wjlGm', null, 1753328475, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(403): btflag_set('chatpro_KJLF4XgSL8wjlGm', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-06-24 03:41:16 --> REQ ----------->$2y$09$5NQl9xDYdIbEUIpx946W9uSLz
INFO - 2025-06-24 03:41:16 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 03:41:33 --> REQ ----------->$2y$09$5NQl9xDYdIbEUIpx946W9uSLz
INFO - 2025-06-24 03:41:33 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
WARNING - 2025-06-24 03:41:33 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('aiproStart', null, 1753328493, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(401): btflag_set('aiproStart', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-24 03:41:33 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('user_email', null, 1753328493, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(402): btflag_set('user_email', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-24 03:41:33 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('chatpro_KJLF4XgSL8wjlGm', null, 1753328493, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(403): btflag_set('chatpro_KJLF4XgSL8wjlGm', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-06-24 03:41:33 --> REQ ----------->
INFO - 2025-06-24 03:41:33 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2025-06-24 03:41:35 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 03:41:35 --> btdbFindBy ---> 
DEBUG - 2025-06-24 03:41:35 --> btdbFindBy ---> SELECT `start-plan`.*, `start-account`.*, DATE_FORMAT(resumed_at , "%M %e, %Y") as "resumed_at_formatted", `start-user`.`mode`, case when DATE_ADD(end_date, INTERVAL 1 DAY) < now() then 'yes' else 'no' end as 'expired'
FROM `start-account`
JOIN `start-user` ON `start-user`.`user_id` = `start-account`.`user_id`
JOIN `start-plan` ON `start-plan`.`plan_id` = `start-account`.`plan_id`
WHERE `start-account`.`user_id` = '3'
ORDER BY `start-account`.`end_date` DESC
WARNING - 2025-06-24 03:41:35 --> [DEPRECATED] strtolower(): Passing null to parameter #1 ($string) of type string is deprecated in APPPATH\Controllers\User\Settings.php on line 163.
 1 APPPATH\Controllers\User\Settings.php(163): strtolower(null)
 2 APPPATH\Controllers\User\Settings.php(59): App\Controllers\User\Settings->get_subs_data()
 3 APPPATH\Controllers\User\Settings.php(45): App\Controllers\User\Settings->getDiscountPricing()
 4 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\User\Settings->index()
 5 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\User\Settings))
 6 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 7 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 8 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-24 03:41:35 --> [DEPRECATED] strtolower(): Passing null to parameter #1 ($string) of type string is deprecated in APPPATH\Controllers\User\Settings.php on line 164.
 1 APPPATH\Controllers\User\Settings.php(164): strtolower(null)
 2 APPPATH\Controllers\User\Settings.php(59): App\Controllers\User\Settings->get_subs_data()
 3 APPPATH\Controllers\User\Settings.php(45): App\Controllers\User\Settings->getDiscountPricing()
 4 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\User\Settings->index()
 5 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\User\Settings))
 6 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 7 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 8 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-24 03:41:35 --> [DEPRECATED] strtolower(): Passing null to parameter #1 ($string) of type string is deprecated in APPPATH\Controllers\User\Settings.php on line 87.
 1 APPPATH\Controllers\User\Settings.php(87): strtolower(null)
 2 APPPATH\Controllers\User\Settings.php(45): App\Controllers\User\Settings->getDiscountPricing()
 3 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\User\Settings->index()
 4 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\User\Settings))
 5 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 6 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 7 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-06-24 03:41:36 --> REQ ----------->$2y$09$5NQl9xDYdIbEUIpx946W9uSLz
INFO - 2025-06-24 03:41:36 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
WARNING - 2025-06-24 03:41:36 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('aiproStart', null, 1753328496, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(401): btflag_set('aiproStart', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-24 03:41:36 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('user_email', null, 1753328496, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(402): btflag_set('user_email', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-24 03:41:36 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('chatpro_KJLF4XgSL8wjlGm', null, 1753328496, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(403): btflag_set('chatpro_KJLF4XgSL8wjlGm', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-06-24 03:41:36 --> REQ ----------->
INFO - 2025-06-24 03:41:36 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 03:41:38 --> REQ ----------->$2y$09$5NQl9xDYdIbEUIpx946W9uSLz
DEBUG - 2025-06-24 03:41:38 --> btdbFindBy ---> 
INFO - 2025-06-24 03:41:38 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 03:41:38 --> REQ ----------->$2y$09$5NQl9xDYdIbEUIpx946W9uSLz
DEBUG - 2025-06-24 03:41:38 --> btdbFindBy ---> 
INFO - 2025-06-24 03:41:38 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
WARNING - 2025-06-24 03:41:38 --> [DEPRECATED] strtolower(): Passing null to parameter #1 ($string) of type string is deprecated in APPPATH\Controllers\Api.php on line 1534.
 1 APPPATH\Controllers\Api.php(1534): strtolower(null)
 2 APPPATH\Controllers\Api.php(189): App\Controllers\Api->getSubscription()
 3 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('get-subscription')
 4 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 5 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 6 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 7 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-24 03:41:38 --> [DEPRECATED] strtolower(): Passing null to parameter #1 ($string) of type string is deprecated in APPPATH\Controllers\Api.php on line 1535.
 1 APPPATH\Controllers\Api.php(1535): strtolower(null)
 2 APPPATH\Controllers\Api.php(189): App\Controllers\Api->getSubscription()
 3 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('get-subscription')
 4 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 5 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 6 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 7 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-06-24 03:41:38 --> REQ ----------->$2y$09$5NQl9xDYdIbEUIpx946W9uSLz
INFO - 2025-06-24 03:41:38 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
WARNING - 2025-06-24 03:41:38 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('aiproStart', null, 1753328498, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(401): btflag_set('aiproStart', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-24 03:41:38 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('user_email', null, 1753328498, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(402): btflag_set('user_email', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-24 03:41:38 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('chatpro_KJLF4XgSL8wjlGm', null, 1753328498, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(403): btflag_set('chatpro_KJLF4XgSL8wjlGm', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-06-24 03:41:38 --> REQ ----------->$2y$09$5NQl9xDYdIbEUIpx946W9uSLz
INFO - 2025-06-24 03:41:38 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 03:42:02 --> REQ ----------->$2y$09$5NQl9xDYdIbEUIpx946W9uSLz
INFO - 2025-06-24 03:42:02 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
WARNING - 2025-06-24 03:42:02 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('aiproStart', null, 1753328522, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(401): btflag_set('aiproStart', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-24 03:42:02 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('user_email', null, 1753328522, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(402): btflag_set('user_email', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-24 03:42:02 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('chatpro_KJLF4XgSL8wjlGm', null, 1753328522, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(403): btflag_set('chatpro_KJLF4XgSL8wjlGm', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-06-24 03:42:02 --> REQ ----------->
INFO - 2025-06-24 03:42:02 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2025-06-24 03:42:04 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 03:42:04 --> btdbFindBy ---> 
DEBUG - 2025-06-24 03:42:04 --> btdbFindBy ---> SELECT `start-plan`.*, `start-account`.*, DATE_FORMAT(resumed_at , "%M %e, %Y") as "resumed_at_formatted", `start-user`.`mode`, case when DATE_ADD(end_date, INTERVAL 1 DAY) < now() then 'yes' else 'no' end as 'expired'
FROM `start-account`
JOIN `start-user` ON `start-user`.`user_id` = `start-account`.`user_id`
JOIN `start-plan` ON `start-plan`.`plan_id` = `start-account`.`plan_id`
WHERE `start-account`.`user_id` = '3'
ORDER BY `start-account`.`end_date` DESC
WARNING - 2025-06-24 03:42:04 --> [DEPRECATED] strtolower(): Passing null to parameter #1 ($string) of type string is deprecated in APPPATH\Controllers\User\Settings.php on line 163.
 1 APPPATH\Controllers\User\Settings.php(163): strtolower(null)
 2 APPPATH\Controllers\User\Settings.php(59): App\Controllers\User\Settings->get_subs_data()
 3 APPPATH\Controllers\User\Settings.php(45): App\Controllers\User\Settings->getDiscountPricing()
 4 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\User\Settings->index()
 5 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\User\Settings))
 6 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 7 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 8 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-24 03:42:04 --> [DEPRECATED] strtolower(): Passing null to parameter #1 ($string) of type string is deprecated in APPPATH\Controllers\User\Settings.php on line 164.
 1 APPPATH\Controllers\User\Settings.php(164): strtolower(null)
 2 APPPATH\Controllers\User\Settings.php(59): App\Controllers\User\Settings->get_subs_data()
 3 APPPATH\Controllers\User\Settings.php(45): App\Controllers\User\Settings->getDiscountPricing()
 4 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\User\Settings->index()
 5 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\User\Settings))
 6 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 7 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 8 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-24 03:42:04 --> [DEPRECATED] strtolower(): Passing null to parameter #1 ($string) of type string is deprecated in APPPATH\Controllers\User\Settings.php on line 87.
 1 APPPATH\Controllers\User\Settings.php(87): strtolower(null)
 2 APPPATH\Controllers\User\Settings.php(45): App\Controllers\User\Settings->getDiscountPricing()
 3 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\User\Settings->index()
 4 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\User\Settings))
 5 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 6 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 7 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-06-24 03:42:05 --> REQ ----------->$2y$09$5NQl9xDYdIbEUIpx946W9uSLz
INFO - 2025-06-24 03:42:05 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
WARNING - 2025-06-24 03:42:05 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('aiproStart', null, 1753328525, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(401): btflag_set('aiproStart', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-24 03:42:05 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('user_email', null, 1753328525, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(402): btflag_set('user_email', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-24 03:42:05 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('chatpro_KJLF4XgSL8wjlGm', null, 1753328525, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(403): btflag_set('chatpro_KJLF4XgSL8wjlGm', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-06-24 03:42:05 --> REQ ----------->
INFO - 2025-06-24 03:42:05 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 03:42:07 --> REQ ----------->$2y$09$5NQl9xDYdIbEUIpx946W9uSLz
DEBUG - 2025-06-24 03:42:07 --> btdbFindBy ---> 
INFO - 2025-06-24 03:42:08 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 03:42:08 --> REQ ----------->$2y$09$5NQl9xDYdIbEUIpx946W9uSLz
DEBUG - 2025-06-24 03:42:08 --> btdbFindBy ---> 
INFO - 2025-06-24 03:42:08 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
WARNING - 2025-06-24 03:42:08 --> [DEPRECATED] strtolower(): Passing null to parameter #1 ($string) of type string is deprecated in APPPATH\Controllers\Api.php on line 1534.
 1 APPPATH\Controllers\Api.php(1534): strtolower(null)
 2 APPPATH\Controllers\Api.php(189): App\Controllers\Api->getSubscription()
 3 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('get-subscription')
 4 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 5 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 6 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 7 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-24 03:42:08 --> [DEPRECATED] strtolower(): Passing null to parameter #1 ($string) of type string is deprecated in APPPATH\Controllers\Api.php on line 1535.
 1 APPPATH\Controllers\Api.php(1535): strtolower(null)
 2 APPPATH\Controllers\Api.php(189): App\Controllers\Api->getSubscription()
 3 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('get-subscription')
 4 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 5 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 6 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 7 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-06-24 03:42:08 --> REQ ----------->$2y$09$5NQl9xDYdIbEUIpx946W9uSLz
INFO - 2025-06-24 03:42:08 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
WARNING - 2025-06-24 03:42:08 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('aiproStart', null, 1753328528, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(401): btflag_set('aiproStart', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-24 03:42:08 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('user_email', null, 1753328528, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(402): btflag_set('user_email', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-24 03:42:08 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('chatpro_KJLF4XgSL8wjlGm', null, 1753328528, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(403): btflag_set('chatpro_KJLF4XgSL8wjlGm', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-06-24 03:42:08 --> REQ ----------->$2y$09$5NQl9xDYdIbEUIpx946W9uSLz
INFO - 2025-06-24 03:42:08 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 03:42:15 --> REQ ----------->$2y$09$5NQl9xDYdIbEUIpx946W9uSLz
INFO - 2025-06-24 03:42:15 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
WARNING - 2025-06-24 03:42:15 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('aiproStart', null, 1753328535, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(401): btflag_set('aiproStart', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-24 03:42:15 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('user_email', null, 1753328535, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(402): btflag_set('user_email', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-24 03:42:15 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('chatpro_KJLF4XgSL8wjlGm', null, 1753328535, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(403): btflag_set('chatpro_KJLF4XgSL8wjlGm', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-06-24 03:42:16 --> REQ ----------->
INFO - 2025-06-24 03:42:16 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 03:42:20 --> REQ ----------->$2y$09$5NQl9xDYdIbEUIpx946W9uSLz
INFO - 2025-06-24 03:42:20 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
WARNING - 2025-06-24 03:42:20 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('aiproStart', null, 1753328540, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(401): btflag_set('aiproStart', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-24 03:42:20 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('user_email', null, 1753328540, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(402): btflag_set('user_email', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-24 03:42:20 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('chatpro_KJLF4XgSL8wjlGm', null, 1753328540, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(403): btflag_set('chatpro_KJLF4XgSL8wjlGm', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-06-24 03:42:20 --> REQ ----------->
INFO - 2025-06-24 03:42:20 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2025-06-24 03:42:24 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 03:42:24 --> btdbFindBy ---> 
DEBUG - 2025-06-24 03:42:24 --> btdbFindBy ---> SELECT `start-plan`.*, `start-account`.*, DATE_FORMAT(resumed_at , "%M %e, %Y") as "resumed_at_formatted", `start-user`.`mode`, case when DATE_ADD(end_date, INTERVAL 1 DAY) < now() then 'yes' else 'no' end as 'expired'
FROM `start-account`
JOIN `start-user` ON `start-user`.`user_id` = `start-account`.`user_id`
JOIN `start-plan` ON `start-plan`.`plan_id` = `start-account`.`plan_id`
WHERE `start-account`.`user_id` = '3'
ORDER BY `start-account`.`end_date` DESC
WARNING - 2025-06-24 03:42:24 --> [DEPRECATED] strtolower(): Passing null to parameter #1 ($string) of type string is deprecated in APPPATH\Controllers\User\Settings.php on line 163.
 1 APPPATH\Controllers\User\Settings.php(163): strtolower(null)
 2 APPPATH\Controllers\User\Settings.php(59): App\Controllers\User\Settings->get_subs_data()
 3 APPPATH\Controllers\User\Settings.php(45): App\Controllers\User\Settings->getDiscountPricing()
 4 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\User\Settings->index()
 5 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\User\Settings))
 6 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 7 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 8 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-24 03:42:24 --> [DEPRECATED] strtolower(): Passing null to parameter #1 ($string) of type string is deprecated in APPPATH\Controllers\User\Settings.php on line 164.
 1 APPPATH\Controllers\User\Settings.php(164): strtolower(null)
 2 APPPATH\Controllers\User\Settings.php(59): App\Controllers\User\Settings->get_subs_data()
 3 APPPATH\Controllers\User\Settings.php(45): App\Controllers\User\Settings->getDiscountPricing()
 4 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\User\Settings->index()
 5 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\User\Settings))
 6 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 7 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 8 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-24 03:42:24 --> [DEPRECATED] strtolower(): Passing null to parameter #1 ($string) of type string is deprecated in APPPATH\Controllers\User\Settings.php on line 87.
 1 APPPATH\Controllers\User\Settings.php(87): strtolower(null)
 2 APPPATH\Controllers\User\Settings.php(45): App\Controllers\User\Settings->getDiscountPricing()
 3 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\User\Settings->index()
 4 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\User\Settings))
 5 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 6 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 7 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-06-24 03:42:24 --> REQ ----------->$2y$09$5NQl9xDYdIbEUIpx946W9uSLz
INFO - 2025-06-24 03:42:24 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
WARNING - 2025-06-24 03:42:24 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('aiproStart', null, 1753328544, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(401): btflag_set('aiproStart', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-24 03:42:24 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('user_email', null, 1753328544, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(402): btflag_set('user_email', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-24 03:42:24 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('chatpro_KJLF4XgSL8wjlGm', null, 1753328544, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(403): btflag_set('chatpro_KJLF4XgSL8wjlGm', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-06-24 03:42:25 --> REQ ----------->
INFO - 2025-06-24 03:42:25 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 03:42:26 --> REQ ----------->$2y$09$5NQl9xDYdIbEUIpx946W9uSLz
DEBUG - 2025-06-24 03:42:26 --> btdbFindBy ---> 
INFO - 2025-06-24 03:42:26 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 03:42:27 --> REQ ----------->$2y$09$5NQl9xDYdIbEUIpx946W9uSLz
DEBUG - 2025-06-24 03:42:27 --> btdbFindBy ---> 
INFO - 2025-06-24 03:42:27 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
WARNING - 2025-06-24 03:42:27 --> [DEPRECATED] strtolower(): Passing null to parameter #1 ($string) of type string is deprecated in APPPATH\Controllers\Api.php on line 1534.
 1 APPPATH\Controllers\Api.php(1534): strtolower(null)
 2 APPPATH\Controllers\Api.php(189): App\Controllers\Api->getSubscription()
 3 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('get-subscription')
 4 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 5 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 6 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 7 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-24 03:42:27 --> [DEPRECATED] strtolower(): Passing null to parameter #1 ($string) of type string is deprecated in APPPATH\Controllers\Api.php on line 1535.
 1 APPPATH\Controllers\Api.php(1535): strtolower(null)
 2 APPPATH\Controllers\Api.php(189): App\Controllers\Api->getSubscription()
 3 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('get-subscription')
 4 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 5 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 6 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 7 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-06-24 03:42:27 --> REQ ----------->$2y$09$5NQl9xDYdIbEUIpx946W9uSLz
INFO - 2025-06-24 03:42:27 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
WARNING - 2025-06-24 03:42:27 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('aiproStart', null, 1753328547, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(401): btflag_set('aiproStart', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-24 03:42:27 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('user_email', null, 1753328547, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(402): btflag_set('user_email', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-24 03:42:27 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('chatpro_KJLF4XgSL8wjlGm', null, 1753328547, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(403): btflag_set('chatpro_KJLF4XgSL8wjlGm', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-06-24 03:42:27 --> REQ ----------->$2y$09$5NQl9xDYdIbEUIpx946W9uSLz
INFO - 2025-06-24 03:42:27 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 05:14:48 --> REQ ----------->$2y$09$5NQl9xDYdIbEUIpx946W9uSLz
INFO - 2025-06-24 05:14:48 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
WARNING - 2025-06-24 05:14:48 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('aiproStart', null, 1753334088, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(401): btflag_set('aiproStart', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-24 05:14:48 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('user_email', null, 1753334088, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(402): btflag_set('user_email', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-24 05:14:48 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('chatpro_KJLF4XgSL8wjlGm', null, 1753334088, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(403): btflag_set('chatpro_KJLF4XgSL8wjlGm', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-06-24 05:14:49 --> REQ ----------->
INFO - 2025-06-24 05:14:49 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 05:15:06 --> REQ ----------->$2y$09$5NQl9xDYdIbEUIpx946W9uSLz
INFO - 2025-06-24 05:15:06 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
WARNING - 2025-06-24 05:15:06 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('aiproStart', null, 1753334106, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(401): btflag_set('aiproStart', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-24 05:15:06 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('user_email', null, 1753334106, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(402): btflag_set('user_email', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-24 05:15:06 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('chatpro_KJLF4XgSL8wjlGm', null, 1753334106, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(403): btflag_set('chatpro_KJLF4XgSL8wjlGm', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-06-24 05:15:07 --> REQ ----------->$2y$09$5NQl9xDYdIbEUIpx946W9uSLz
DEBUG - 2025-06-24 05:15:07 --> btdbFindBy ---> 
INFO - 2025-06-24 05:15:11 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 05:15:18 --> REQ ----------->
INFO - 2025-06-24 05:15:18 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 05:15:18 --> REQ ----------->
INFO - 2025-06-24 05:15:18 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 05:15:21 --> REQ ----------->
INFO - 2025-06-24 05:15:21 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 05:15:21 --> REQ ----------->
INFO - 2025-06-24 05:15:21 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 05:15:24 --> REQ ----------->test_gero_062025_3@mailinator.com123123
DEBUG - 2025-06-24 05:15:24 --> btdbFindBy ---> 
DEBUG - 2025-06-24 05:15:25 --> btdbFindBy ---> SELECT *
FROM `start-user`
WHERE `email` = '<EMAIL>'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
INFO - 2025-06-24 05:15:26 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 05:15:26 --> btdbFindBy ---> SELECT `start-plan`.*, `start-account`.*, DATE_FORMAT(resumed_at , "%M %e, %Y") as "resumed_at_formatted", `start-user`.`mode`, case when DATE_ADD(end_date, INTERVAL 1 DAY) < now() then 'yes' else 'no' end as 'expired'
FROM `start-account`
JOIN `start-user` ON `start-user`.`user_id` = `start-account`.`user_id`
JOIN `start-plan` ON `start-plan`.`plan_id` = `start-account`.`plan_id`
WHERE `start-account`.`user_id` = '55120'
ORDER BY `start-account`.`end_date` DESC
DEBUG - 2025-06-24 05:15:26 --> btdbFindBy ---> SELECT *
FROM `start-plan`
WHERE `plan_id` = '70'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-24 05:15:27 --> login-------> <EMAIL>
DEBUG - 2025-06-24 05:15:27 --> btdbFindBy ---> SELECT *
FROM `start-user_app`
WHERE `email` = '<EMAIL>'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
INFO - 2025-06-24 05:15:29 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 05:15:29 --> btdbFindBy ---> 
DEBUG - 2025-06-24 05:15:30 --> btdbFindBy ---> SELECT `start-plan`.*, `start-account`.*, DATE_FORMAT(resumed_at , "%M %e, %Y") as "resumed_at_formatted", `start-user`.`mode`, case when DATE_ADD(end_date, INTERVAL 1 DAY) < now() then 'yes' else 'no' end as 'expired'
FROM `start-account`
JOIN `start-user` ON `start-user`.`user_id` = `start-account`.`user_id`
JOIN `start-plan` ON `start-plan`.`plan_id` = `start-account`.`plan_id`
WHERE `start-account`.`user_id` = '55120'
ORDER BY `start-account`.`end_date` DESC
DEBUG - 2025-06-24 05:15:31 --> btdbFindBy ---> SELECT *
FROM `start-plan`
WHERE `plan_id` = '70'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-24 05:15:33 --> REQ ----------->$2y$09$EQN0p7mEiUqtnp2wAPjJ4.mEQn48j/UxxBZHH/rESTecC64JIMK9e
INFO - 2025-06-24 05:15:33 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
WARNING - 2025-06-24 05:15:33 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('aiproStart', null, **********, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(401): btflag_set('aiproStart', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-24 05:15:33 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('user_email', null, **********, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(402): btflag_set('user_email', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-24 05:15:33 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('chatpro_KJLF4XgSL8wjlGm', null, **********, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(403): btflag_set('chatpro_KJLF4XgSL8wjlGm', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-06-24 05:15:34 --> REQ ----------->
DEBUG - 2025-06-24 05:15:35 --> REQ ----------->
INFO - 2025-06-24 05:15:35 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 05:15:40 --> REQ ----------->$2y$09$EQN0p7mEiUqtnp2wAPjJ4.mEQn48j/UxxBZHH/rESTecC64JIMK9e
DEBUG - 2025-06-24 05:15:40 --> btdbFindBy ---> 
INFO - 2025-06-24 05:15:41 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2025-06-24 05:15:41 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 05:15:41 --> btdbFindBy ---> 
DEBUG - 2025-06-24 05:15:43 --> btdbFindBy ---> SELECT `start-plan`.*, `start-account`.*, DATE_FORMAT(resumed_at , "%M %e, %Y") as "resumed_at_formatted", `start-user`.`mode`, case when DATE_ADD(end_date, INTERVAL 1 DAY) < now() then 'yes' else 'no' end as 'expired'
FROM `start-account`
JOIN `start-user` ON `start-user`.`user_id` = `start-account`.`user_id`
JOIN `start-plan` ON `start-plan`.`plan_id` = `start-account`.`plan_id`
WHERE `start-account`.`user_id` = '55120'
ORDER BY `start-account`.`end_date` DESC
DEBUG - 2025-06-24 05:15:45 --> REQ ----------->$2y$09$EQN0p7mEiUqtnp2wAPjJ4.mEQn48j/UxxBZHH/rESTecC64JIMK9e
INFO - 2025-06-24 05:15:45 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
WARNING - 2025-06-24 05:15:45 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('aiproStart', null, **********, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(401): btflag_set('aiproStart', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-24 05:15:45 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('user_email', null, **********, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(402): btflag_set('user_email', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-24 05:15:45 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('chatpro_KJLF4XgSL8wjlGm', null, **********, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(403): btflag_set('chatpro_KJLF4XgSL8wjlGm', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-06-24 05:15:45 --> REQ ----------->
INFO - 2025-06-24 05:15:45 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 05:15:52 --> REQ ----------->$2y$09$EQN0p7mEiUqtnp2wAPjJ4.mEQn48j/UxxBZHH/rESTecC64JIMK9e
DEBUG - 2025-06-24 05:15:52 --> btdbFindBy ---> 
INFO - 2025-06-24 05:15:53 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 05:15:53 --> REQ ----------->$2y$09$EQN0p7mEiUqtnp2wAPjJ4.mEQn48j/UxxBZHH/rESTecC64JIMK9e
DEBUG - 2025-06-24 05:15:53 --> btdbFindBy ---> 
INFO - 2025-06-24 05:15:55 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 05:15:55 --> REQ ----------->$2y$09$EQN0p7mEiUqtnp2wAPjJ4.mEQn48j/UxxBZHH/rESTecC64JIMK9e
INFO - 2025-06-24 05:15:55 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
WARNING - 2025-06-24 05:15:55 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('aiproStart', null, 1753334155, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(401): btflag_set('aiproStart', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-24 05:15:55 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('user_email', null, 1753334155, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(402): btflag_set('user_email', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-24 05:15:55 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('chatpro_KJLF4XgSL8wjlGm', null, 1753334155, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(403): btflag_set('chatpro_KJLF4XgSL8wjlGm', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-06-24 05:15:55 --> REQ ----------->$2y$09$EQN0p7mEiUqtnp2wAPjJ4.mEQn48j/UxxBZHH/rESTecC64JIMK9e
INFO - 2025-06-24 05:15:55 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 05:17:04 --> REQ ----------->$2y$09$EQN0p7mEiUqtnp2wAPjJ4.mEQn48j/UxxBZHH/rESTecC64JIMK9e
INFO - 2025-06-24 05:17:04 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
WARNING - 2025-06-24 05:17:04 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('aiproStart', null, 1753334224, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(405): btflag_set('aiproStart', null, [...])
 3 APPPATH\Controllers\Api.php(795): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-24 05:17:04 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('user_email', null, 1753334224, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(406): btflag_set('user_email', null, [...])
 3 APPPATH\Controllers\Api.php(795): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-24 05:17:04 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('chatpro_KJLF4XgSL8wjlGm', null, 1753334224, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(407): btflag_set('chatpro_KJLF4XgSL8wjlGm', null, [...])
 3 APPPATH\Controllers\Api.php(795): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-06-24 05:17:04 --> REQ ----------->
INFO - 2025-06-24 05:17:04 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 05:17:16 --> REQ ----------->$2y$09$EQN0p7mEiUqtnp2wAPjJ4.mEQn48j/UxxBZHH/rESTecC64JIMK9e
INFO - 2025-06-24 05:17:16 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
WARNING - 2025-06-24 05:17:16 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('aiproStart', null, 1753334236, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(405): btflag_set('aiproStart', null, [...])
 3 APPPATH\Controllers\Api.php(795): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-24 05:17:16 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('user_email', null, 1753334236, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(406): btflag_set('user_email', null, [...])
 3 APPPATH\Controllers\Api.php(795): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-24 05:17:16 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('chatpro_KJLF4XgSL8wjlGm', null, 1753334236, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(407): btflag_set('chatpro_KJLF4XgSL8wjlGm', null, [...])
 3 APPPATH\Controllers\Api.php(795): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-06-24 05:17:16 --> REQ ----------->
INFO - 2025-06-24 05:17:16 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 05:17:26 --> REQ ----------->$2y$09$EQN0p7mEiUqtnp2wAPjJ4.mEQn48j/UxxBZHH/rESTecC64JIMK9e
INFO - 2025-06-24 05:17:26 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
WARNING - 2025-06-24 05:17:26 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('aiproStart', null, 1753334246, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(405): btflag_set('aiproStart', null, [...])
 3 APPPATH\Controllers\Api.php(795): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-24 05:17:26 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('user_email', null, 1753334246, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(406): btflag_set('user_email', null, [...])
 3 APPPATH\Controllers\Api.php(795): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-24 05:17:26 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('chatpro_KJLF4XgSL8wjlGm', null, 1753334246, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(407): btflag_set('chatpro_KJLF4XgSL8wjlGm', null, [...])
 3 APPPATH\Controllers\Api.php(795): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-06-24 05:17:26 --> REQ ----------->
INFO - 2025-06-24 05:17:26 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
