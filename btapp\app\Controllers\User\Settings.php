<?php

namespace App\Controllers\User;

use App\Controllers\BaseController;
use App\Models\AccountModel;
use App\Models\PlanModel;

class Settings extends BaseController
{
    private $theme = 'arcana';
    private $themeSlug = '';
    private $themePageVersion = 'v1';

    private $pageSlug = '';
    private $themeData = [];
    private $date_now;


    //-------------------------------------------------------------------
    //  public
    //-------------------------------------------------------------------

    public function __construct()
    {
        // parent::__construct();
        $this->date_now = date('Y-m-d H:i:s', time());
    }

    public function index()
    {
        if(!btsessionIsUserLoggedIn()) {
            header("Location: " . base_url('login'));
            die;
        }
        $user = btdbFindBy('UserModel', 'user_id', btsessionGET("USER")->user_id);
        if( $user['success'] && $user['res'] && $user['res'][0]->login_token ) btsessionSetAccount($user['res'][0], 1);
        else $this->destroySession();
        $this->theme = btflag('theme', $this->theme);
        $uri = current_url(true);
        $this->pageSlug = (($uri->getSegment(2))) ? $uri->getSegment(2): PAGE_MIXPANELNAME_DEFAULT;

        $this->themeData = [
            'active_tab' => isset($_GET['tab']) && $_GET['tab'] === 'mem' ? $_GET['tab'] : '',
            'offer' => $this->getDiscountPricing()
        ];
        
        $this->theme_data();
        $this->theme_pageVersion();

        switch ($this->theme) {
            case 'arcana':
            default:
                $this->theme_arcana();
        }
    }

    private function getDiscountPricing() {
        $subs_data = $this->get_subs_data();
        $currency = '';
        $plan_type = '';
        $payment_interval = '';
        $days_count = '';
        $plan_id = '';
        $display_txt1 = '';
        $plan_type = '';
        $price = ''; 
        $discount_name = '';
        $percent_discount = '25';
        $cancel_now = 'no';

        
        if (btsessionHas("PLAN") && btsessionGET("PLAN")->plan_pid!=='') {
            $plan_pid = btsessionGET("PLAN")->plan_pid;
            $pos = strpos($plan_pid, 'dis-');
            if ($pos !== false) {
                $cancel_now = 'yes';
            }
        }
        
        if (isset($subs_data[0])){
            $currency = strtolower($subs_data[0]->currency);
            $plan_type = strtolower($subs_data[0]->plan_type);
            $payment_interval = strtolower($subs_data[0]->payment_interval);
            $days_count = strtolower($subs_data[0]->days_count);
            $price = $subs_data[0]->price;
            $trial_days = strtolower($subs_data[0]->trial_days);
            $trial_prefix = '';

            if ($days_count>60) {
                $percent_discount = '45';
            }

            if ($trial_days!='' && $trial_days>0){
                $trial_prefix = 'trial-';
            }

            $discount_name = 'dis-'.$trial_prefix.$plan_type.'-'.$payment_interval.'-'.$currency.'-'.$percent_discount;

            //no discount for basic less than 10 dollars
            if ($price<10 && $plan_type == 'basic') {
                return [
                    'cancel'=>'yes',
                    'display_txt1' => '',
                    'plan_id' => '',   
                    'price' => '',   
                    'currency' => '',   
                    'plan_type' => '',   
                    'percentage' => ''   
                ]; 
            }

            $pos = strpos($discount_name, 'dis-trial-basic');
            if ($pos !== false) {
                $discount_name = str_replace('dis-trial-basic', 'dis-basic', $discount_name);
            }            

            if ($plan_type=='pro' && $price == 23.85 && ($trial_days=='' || $trial_days==0)) {
                $pos = strpos($discount_name, 'dis-pro');
                if ($pos !== false) {
                    $discount_name = str_replace('dis-pro', 'dis-trial-pro', $discount_name);
                }            
            }

            $planModel = new PlanModel();
            $plan = $planModel->getDiscountPricing($discount_name);
            $plan =  $plan['res'];

            $plan_id = isset($plan[0]) ? $plan[0]->plan_id : '';
            $display_txt1 = isset($plan[0]) ? $plan[0]->display_txt1 : '';
            $price = isset($plan[0]) ? $plan[0]->price : '';
            $currency = isset($plan[0]) ? $plan[0]->currency : '';
            $plan_type = isset($plan[0]) ? $plan[0]->plan_type : '';
        }

        if ($percent_discount=='45') {
            $percent_discount = '40';
        }

        return [
                'cancel'=>$cancel_now,
                'dis' => $discount_name,
                'display_txt1' => $display_txt1,
                'plan_id' => $plan_id,   
                'price' => $price,   
                'currency' => $currency,   
                'plan_type' => $plan_type,   
                'percentage' => $percent_discount   
        ]; 
    }

    private function get_subs_data() {
        $acct_user = [];

        if(btsessionIsUserLoggedIn()) {
            $accountModel = new AccountModel();
            $account = $accountModel->getSubscriptionForDiscount(btsessionGET("USER")->user_id);
    
            if($account['success'] && $account['res']) {
                $acct_user = $account['res'];
                foreach($acct_user as $key => $acct){
                    $plan_type = strtolower($acct_user[$key]->plan_type);
                    $price_per_member = strtolower($acct_user[$key]->price_per_member);
                    $trial_days = strtolower($acct_user[$key]->trial_days);
                    $start_date = strtolower($acct_user[$key]->start_date);
    
                    if ($plan_type=='enterprise'){ 
                        $members = $acct_user[$key]->members;
                        $base_price = $acct_user[$key]->price;
                        $payment_interval = strtolower($acct_user[$key]->payment_interval);
                        if ($members>9){
                            $price = $base_price+(($members-9)*$price_per_member);
                            $acct_user[$key]->price = $price;
                            if ($payment_interval=='monthly'){
                                $acct_user[$key]->price_label = '$'.number_format($price).'/MONTH';
                            }else{
                                $acct_user[$key]->price_label = '$'.number_format($price).'/YEAR';
                            }
                        }
                    }
    
                    $acct_user[$key]->is_trial_end = 'yes';
                    if ($trial_days!==null&&$trial_days!==''&&$trial_days>0){
                        $trial_end = date('Y-m-d', strtotime($start_date. ' + '.$trial_days.' days'));
                        $acct_user[$key]->is_trial_end = 'no';
                        if ($trial_end < $this->date_now){
                            $acct_user[$key]->is_trial_end = 'yes';
                        }
                    }
                }
            }    
        }
        return $acct_user;
    }

    public function changeCard() {
        $pid = $this->request->getGet('pid');
        if(!$pid) {
            header("Location: " . base_url("404"));
            die;
        }

        if(isset($req['tk'])) $user = $this->getUserByToken($req['tk']);
        if(btsessionIsUserLoggedIn()) {
            $user_id = btsessionGet('USER')->user_id;
        } else if($user && $user['success']) {
            $user = $user['data'];
            $user_id = $user->user_id;
        } else {
            header("Location: " . base_url("login"));
            die;
        }
        $acct = btdbFindBy('AccountModel', ['user_id', 'account_pid'], [$user_id, $pid]);
        if(!$acct['success'] || !$acct['res']) {
            header("Location: " . base_url("404"));
            die;
        }

        $this->index();
    }

    //-------------------------------------------------------------------
    //  protected
    //-------------------------------------------------------------------


    //-------------------------------------------------------------------
    //  private
    //-------------------------------------------------------------------

    private function theme_arcana()
    {
        $viewData = [
            'BASE' => $this->themeBaseData,
            'PAGE' => $this->themePageData,
            'DATA' => json_encode($this->themeData)
        ];

        $twigFile = $this->themeSlug;
        // do not edit this
        $this->bttwig->view("/theme_arcana/index_{$this->themePageVersion}.twig", $viewData);
    }

    private function theme_data()
    {
        $this->themePageData = [
            'include_session' => [
                'ctx' => base_url(),
            ],
            'include_twitter' => true,
            'page_title' => 'AI-Pro',
            'meta_data' => [
                'description' => '',
                // 'robots' => 'noindex, follow', //uncomment and change as needed
            ],
            'include_vwo' => btutilIsVwoOn(),
            'include_fbmeta' => true,
            'include_tiktok' => true,
            'include_quora' => true,
            'include_mixpanel' => [
                'debug' => CONFIG_MIXPANEL_DEBUG,
                'mixpanel_name' => $this->pageSlug,
                'keyword' => btflag('keyword', ''),
                'emailid' => btflag('emailid', ''),
                'adid' => btflag('adid', ''),
                'ppg' => btflag('ppg', FLAG_PPG_DEFAULT),
                'pmt' => btflag('pmt', FLAG_PMT_DEFAULT),
                'email' => btflag_cookie('user_email', ''),
                'user_plan' => btflag_cookie('user_plan', ''),
                'howdoiplantouse' => btflag_cookie('howdoiplantouse', ''),
                'remakemedloption' => btflag_cookie('remakemedloption', ''),
            ],
            'include_gtag_AW' => false,
            'include_gtag_GA4' => true,
            'include_bing' => true,
            'include_fullstory' => [
                'email' => btflag_cookie('user_email', ''),
            ],
        ];

        if (btflag_cookie('admin','0')=='1'){
            unset($this->themePageData['include_fullstory']);
            unset($this->themePageData['include_mixpanel']);        
        }

    }

    private function theme_pageVersion()
    {
        // not yet really used
        $this->themePageVersion = btflag_get('v', $this->themePageVersion);
    }
}
