<?php

namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;

class AlterNegativeTable20250522 extends Migration
{

    private $table = "negative";

    public function up()
    {
        if ($this->db->fieldExists('ip_address', $this->table)) return;
        $this->db->disableForeignKeyChecks();

        $fields = [
            'ip_address' => [
                'type' => 'VARCHAR',
                'constraint' => 100,
                'null' => true,
                'default' => '',
                'after' => 'cc_number_encrypted'
            ]
        ];
        $this->forge->addColumn($this->table, $fields);

        $this->db->enableForeignKeyChecks();

    }

    public function down()
    {
        //
    }
}
