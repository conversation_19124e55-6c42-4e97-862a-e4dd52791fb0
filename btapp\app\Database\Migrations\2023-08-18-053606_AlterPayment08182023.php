<?php

namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;

class AlterPayment08182023 extends Migration
{
    private $table = "payment";

    public function up()
    {
        $this->db->disableForeignKeyChecks();

        if (!$this->db->fieldExists('cc_number', $this->table)) {
            $fields = [
                'cc_number' => [
                    'type' => 'VARCHAR',
                    'constraint' => 100,
                    'null' => true,
                    'default' => '',
                    'after' => 'mode'
                ]
            ];
            $this->forge->addColumn($this->table, $fields);
        }
        $this->db->enableForeignKeyChecks();


    }

    public function down()
    {
        //
    }
}
