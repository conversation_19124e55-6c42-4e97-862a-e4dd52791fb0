<?php

namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;

class AlterAccountTable20240523 extends Migration
{
    private $table = "account";

    public function up()
    {
        if ($this->db->fieldExists('resumed_at', $this->table)) return;
        $this->db->disableForeignKeyChecks();

        $fields = [
            'resumed_at' => [
				'type' => 'DATETIME',
                'null' => true,
                'after' => 'deleted_at'
            ]
        ];
        $this->forge->addColumn($this->table, $fields);

        $this->db->enableForeignKeyChecks();
    }

    public function down()
    {
        //
    }
}
