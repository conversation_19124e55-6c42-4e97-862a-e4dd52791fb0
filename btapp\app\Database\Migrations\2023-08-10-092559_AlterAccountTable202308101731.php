<?php

namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;

class AlterAccountTable202308101731 extends Migration
{
    private $table = "account";

    public function up()
    {
        $this->db->disableForeignKeyChecks();
        if (!$this->db->fieldExists('cc_number', $this->table)) {
            $fields = [
                'cc_number' => [
                    'type' => 'VARCHAR',
                    'constraint' => 100,
                    'null' => true,
                    'default' => '',
                    'after' => 'plan_id'
                ]
            ];
            $this->forge->addColumn($this->table, $fields);
        }

        if (!$this->db->fieldExists('cc_number_encrypted', $this->table)) {
            $fields = [
                'cc_number_encrypted' => [
                    'type' => 'VARCHAR',
                    'constraint' => 100,
                    'null' => true,
                    'default' => '',
                    'after' => 'plan_id'
                ]
            ];
            $this->forge->addColumn($this->table, $fields);
        }
        $this->db->enableForeignKeyChecks();
    }

    public function down()
    {
        //
    }
}
