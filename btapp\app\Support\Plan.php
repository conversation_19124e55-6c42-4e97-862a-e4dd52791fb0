<?php

namespace App\Support;

use App\Enums\MaxTokens;
use App\Enums\PlanType;
use DateTime;

class Plan
{
    private PlanType $plan_type;

    public function __construct(private array $data)
    {
        $this->plan_type = PlanType::from($data['plan_type']);
    }

    public function get(): array
    {
        $data = $this->data;
        $date = new DateTime('now');
        $date_now = $date->format('Y-m-d H:i:s');
        $locales = $data['locales'];

        if (!is_null($locales)) {
            $locales = json_encode(array_flip($locales));
        }

        return array_merge($data, [
            'stripe_plan_id' => $this->getStripePlanId($data['stripe_plan_id']),
            'max_tokens' => $this->getMaxTokens($data['max_tokens']),
            'locales' => $locales,
            'created_at' => $date_now,
            'updated_at' => $date_now,
        ]);
    }

    public function getStripePlanId(string|array|null $stripe_plan_id = null): string
    {
        if (is_null($stripe_plan_id)) {
            $stripe_plan_id = match ($this->plan_type) {
                PlanType::Basic => StripePlanId::basic(),
                PlanType::Pro => StripePlanId::pro(),
                PlanType::ProMax => StripePlanId::proMax(),
                PlanType::Enterprise => StripePlanId::enterprise(),
                PlanType::Advanced => StripePlanId::advanced(),
                default => null,
            };
        }

        return is_array($stripe_plan_id) ? json_encode($stripe_plan_id) : $stripe_plan_id;
    }

    public function getMaxTokens(int|MaxTokens|null $max_tokens = null): int
    {
        if (is_null($max_tokens)) {
            $max_tokens = match ($this->plan_type) {
                PlanType::Basic => MaxTokens::Basic,
                PlanType::Pro => MaxTokens::Pro,
                PlanType::ProMax => MaxTokens::ProMax,
                PlanType::Enterprise => MaxTokens::Enterprise,
                PlanType::Advanced => MaxTokens::Advanced,
                default => null,
            };
        }

        return $max_tokens instanceof MaxTokens ? $max_tokens->value : $max_tokens;
    }
}
