<?php

namespace App\Controllers\FlowPages;

use App\Controllers\BaseController;

class Authentication extends BaseController
{
    private $theme = FLAG_THEME_DEFAULT;
    private $themeSlug = ''; //not used
    private $themePageVersion = 'v1'; //this is used for twig filename
    private $themeData = [];

    private $pageSlug = '';

    //-------------------------------------------------------------------
    //  public
    //-------------------------------------------------------------------

    public function __construct()
    {
        // parent::__construct();
    }

    public function index()
    {
        $this->theme = btflag($this->encryptedFlag['theme'], $this->theme);
        $uri = current_url(true);
        $this->pageSlug = (($uri->getSegment(2))) ? $uri->getSegment(2): PAGE_MIXPANELNAME_DEFAULT;

        if( in_array($this->pageSlug, array('register'))
        && btsessionIsUserLoggedIn() ) {
            if( $this->theme === 'basilisk-02' ) {
                header("Location: " . base_url('free-chat'));
                die;
            } else if ( btflag($this->encryptedFlag['restrict_lp_redirection'], "") === 'on' && in_array(btflag('landingpage', ''), array(
                'start-chat-gpt', 'start-stable-diffusion', 'start-chatgpt-v2', 'start-chatgpt-go', 'text-to-image'
            ))) {
                header("Location: " . base_url(btflag('landingpage', '')));
                die;
            } else if (btflag('flow') == 'chatapp') {
                $redirectUrl = (btflag('flow') === 'chatapp') ? (getenv('CHATBOTPRO_URL') ?? 'https://chatpro.ai-pro.org') . '/chat/new' : null;

                if ($redirectUrl) {
                    header('Location: ' . $redirectUrl);
                    die;
                }
            }
        }

        $this->theme_data();
        // $this->theme_pageVersion();

        if(isset($_GET['rd']) && $_GET['rd'] === 'on' && !btsessionHas('stop-session')) {
            $this->themeData['stop-session'] = "1";
            btsessionSet('stop-session', '1');
        }

        switch ($this->theme) {
            case 'basilisk-03':
            case 'basilisk-02':
            case 'basilisk':
                $this->theme_basilisk();
                break;
            case 'druig':
                $this->theme_druig();
                break;
            case 'echo':
                $this->theme_echo();
                break;   
            case 'arcana':
            default:
                $this->theme_arcana();
        }
    }

    //-------------------------------------------------------------------
    //  protected
    //-------------------------------------------------------------------


    //-------------------------------------------------------------------
    //  private
    //-------------------------------------------------------------------

    private function theme_arcana()
    {
        $viewData = [
            'BASE' => $this->themeBaseData,
            'PAGE' => $this->themePageData,
            'DATA' => json_encode($this->themeData)
        ];

        $twigFile = $this->themeSlug;
        // do not edit this
        $this->bttwig->view("/theme_arcana/index_{$this->themePageVersion}.twig", $viewData);
    }

    private function theme_basilisk()
    {
        // basilisk has no custom LOGIN, FORGOT, RESET-PASS pages so will use the default theme
        switch($this->pageSlug) {
            case 'login':
            case 'forgot':
            case 'resetpass':
                $this->theme_arcana();
                return;
        }

        $viewData = [
            'BASE' => $this->themeBaseData,
            'PAGE' => $this->themePageData,
            'DATA' => json_encode($this->themeData)
        ];

        $twigFile = $this->themeSlug;
        // do not edit this
        $this->bttwig->view("/theme_basilisk/index_{$this->themePageVersion}.twig", $viewData);
    }

    private function theme_cerebro()
    {
        $viewData = [
            'BASE' => $this->themeBaseData,
            'PAGE' => $this->themePageData,
            'DATA' => json_encode($this->themeData)
        ];

        $twigFile = $this->themeSlug;
        // do not edit this
        $this->bttwig->view("/theme_cerebro/index_{$this->themePageVersion}.twig", $viewData);
    }
    
    private function theme_druig()
    {
        // druig has no custom LOGIN, FORGOT, RESET-PASS pages so will use the default theme
        switch($this->pageSlug) {
            case 'resetpass':
                $this->theme_arcana();
                return;
        }
    
        $viewData = [
            'BASE' => $this->themeBaseData,
            'PAGE' => $this->themePageData,
            'DATA' => json_encode($this->themeData)
        ];

        $twigFile = $this->themeSlug;
        // do not edit this
        $this->bttwig->view("/theme_druig/index_{$this->themePageVersion}.twig", $viewData);
    }

    private function theme_echo()
    {
        // druig has no custom LOGIN, FORGOT, RESET-PASS pages so will use the default theme
        switch($this->pageSlug) {
            case 'resetpass':
            case 'login':
                $this->theme_arcana();
                return;
        }
    
        $viewData = [
            'BASE' => $this->themeBaseData,
            'PAGE' => $this->themePageData,
            'DATA' => json_encode($this->themeData)
        ];

        $twigFile = $this->themeSlug;
        // do not edit this
        $this->bttwig->view("/theme_echo/index_{$this->themePageVersion}.twig", $viewData);
    }
    
    private function theme_data()
    {
        $this->themePageData = [
            'include_session' => [
                'ctx' => base_url(),
            ],
            'include_twitter' => true,
            'page_title' => 'AI-Pro',
            'meta_data' => [
                'description' => '',
                // 'robots' => 'noindex, follow', //uncomment and change as needed
            ],
            'include_vwo' => btutilIsVwoOn(),
            'include_fbmeta' => true,
            'include_tiktok' => true,
            'include_quora' => true,
            'include_mixpanel' => [
                'debug' => CONFIG_MIXPANEL_DEBUG,
                'mixpanel_name' => $this->pageSlug,
                'keyword' => btflag('keyword', ''),
                'emailid' => btflag('emailid', ''),
                'adid' => btflag('adid', ''),
                'ppg' => btflag('ppg', FLAG_PPG_DEFAULT),
                'pmt' => btflag('pmt', FLAG_PMT_DEFAULT),
                'email' => btflag_cookie('user_email', ''),
                'user_plan' => btflag_cookie('user_plan', ''),
                'howdoiplantouse' => btflag_cookie('howdoiplantouse', ''),
                'remakemedloption' => btflag_cookie('remakemedloption', ''),
                'ctx' => base_url()
            ],
            'include_gtag_AW' => false,
            'include_gtag_GA4' => true,
            'include_bing' => false,
            'include_fullstory' => [
                'email' => btflag_cookie('user_email', ''),
            ],
        ];

        if (btflag_cookie('admin','0')=='1'){
            unset($this->themePageData['include_fullstory']);
            unset($this->themePageData['include_mixpanel']);
        }

    }

    private function theme_pageVersion()
    {
        // not yet really used
        $this->themePageVersion = btflag_get('v', $this->themePageVersion);
    }
}

