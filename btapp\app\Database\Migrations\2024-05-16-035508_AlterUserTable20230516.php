<?php

namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;

class AlterUserTable20230516 extends Migration
{

    private $table = "user";

    public function up()
    {

        if ($this->db->fieldExists('country', $this->table)) return;
        $this->db->disableForeignKeyChecks();

        $fields = [
            'country' => [
                'type' => 'VARCHAR',
                'constraint' => 100,
                'null' => true,
                'default' => '',
                'after' => 'ip_address'
            ]
        ];
        $this->forge->addColumn($this->table, $fields);

        $this->db->enableForeignKeyChecks();


    }

    public function down()
    {
        //
    }
}
