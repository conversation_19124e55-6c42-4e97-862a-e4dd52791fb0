<?php

/*
 | --------------------------------------------------------------------
 | App Namespace
 | --------------------------------------------------------------------
 |
 | This defines the default Namespace that is used throughout
 | CodeIgniter to refer to the Application directory. Change
 | this constant to change the namespace that all application
 | classes should use.
 |
 | NOTE: changing this will require manually modifying the
 | existing namespaces of App\* namespaced-classes.
 */
defined('APP_NAMESPACE') || define('APP_NAMESPACE', 'App');

/*
 | --------------------------------------------------------------------------
 | Composer Path
 | --------------------------------------------------------------------------
 |
 | The path that Composer's autoload file is expected to live. By default,
 | the vendor folder is in the Root directory, but you can customize that here.
 */
defined('COMPOSER_PATH') || define('COMPOSER_PATH', ROOTPATH . 'vendor/autoload.php');

/*
 |--------------------------------------------------------------------------
 | Timing Constants
 |--------------------------------------------------------------------------
 |
 | Provide simple ways to work with the myriad of PHP functions that
 | require information to be in seconds.
 */
defined('SECOND') || define('SECOND', 1);
defined('MINUTE') || define('MINUTE', 60);
defined('HOUR')   || define('HOUR', 3600);
defined('DAY')    || define('DAY', 86400);
defined('WEEK')   || define('WEEK', 604800);
defined('MONTH')  || define('MONTH', 2_592_000);
defined('YEAR')   || define('YEAR', 31_536_000);
defined('DECADE') || define('DECADE', 315_360_000);

/*
 | --------------------------------------------------------------------------
 | Exit Status Codes
 | --------------------------------------------------------------------------
 |
 | Used to indicate the conditions under which the script is exit()ing.
 | While there is no universal standard for error codes, there are some
 | broad conventions.  Three such conventions are mentioned below, for
 | those who wish to make use of them.  The CodeIgniter defaults were
 | chosen for the least overlap with these conventions, while still
 | leaving room for others to be defined in future versions and user
 | applications.
 |
 | The three main conventions used for determining exit status codes
 | are as follows:
 |
 |    Standard C/C++ Library (stdlibc):
 |       http://www.gnu.org/software/libc/manual/html_node/Exit-Status.html
 |       (This link also contains other GNU-specific conventions)
 |    BSD sysexits.h:
 |       http://www.gsp.com/cgi-bin/man.cgi?section=3&topic=sysexits
 |    Bash scripting:
 |       http://tldp.org/LDP/abs/html/exitcodes.html
 |
 */
defined('EXIT_SUCCESS')        || define('EXIT_SUCCESS', 0);        // no errors
defined('EXIT_ERROR')          || define('EXIT_ERROR', 1);          // generic error
defined('EXIT_CONFIG')         || define('EXIT_CONFIG', 3);         // configuration error
defined('EXIT_UNKNOWN_FILE')   || define('EXIT_UNKNOWN_FILE', 4);   // file not found
defined('EXIT_UNKNOWN_CLASS')  || define('EXIT_UNKNOWN_CLASS', 5);  // unknown class
defined('EXIT_UNKNOWN_METHOD') || define('EXIT_UNKNOWN_METHOD', 6); // unknown class member
defined('EXIT_USER_INPUT')     || define('EXIT_USER_INPUT', 7);     // invalid user input
defined('EXIT_DATABASE')       || define('EXIT_DATABASE', 8);       // database error
defined('EXIT__AUTO_MIN')      || define('EXIT__AUTO_MIN', 9);      // lowest automatically-assigned error code
defined('EXIT__AUTO_MAX')      || define('EXIT__AUTO_MAX', 125);    // highest automatically-assigned error code

/**
 * @deprecated Use \CodeIgniter\Events\Events::PRIORITY_LOW instead.
 */
define('EVENT_PRIORITY_LOW', 200);

/**
 * @deprecated Use \CodeIgniter\Events\Events::PRIORITY_NORMAL instead.
 */
define('EVENT_PRIORITY_NORMAL', 100);

/**
 * @deprecated Use \CodeIgniter\Events\Events::PRIORITY_HIGH instead.
 */
define('EVENT_PRIORITY_HIGH', 10);

/*
 | --------------------------------------------------------------------------
 | CUSTOM
 | --------------------------------------------------------------------------
 */

/**
 * EMAIL
 */
defined('BTEMAIL_SUPPORT') ||
    define('BTEMAIL_SUPPORT', '<EMAIL>');
defined('BTEMAIL_SUPPORTNAME') || define('BTEMAIL_SUPPORTNAME', 'AI PRO');
defined('BTEMAIL_NOREPLY') ||
    define('BTEMAIL_NOREPLY', '<EMAIL>');
defined('BTEMAIL_NOREPLYNAME') || define('BTEMAIL_NOREPLYNAME', 'AI PRO');
defined('BTEMAIL_BCC') || define('BTEMAIL_BCC', '');
defined('BTEMAIL_ALERT') || define('BTEMAIL_ALERT', '<EMAIL>');


/**
 * FLAG
 */
defined('FLAG_MODE_LIVE') || define('FLAG_MODE_LIVE', 'live');
defined('FLAG_MODE_TEST') || define('FLAG_MODE_TEST', 'test');
defined('FLAG_PPG_DEFAULT') || define('FLAG_PPG_DEFAULT', '14');
defined('FLAG_PP_CTACLR') || define('FLAG_PP_CTACLR', '1559ED');
defined('FLAG_PMT_DEFAULT') || define('FLAG_PMT_DEFAULT', 'rec');
defined('FLAG_THEME_DEFAULT') || define('FLAG_THEME_DEFAULT', 'arcana');
defined('FLAG_DYNAMIC_PROMPT') || define('FLAG_DYNAMIC_PROMPT', 'wjuj3jZXZ');

/**
 * MixPanel
 */
defined('CONFIG_MIXPANEL_DEBUG') || define('CONFIG_MIXPANEL_DEBUG', 0);
defined('PAGE_MIXPANELNAME_DEFAULT') || define('PAGE_MIXPANELNAME_DEFAULT', 'home');

defined('PPG40') || define('PPG40', '15,16,19,20');
defined('PPG48') || define('PPG48', '15,16,19,20');
defined('PPG58') || define('PPG58', '86, 73');
defined('PPG59') || define('PPG59', '72, 73, 105');
defined('PPGBASIC') || define('PPGBASIC', '15, 19');

define('THEME_CEREBRO_PPG', 51);

defined('BASICPRO_LOGIC_STARTDATE') || define('BASICPRO_LOGIC_STARTDATE', '2023-07-31 04:00:00');
