<?php

namespace App\Support;

class StripePlanId
{
    private static array $product_ids = [
        'basic' => [
            'test'  => 'prod_OCUWwiEMO9bZaP',
            'live'  => 'prod_OCUUZxq2eSLN5r',
        ],
        'pro' => [
            'test'  => 'prod_OCUWcWHZP8odSH',
            'live'  => 'prod_OCUU8CMKNuLKkL',
        ],
        'pro_max' => [
            'test'  => 'prod_PViLUBjm5K8FTP',
            'live'  => 'prod_PViKe9XmZ2L4aW',
        ],
        'pro_max_yearly' => [
            'test'  => 'prod_PW9wo6QkI7dZ4f',
            'live'  => 'prod_PW9v1uQ1SVYOXU',
        ],
        'advanced' => [
            'test'  => 'prod_RHOCsUFp9Nbw8V',
            'live'  => 'prod_RHOF8gCsccsRPx',
        ],
    ];

    public static function __callStatic(string $name, $arguments)
    {
        $name = strtolower(preg_replace('/([a-z])([A-Z])/', '$1_$2', $name));

        if (!array_key_exists($name, self::$product_ids)) {
            throw new \InvalidArgumentException("Invalid plan type: $name");
        }

        return self::$product_ids[$name];
    }

    public static function enterprise()
    {
        return self::$product_ids['pro'];
    }
}
