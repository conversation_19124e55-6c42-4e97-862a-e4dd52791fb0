<?php

namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;

class AlterUserTable2 extends Migration
{
    private $table = "user";

    public function up()
    {
        if ($this->db->fieldExists('ip_address', $this->table)) return;
        $this->db->disableForeignKeyChecks();

        $fields = [
            'ip_address' => [
                'type' => 'VARCHAR',
                'constraint' => 100,
                'null' => true,
                'default' => '',
                'before' => 'mode'
            ]
        ];
        $this->forge->addColumn($this->table, $fields);

        $this->db->enableForeignKeyChecks();

    }

    public function down()
    {
        //
    }
}
