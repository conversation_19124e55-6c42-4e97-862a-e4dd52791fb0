<?php
/** Move this to START */

namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;
use CodeIgniter\Database\RawSql;

class CreateTblDeviceFingerprint extends Migration
{
    private $table = 'device_fingerprint';

    public function up()
    {
        $this->forge->addField([
            // 'id' => [
            //     'type' => 'INT',
            //     'constraint' => 11,
            //     'unsigned' => false,
            //     'auto_increment' => true,
            // ],
            'user_pid' => [
                'type' => 'VARCHAR',
                'constraint' => 100,
                'null' => false,
                'comment' => 'UUID; Public ID; reference ID for external use such as in URL, storage folder/file name',
            ],
            'user_email' => [
                'type' => 'VARCHAR',
                'constraint' => 150,
                'null' => false
            ],
            'ip_address' => [
                'type' => 'VARCHAR',
                'constraint' => 200,
                'null' => false
            ],
            'canvas_id' => [
                'type' => 'VARCHAR',
                'constraint' => 200,
                'null' => false
            ],
            'canvas_id_desc' => [
                'type' => 'VARCHAR',
                'constraint' => 200,
                'null' => false
            ],
            // 'device_fp_pid' => [
            //     'type' => 'VARCHAR',
            //     'constraint' => 36,
            //     'null' => false,
            //     'comment' => 'UUID; Public ID; reference ID for external use such as in URL, storage folder/file name',
            // ],
            'event' => [
                'type' => 'VARCHAR',
                'constraint' => 50,
                'null' => false,
            ],
            'event_date' => [
                'type' => 'DATETIME',
            ],
            'build_version' => [
                'type' => 'VARCHAR',
                'constraint' => 150,
                'null' => true
            ],
            'fingerprint_combo1' => [
                'type' => 'VARCHAR',
                'constraint' => 200,
                'null' => false
            ],
            'fingerprint_combo1_desc' => [
                'type' => 'VARCHAR',
                'constraint' => 200,
                'null' => false
            ],
            'fingerprint_combo2' => [
                'type' => 'VARCHAR',
                'constraint' => 200,
                'null' => true
            ],
            'fingerprint_combo2_desc' => [
                'type' => 'VARCHAR',
                'constraint' => 200,
                'null' => true
            ],
            'http_headers_cookie' => [
                'type' => 'TEXT',
                'null' => false
            ],
            'fraudster' => [
                'type' => 'BIT',
                'default' => false
            ],
            'fraud_last_checked' => [
                'type' => 'DATETIME',
            ],
            'created_at' => [
                'type' => 'TIMESTAMP',
                'default' => new RawSql('CURRENT_TIMESTAMP'),
            ],
            'updated_at' => [
                'type' => 'TIMESTAMP',
                'null' => true,
                'default' => null,
            ],
            'deleted_at' => [
                'type' => 'TIMESTAMP',
                'null' => true,
                'default' => null,
                ]
        ]);
        $this->forge->addKey(['user_pid','user_email','ip_address','canvas_id','event'], true,true, 'device_fp');

        $this->forge->createTable($this->table);
    }

    public function down()
    {
        // TODO: always disable this function on deployment
        $this->forge->dropTable($this->table);
    }
}
