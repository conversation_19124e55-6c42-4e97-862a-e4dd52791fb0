DEBUG - 2025-06-12 00:06:13 --> REQ ----------->
INFO - 2025-06-12 00:06:14 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-12 00:11:16 --> REQ ----------->
INFO - 2025-06-12 00:11:16 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-12 00:16:05 --> REQ ----------->
INFO - 2025-06-12 00:16:05 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-12 00:18:34 --> REQ ----------->
INFO - 2025-06-12 00:18:34 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
ERROR - 2025-06-12 01:02:44 --> mysqli_sql_exception: Column count doesn't match value count at row 1 in C:\laragon\www\app.ai-pro.org\btapp\vendor\codeigniter4\framework\system\Database\MySQLi\Connection.php:295
Stack trace:
#0 C:\laragon\www\app.ai-pro.org\btapp\vendor\codeigniter4\framework\system\Database\MySQLi\Connection.php(295): mysqli->query('INSERT INTO `st...', 0)
#1 C:\laragon\www\app.ai-pro.org\btapp\vendor\codeigniter4\framework\system\Database\BaseConnection.php(692): CodeIgniter\Database\MySQLi\Connection->execute('INSERT INTO `st...')
#2 C:\laragon\www\app.ai-pro.org\btapp\vendor\codeigniter4\framework\system\Database\BaseConnection.php(606): CodeIgniter\Database\BaseConnection->simpleQuery('INSERT INTO `st...')
#3 C:\laragon\www\app.ai-pro.org\btapp\vendor\codeigniter4\framework\system\Database\BaseBuilder.php(1801): CodeIgniter\Database\BaseConnection->query('INSERT INTO `st...', NULL, false)
#4 C:\laragon\www\app.ai-pro.org\btapp\vendor\codeigniter4\framework\system\Database\BaseBuilder.php(2192): CodeIgniter\Database\BaseBuilder->batchExecute('_insertBatch', 100)
#5 C:\laragon\www\app.ai-pro.org\btapp\app\Database\Seeds\InsertPlanTableV2.php(6917): CodeIgniter\Database\BaseBuilder->insertBatch(Array)
#6 C:\laragon\www\app.ai-pro.org\btapp\vendor\codeigniter4\framework\system\Database\Seeder.php(146): App\Database\Seeds\InsertPlanTableV2->run()
#7 C:\laragon\www\app.ai-pro.org\btapp\vendor\codeigniter4\framework\system\Commands\Database\Seed.php(77): CodeIgniter\Database\Seeder->call('App\\Database\\Se...')
#8 C:\laragon\www\app.ai-pro.org\btapp\vendor\codeigniter4\framework\system\CLI\Commands.php(65): CodeIgniter\Commands\Database\Seed->run(Array)
#9 C:\laragon\www\app.ai-pro.org\btapp\vendor\codeigniter4\framework\system\CLI\Console.php(37): CodeIgniter\CLI\Commands->run('db:seed', Array)
#10 C:\laragon\www\app.ai-pro.org\btapp\spark(97): CodeIgniter\CLI\Console->run()
#11 {main}
ERROR - 2025-06-12 01:03:26 --> mysqli_sql_exception: Column count doesn't match value count at row 1 in C:\laragon\www\app.ai-pro.org\btapp\vendor\codeigniter4\framework\system\Database\MySQLi\Connection.php:295
Stack trace:
#0 C:\laragon\www\app.ai-pro.org\btapp\vendor\codeigniter4\framework\system\Database\MySQLi\Connection.php(295): mysqli->query('INSERT INTO `st...', 0)
#1 C:\laragon\www\app.ai-pro.org\btapp\vendor\codeigniter4\framework\system\Database\BaseConnection.php(692): CodeIgniter\Database\MySQLi\Connection->execute('INSERT INTO `st...')
#2 C:\laragon\www\app.ai-pro.org\btapp\vendor\codeigniter4\framework\system\Database\BaseConnection.php(606): CodeIgniter\Database\BaseConnection->simpleQuery('INSERT INTO `st...')
#3 C:\laragon\www\app.ai-pro.org\btapp\vendor\codeigniter4\framework\system\Database\BaseBuilder.php(1801): CodeIgniter\Database\BaseConnection->query('INSERT INTO `st...', NULL, false)
#4 C:\laragon\www\app.ai-pro.org\btapp\vendor\codeigniter4\framework\system\Database\BaseBuilder.php(2192): CodeIgniter\Database\BaseBuilder->batchExecute('_insertBatch', 100)
#5 C:\laragon\www\app.ai-pro.org\btapp\app\Database\Seeds\InsertPlanTableV2.php(6670): CodeIgniter\Database\BaseBuilder->insertBatch(Array)
#6 C:\laragon\www\app.ai-pro.org\btapp\vendor\codeigniter4\framework\system\Database\Seeder.php(146): App\Database\Seeds\InsertPlanTableV2->run()
#7 C:\laragon\www\app.ai-pro.org\btapp\vendor\codeigniter4\framework\system\Commands\Database\Seed.php(77): CodeIgniter\Database\Seeder->call('App\\Database\\Se...')
#8 C:\laragon\www\app.ai-pro.org\btapp\vendor\codeigniter4\framework\system\CLI\Commands.php(65): CodeIgniter\Commands\Database\Seed->run(Array)
#9 C:\laragon\www\app.ai-pro.org\btapp\vendor\codeigniter4\framework\system\CLI\Console.php(37): CodeIgniter\CLI\Commands->run('db:seed', Array)
#10 C:\laragon\www\app.ai-pro.org\btapp\spark(97): CodeIgniter\CLI\Console->run()
#11 {main}
ERROR - 2025-06-12 01:16:38 --> mysqli_sql_exception: Column count doesn't match value count at row 1 in C:\laragon\www\app.ai-pro.org\btapp\vendor\codeigniter4\framework\system\Database\MySQLi\Connection.php:295
Stack trace:
#0 C:\laragon\www\app.ai-pro.org\btapp\vendor\codeigniter4\framework\system\Database\MySQLi\Connection.php(295): mysqli->query('INSERT INTO `st...', 0)
#1 C:\laragon\www\app.ai-pro.org\btapp\vendor\codeigniter4\framework\system\Database\BaseConnection.php(692): CodeIgniter\Database\MySQLi\Connection->execute('INSERT INTO `st...')
#2 C:\laragon\www\app.ai-pro.org\btapp\vendor\codeigniter4\framework\system\Database\BaseConnection.php(606): CodeIgniter\Database\BaseConnection->simpleQuery('INSERT INTO `st...')
#3 C:\laragon\www\app.ai-pro.org\btapp\vendor\codeigniter4\framework\system\Database\BaseBuilder.php(1801): CodeIgniter\Database\BaseConnection->query('INSERT INTO `st...', NULL, false)
#4 C:\laragon\www\app.ai-pro.org\btapp\vendor\codeigniter4\framework\system\Database\BaseBuilder.php(2192): CodeIgniter\Database\BaseBuilder->batchExecute('_insertBatch', 100)
#5 C:\laragon\www\app.ai-pro.org\btapp\app\Database\Seeds\InsertPlanTableV2.php(6697): CodeIgniter\Database\BaseBuilder->insertBatch(Array)
#6 C:\laragon\www\app.ai-pro.org\btapp\vendor\codeigniter4\framework\system\Database\Seeder.php(146): App\Database\Seeds\InsertPlanTableV2->run()
#7 C:\laragon\www\app.ai-pro.org\btapp\vendor\codeigniter4\framework\system\Commands\Database\Seed.php(77): CodeIgniter\Database\Seeder->call('App\\Database\\Se...')
#8 C:\laragon\www\app.ai-pro.org\btapp\vendor\codeigniter4\framework\system\CLI\Commands.php(65): CodeIgniter\Commands\Database\Seed->run(Array)
#9 C:\laragon\www\app.ai-pro.org\btapp\vendor\codeigniter4\framework\system\CLI\Console.php(37): CodeIgniter\CLI\Commands->run('db:seed', Array)
#10 C:\laragon\www\app.ai-pro.org\btapp\spark(97): CodeIgniter\CLI\Console->run()
#11 {main}
ERROR - 2025-06-12 01:31:39 --> mysqli_sql_exception: Column count doesn't match value count at row 1 in C:\laragon\www\app.ai-pro.org\btapp\vendor\codeigniter4\framework\system\Database\MySQLi\Connection.php:295
Stack trace:
#0 C:\laragon\www\app.ai-pro.org\btapp\vendor\codeigniter4\framework\system\Database\MySQLi\Connection.php(295): mysqli->query('INSERT INTO `st...', 0)
#1 C:\laragon\www\app.ai-pro.org\btapp\vendor\codeigniter4\framework\system\Database\BaseConnection.php(692): CodeIgniter\Database\MySQLi\Connection->execute('INSERT INTO `st...')
#2 C:\laragon\www\app.ai-pro.org\btapp\vendor\codeigniter4\framework\system\Database\BaseConnection.php(606): CodeIgniter\Database\BaseConnection->simpleQuery('INSERT INTO `st...')
#3 C:\laragon\www\app.ai-pro.org\btapp\vendor\codeigniter4\framework\system\Database\BaseBuilder.php(1801): CodeIgniter\Database\BaseConnection->query('INSERT INTO `st...', NULL, false)
#4 C:\laragon\www\app.ai-pro.org\btapp\vendor\codeigniter4\framework\system\Database\BaseBuilder.php(2192): CodeIgniter\Database\BaseBuilder->batchExecute('_insertBatch', 100)
#5 C:\laragon\www\app.ai-pro.org\btapp\app\Database\Seeds\InsertPlanTableV2.php(7508): CodeIgniter\Database\BaseBuilder->insertBatch(Array)
#6 C:\laragon\www\app.ai-pro.org\btapp\vendor\codeigniter4\framework\system\Database\Seeder.php(146): App\Database\Seeds\InsertPlanTableV2->run()
#7 C:\laragon\www\app.ai-pro.org\btapp\vendor\codeigniter4\framework\system\Commands\Database\Seed.php(77): CodeIgniter\Database\Seeder->call('App\\Database\\Se...')
#8 C:\laragon\www\app.ai-pro.org\btapp\vendor\codeigniter4\framework\system\CLI\Commands.php(65): CodeIgniter\Commands\Database\Seed->run(Array)
#9 C:\laragon\www\app.ai-pro.org\btapp\vendor\codeigniter4\framework\system\CLI\Console.php(37): CodeIgniter\CLI\Commands->run('db:seed', Array)
#10 C:\laragon\www\app.ai-pro.org\btapp\spark(97): CodeIgniter\CLI\Console->run()
#11 {main}
ERROR - 2025-06-12 01:32:50 --> mysqli_sql_exception: Column count doesn't match value count at row 1 in C:\laragon\www\app.ai-pro.org\btapp\vendor\codeigniter4\framework\system\Database\MySQLi\Connection.php:295
Stack trace:
#0 C:\laragon\www\app.ai-pro.org\btapp\vendor\codeigniter4\framework\system\Database\MySQLi\Connection.php(295): mysqli->query('INSERT INTO `st...', 0)
#1 C:\laragon\www\app.ai-pro.org\btapp\vendor\codeigniter4\framework\system\Database\BaseConnection.php(692): CodeIgniter\Database\MySQLi\Connection->execute('INSERT INTO `st...')
#2 C:\laragon\www\app.ai-pro.org\btapp\vendor\codeigniter4\framework\system\Database\BaseConnection.php(606): CodeIgniter\Database\BaseConnection->simpleQuery('INSERT INTO `st...')
#3 C:\laragon\www\app.ai-pro.org\btapp\vendor\codeigniter4\framework\system\Database\BaseBuilder.php(1801): CodeIgniter\Database\BaseConnection->query('INSERT INTO `st...', NULL, false)
#4 C:\laragon\www\app.ai-pro.org\btapp\vendor\codeigniter4\framework\system\Database\BaseBuilder.php(2192): CodeIgniter\Database\BaseBuilder->batchExecute('_insertBatch', 100)
#5 C:\laragon\www\app.ai-pro.org\btapp\app\Database\Seeds\InsertPlanTableV2.php(7508): CodeIgniter\Database\BaseBuilder->insertBatch(Array)
#6 C:\laragon\www\app.ai-pro.org\btapp\vendor\codeigniter4\framework\system\Database\Seeder.php(146): App\Database\Seeds\InsertPlanTableV2->run()
#7 C:\laragon\www\app.ai-pro.org\btapp\vendor\codeigniter4\framework\system\Commands\Database\Seed.php(77): CodeIgniter\Database\Seeder->call('App\\Database\\Se...')
#8 C:\laragon\www\app.ai-pro.org\btapp\vendor\codeigniter4\framework\system\CLI\Commands.php(65): CodeIgniter\Commands\Database\Seed->run(Array)
#9 C:\laragon\www\app.ai-pro.org\btapp\vendor\codeigniter4\framework\system\CLI\Console.php(37): CodeIgniter\CLI\Commands->run('db:seed', Array)
#10 C:\laragon\www\app.ai-pro.org\btapp\spark(97): CodeIgniter\CLI\Console->run()
#11 {main}
ERROR - 2025-06-12 01:38:24 --> mysqli_sql_exception: Column count doesn't match value count at row 1 in C:\laragon\www\app.ai-pro.org\btapp\vendor\codeigniter4\framework\system\Database\MySQLi\Connection.php:295
Stack trace:
#0 C:\laragon\www\app.ai-pro.org\btapp\vendor\codeigniter4\framework\system\Database\MySQLi\Connection.php(295): mysqli->query('INSERT INTO `st...', 0)
#1 C:\laragon\www\app.ai-pro.org\btapp\vendor\codeigniter4\framework\system\Database\BaseConnection.php(692): CodeIgniter\Database\MySQLi\Connection->execute('INSERT INTO `st...')
#2 C:\laragon\www\app.ai-pro.org\btapp\vendor\codeigniter4\framework\system\Database\BaseConnection.php(606): CodeIgniter\Database\BaseConnection->simpleQuery('INSERT INTO `st...')
#3 C:\laragon\www\app.ai-pro.org\btapp\vendor\codeigniter4\framework\system\Database\BaseBuilder.php(1801): CodeIgniter\Database\BaseConnection->query('INSERT INTO `st...', NULL, false)
#4 C:\laragon\www\app.ai-pro.org\btapp\vendor\codeigniter4\framework\system\Database\BaseBuilder.php(2192): CodeIgniter\Database\BaseBuilder->batchExecute('_insertBatch', 100)
#5 C:\laragon\www\app.ai-pro.org\btapp\app\Database\Seeds\InsertPlanTableV2.php(7568): CodeIgniter\Database\BaseBuilder->insertBatch(Array)
#6 C:\laragon\www\app.ai-pro.org\btapp\vendor\codeigniter4\framework\system\Database\Seeder.php(146): App\Database\Seeds\InsertPlanTableV2->run()
#7 C:\laragon\www\app.ai-pro.org\btapp\vendor\codeigniter4\framework\system\Commands\Database\Seed.php(77): CodeIgniter\Database\Seeder->call('App\\Database\\Se...')
#8 C:\laragon\www\app.ai-pro.org\btapp\vendor\codeigniter4\framework\system\CLI\Commands.php(65): CodeIgniter\Commands\Database\Seed->run(Array)
#9 C:\laragon\www\app.ai-pro.org\btapp\vendor\codeigniter4\framework\system\CLI\Console.php(37): CodeIgniter\CLI\Commands->run('db:seed', Array)
#10 C:\laragon\www\app.ai-pro.org\btapp\spark(97): CodeIgniter\CLI\Console->run()
#11 {main}
ERROR - 2025-06-12 01:38:39 --> mysqli_sql_exception: Column count doesn't match value count at row 1 in C:\laragon\www\app.ai-pro.org\btapp\vendor\codeigniter4\framework\system\Database\MySQLi\Connection.php:295
Stack trace:
#0 C:\laragon\www\app.ai-pro.org\btapp\vendor\codeigniter4\framework\system\Database\MySQLi\Connection.php(295): mysqli->query('INSERT INTO `st...', 0)
#1 C:\laragon\www\app.ai-pro.org\btapp\vendor\codeigniter4\framework\system\Database\BaseConnection.php(692): CodeIgniter\Database\MySQLi\Connection->execute('INSERT INTO `st...')
#2 C:\laragon\www\app.ai-pro.org\btapp\vendor\codeigniter4\framework\system\Database\BaseConnection.php(606): CodeIgniter\Database\BaseConnection->simpleQuery('INSERT INTO `st...')
#3 C:\laragon\www\app.ai-pro.org\btapp\vendor\codeigniter4\framework\system\Database\BaseBuilder.php(1801): CodeIgniter\Database\BaseConnection->query('INSERT INTO `st...', NULL, false)
#4 C:\laragon\www\app.ai-pro.org\btapp\vendor\codeigniter4\framework\system\Database\BaseBuilder.php(2192): CodeIgniter\Database\BaseBuilder->batchExecute('_insertBatch', 100)
#5 C:\laragon\www\app.ai-pro.org\btapp\app\Database\Seeds\InsertPlanTableV2.php(7568): CodeIgniter\Database\BaseBuilder->insertBatch(Array)
#6 C:\laragon\www\app.ai-pro.org\btapp\vendor\codeigniter4\framework\system\Database\Seeder.php(146): App\Database\Seeds\InsertPlanTableV2->run()
#7 C:\laragon\www\app.ai-pro.org\btapp\vendor\codeigniter4\framework\system\Commands\Database\Seed.php(77): CodeIgniter\Database\Seeder->call('App\\Database\\Se...')
#8 C:\laragon\www\app.ai-pro.org\btapp\vendor\codeigniter4\framework\system\CLI\Commands.php(65): CodeIgniter\Commands\Database\Seed->run(Array)
#9 C:\laragon\www\app.ai-pro.org\btapp\vendor\codeigniter4\framework\system\CLI\Console.php(37): CodeIgniter\CLI\Commands->run('db:seed', Array)
#10 C:\laragon\www\app.ai-pro.org\btapp\spark(97): CodeIgniter\CLI\Console->run()
#11 {main}
ERROR - 2025-06-12 06:51:55 --> mysqli_sql_exception: Column count doesn't match value count at row 1 in C:\laragon\www\app.ai-pro.org\btapp\vendor\codeigniter4\framework\system\Database\MySQLi\Connection.php:295
Stack trace:
#0 C:\laragon\www\app.ai-pro.org\btapp\vendor\codeigniter4\framework\system\Database\MySQLi\Connection.php(295): mysqli->query('INSERT INTO `st...', 0)
#1 C:\laragon\www\app.ai-pro.org\btapp\vendor\codeigniter4\framework\system\Database\BaseConnection.php(692): CodeIgniter\Database\MySQLi\Connection->execute('INSERT INTO `st...')
#2 C:\laragon\www\app.ai-pro.org\btapp\vendor\codeigniter4\framework\system\Database\BaseConnection.php(606): CodeIgniter\Database\BaseConnection->simpleQuery('INSERT INTO `st...')
#3 C:\laragon\www\app.ai-pro.org\btapp\vendor\codeigniter4\framework\system\Database\BaseBuilder.php(1801): CodeIgniter\Database\BaseConnection->query('INSERT INTO `st...', NULL, false)
#4 C:\laragon\www\app.ai-pro.org\btapp\vendor\codeigniter4\framework\system\Database\BaseBuilder.php(2192): CodeIgniter\Database\BaseBuilder->batchExecute('_insertBatch', 100)
#5 C:\laragon\www\app.ai-pro.org\btapp\app\Database\Seeds\InsertPlanTableV2.php(11325): CodeIgniter\Database\BaseBuilder->insertBatch(Array)
#6 C:\laragon\www\app.ai-pro.org\btapp\vendor\codeigniter4\framework\system\Database\Seeder.php(146): App\Database\Seeds\InsertPlanTableV2->run()
#7 C:\laragon\www\app.ai-pro.org\btapp\vendor\codeigniter4\framework\system\Commands\Database\Seed.php(77): CodeIgniter\Database\Seeder->call('App\\Database\\Se...')
#8 C:\laragon\www\app.ai-pro.org\btapp\vendor\codeigniter4\framework\system\CLI\Commands.php(65): CodeIgniter\Commands\Database\Seed->run(Array)
#9 C:\laragon\www\app.ai-pro.org\btapp\vendor\codeigniter4\framework\system\CLI\Console.php(37): CodeIgniter\CLI\Commands->run('db:seed', Array)
#10 C:\laragon\www\app.ai-pro.org\btapp\spark(97): CodeIgniter\CLI\Console->run()
#11 {main}
ERROR - 2025-06-12 06:54:36 --> mysqli_sql_exception: Column count doesn't match value count at row 1 in C:\laragon\www\app.ai-pro.org\btapp\vendor\codeigniter4\framework\system\Database\MySQLi\Connection.php:295
Stack trace:
#0 C:\laragon\www\app.ai-pro.org\btapp\vendor\codeigniter4\framework\system\Database\MySQLi\Connection.php(295): mysqli->query('INSERT INTO `st...', 0)
#1 C:\laragon\www\app.ai-pro.org\btapp\vendor\codeigniter4\framework\system\Database\BaseConnection.php(692): CodeIgniter\Database\MySQLi\Connection->execute('INSERT INTO `st...')
#2 C:\laragon\www\app.ai-pro.org\btapp\vendor\codeigniter4\framework\system\Database\BaseConnection.php(606): CodeIgniter\Database\BaseConnection->simpleQuery('INSERT INTO `st...')
#3 C:\laragon\www\app.ai-pro.org\btapp\vendor\codeigniter4\framework\system\Database\BaseBuilder.php(1801): CodeIgniter\Database\BaseConnection->query('INSERT INTO `st...', NULL, false)
#4 C:\laragon\www\app.ai-pro.org\btapp\vendor\codeigniter4\framework\system\Database\BaseBuilder.php(2192): CodeIgniter\Database\BaseBuilder->batchExecute('_insertBatch', 100)
#5 C:\laragon\www\app.ai-pro.org\btapp\app\Database\Seeds\InsertPlanTableV2.php(11325): CodeIgniter\Database\BaseBuilder->insertBatch(Array)
#6 C:\laragon\www\app.ai-pro.org\btapp\vendor\codeigniter4\framework\system\Database\Seeder.php(146): App\Database\Seeds\InsertPlanTableV2->run()
#7 C:\laragon\www\app.ai-pro.org\btapp\vendor\codeigniter4\framework\system\Commands\Database\Seed.php(77): CodeIgniter\Database\Seeder->call('App\\Database\\Se...')
#8 C:\laragon\www\app.ai-pro.org\btapp\vendor\codeigniter4\framework\system\CLI\Commands.php(65): CodeIgniter\Commands\Database\Seed->run(Array)
#9 C:\laragon\www\app.ai-pro.org\btapp\vendor\codeigniter4\framework\system\CLI\Console.php(37): CodeIgniter\CLI\Commands->run('db:seed', Array)
#10 C:\laragon\www\app.ai-pro.org\btapp\spark(97): CodeIgniter\CLI\Console->run()
#11 {main}
ERROR - 2025-06-12 06:55:17 --> mysqli_sql_exception: Column count doesn't match value count at row 1 in C:\laragon\www\app.ai-pro.org\btapp\vendor\codeigniter4\framework\system\Database\MySQLi\Connection.php:295
Stack trace:
#0 C:\laragon\www\app.ai-pro.org\btapp\vendor\codeigniter4\framework\system\Database\MySQLi\Connection.php(295): mysqli->query('INSERT INTO `st...', 0)
#1 C:\laragon\www\app.ai-pro.org\btapp\vendor\codeigniter4\framework\system\Database\BaseConnection.php(692): CodeIgniter\Database\MySQLi\Connection->execute('INSERT INTO `st...')
#2 C:\laragon\www\app.ai-pro.org\btapp\vendor\codeigniter4\framework\system\Database\BaseConnection.php(606): CodeIgniter\Database\BaseConnection->simpleQuery('INSERT INTO `st...')
#3 C:\laragon\www\app.ai-pro.org\btapp\vendor\codeigniter4\framework\system\Database\BaseBuilder.php(1801): CodeIgniter\Database\BaseConnection->query('INSERT INTO `st...', NULL, false)
#4 C:\laragon\www\app.ai-pro.org\btapp\vendor\codeigniter4\framework\system\Database\BaseBuilder.php(2192): CodeIgniter\Database\BaseBuilder->batchExecute('_insertBatch', 100)
#5 C:\laragon\www\app.ai-pro.org\btapp\app\Database\Seeds\InsertPlanTableV2.php(11170): CodeIgniter\Database\BaseBuilder->insertBatch(Array)
#6 C:\laragon\www\app.ai-pro.org\btapp\vendor\codeigniter4\framework\system\Database\Seeder.php(146): App\Database\Seeds\InsertPlanTableV2->run()
#7 C:\laragon\www\app.ai-pro.org\btapp\vendor\codeigniter4\framework\system\Commands\Database\Seed.php(77): CodeIgniter\Database\Seeder->call('App\\Database\\Se...')
#8 C:\laragon\www\app.ai-pro.org\btapp\vendor\codeigniter4\framework\system\CLI\Commands.php(65): CodeIgniter\Commands\Database\Seed->run(Array)
#9 C:\laragon\www\app.ai-pro.org\btapp\vendor\codeigniter4\framework\system\CLI\Console.php(37): CodeIgniter\CLI\Commands->run('db:seed', Array)
#10 C:\laragon\www\app.ai-pro.org\btapp\spark(97): CodeIgniter\CLI\Console->run()
#11 {main}
ERROR - 2025-06-12 06:55:52 --> mysqli_sql_exception: Column count doesn't match value count at row 1 in C:\laragon\www\app.ai-pro.org\btapp\vendor\codeigniter4\framework\system\Database\MySQLi\Connection.php:295
Stack trace:
#0 C:\laragon\www\app.ai-pro.org\btapp\vendor\codeigniter4\framework\system\Database\MySQLi\Connection.php(295): mysqli->query('INSERT INTO `st...', 0)
#1 C:\laragon\www\app.ai-pro.org\btapp\vendor\codeigniter4\framework\system\Database\BaseConnection.php(692): CodeIgniter\Database\MySQLi\Connection->execute('INSERT INTO `st...')
#2 C:\laragon\www\app.ai-pro.org\btapp\vendor\codeigniter4\framework\system\Database\BaseConnection.php(606): CodeIgniter\Database\BaseConnection->simpleQuery('INSERT INTO `st...')
#3 C:\laragon\www\app.ai-pro.org\btapp\vendor\codeigniter4\framework\system\Database\BaseBuilder.php(1801): CodeIgniter\Database\BaseConnection->query('INSERT INTO `st...', NULL, false)
#4 C:\laragon\www\app.ai-pro.org\btapp\vendor\codeigniter4\framework\system\Database\BaseBuilder.php(2192): CodeIgniter\Database\BaseBuilder->batchExecute('_insertBatch', 100)
#5 C:\laragon\www\app.ai-pro.org\btapp\app\Database\Seeds\InsertPlanTableV2.php(11170): CodeIgniter\Database\BaseBuilder->insertBatch(Array)
#6 C:\laragon\www\app.ai-pro.org\btapp\vendor\codeigniter4\framework\system\Database\Seeder.php(146): App\Database\Seeds\InsertPlanTableV2->run()
#7 C:\laragon\www\app.ai-pro.org\btapp\vendor\codeigniter4\framework\system\Commands\Database\Seed.php(77): CodeIgniter\Database\Seeder->call('App\\Database\\Se...')
#8 C:\laragon\www\app.ai-pro.org\btapp\vendor\codeigniter4\framework\system\CLI\Commands.php(65): CodeIgniter\Commands\Database\Seed->run(Array)
#9 C:\laragon\www\app.ai-pro.org\btapp\vendor\codeigniter4\framework\system\CLI\Console.php(37): CodeIgniter\CLI\Commands->run('db:seed', Array)
#10 C:\laragon\www\app.ai-pro.org\btapp\spark(97): CodeIgniter\CLI\Console->run()
#11 {main}
ERROR - 2025-06-12 06:55:57 --> mysqli_sql_exception: Column count doesn't match value count at row 1 in C:\laragon\www\app.ai-pro.org\btapp\vendor\codeigniter4\framework\system\Database\MySQLi\Connection.php:295
Stack trace:
#0 C:\laragon\www\app.ai-pro.org\btapp\vendor\codeigniter4\framework\system\Database\MySQLi\Connection.php(295): mysqli->query('INSERT INTO `st...', 0)
#1 C:\laragon\www\app.ai-pro.org\btapp\vendor\codeigniter4\framework\system\Database\BaseConnection.php(692): CodeIgniter\Database\MySQLi\Connection->execute('INSERT INTO `st...')
#2 C:\laragon\www\app.ai-pro.org\btapp\vendor\codeigniter4\framework\system\Database\BaseConnection.php(606): CodeIgniter\Database\BaseConnection->simpleQuery('INSERT INTO `st...')
#3 C:\laragon\www\app.ai-pro.org\btapp\vendor\codeigniter4\framework\system\Database\BaseBuilder.php(1801): CodeIgniter\Database\BaseConnection->query('INSERT INTO `st...', NULL, false)
#4 C:\laragon\www\app.ai-pro.org\btapp\vendor\codeigniter4\framework\system\Database\BaseBuilder.php(2192): CodeIgniter\Database\BaseBuilder->batchExecute('_insertBatch', 100)
#5 C:\laragon\www\app.ai-pro.org\btapp\app\Database\Seeds\InsertPlanTableV2.php(11139): CodeIgniter\Database\BaseBuilder->insertBatch(Array)
#6 C:\laragon\www\app.ai-pro.org\btapp\vendor\codeigniter4\framework\system\Database\Seeder.php(146): App\Database\Seeds\InsertPlanTableV2->run()
#7 C:\laragon\www\app.ai-pro.org\btapp\vendor\codeigniter4\framework\system\Commands\Database\Seed.php(77): CodeIgniter\Database\Seeder->call('App\\Database\\Se...')
#8 C:\laragon\www\app.ai-pro.org\btapp\vendor\codeigniter4\framework\system\CLI\Commands.php(65): CodeIgniter\Commands\Database\Seed->run(Array)
#9 C:\laragon\www\app.ai-pro.org\btapp\vendor\codeigniter4\framework\system\CLI\Console.php(37): CodeIgniter\CLI\Commands->run('db:seed', Array)
#10 C:\laragon\www\app.ai-pro.org\btapp\spark(97): CodeIgniter\CLI\Console->run()
#11 {main}
DEBUG - 2025-06-12 08:31:26 --> REQ ----------->
INFO - 2025-06-12 08:31:26 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-12 08:31:26 --> REQ ----------->
INFO - 2025-06-12 08:31:26 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2025-06-12 08:31:27 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-12 08:31:28 --> REQ ----------->14
DEBUG - 2025-06-12 08:31:29 --> REQ ----------->
INFO - 2025-06-12 08:31:29 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-12 08:32:11 --> REQ ----------->
INFO - 2025-06-12 08:32:11 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-12 08:33:09 --> REQ ----------->
INFO - 2025-06-12 08:33:09 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-12 08:33:10 --> REQ ----------->
INFO - 2025-06-12 08:33:10 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-12 08:33:12 --> REQ ----------->
INFO - 2025-06-12 08:33:12 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-12 08:33:45 --> REQ ----------->
INFO - 2025-06-12 08:33:45 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-12 08:35:20 --> REQ ----------->
INFO - 2025-06-12 08:35:20 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-12 08:35:25 --> REQ ----------->
INFO - 2025-06-12 08:35:25 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-12 08:35:25 --> REQ ----------->
INFO - 2025-06-12 08:35:25 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-12 08:35:26 --> REQ ----------->
INFO - 2025-06-12 08:35:26 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-12 08:35:26 --> REQ ----------->
INFO - 2025-06-12 08:35:26 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-12 08:35:27 --> REQ ----------->
INFO - 2025-06-12 08:35:27 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-12 08:35:28 --> REQ ----------->
INFO - 2025-06-12 08:35:28 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-12 08:35:39 --> REQ ----------->
INFO - 2025-06-12 08:35:39 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-12 08:36:07 --> REQ ----------->
INFO - 2025-06-12 08:36:07 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-12 08:45:17 --> REQ ----------->
INFO - 2025-06-12 08:45:17 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-12 09:09:29 --> REQ ----------->
INFO - 2025-06-12 09:09:29 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-12 09:09:29 --> REQ ----------->
INFO - 2025-06-12 09:09:29 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-12 09:09:30 --> REQ ----------->
INFO - 2025-06-12 09:09:30 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-12 09:09:31 --> REQ ----------->
INFO - 2025-06-12 09:09:31 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2025-06-12 09:09:31 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-12 09:09:32 --> REQ ----------->14
DEBUG - 2025-06-12 09:09:33 --> REQ ----------->
INFO - 2025-06-12 09:09:33 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-12 09:10:06 --> REQ ----------->
INFO - 2025-06-12 09:10:06 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-12 09:10:20 --> REQ ----------->
INFO - 2025-06-12 09:10:20 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-12 09:16:21 --> REQ ----------->
INFO - 2025-06-12 09:16:21 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-12 09:16:33 --> REQ ----------->
INFO - 2025-06-12 09:16:33 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-12 09:16:35 --> REQ ----------->
INFO - 2025-06-12 09:16:35 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-12 09:16:35 --> REQ ----------->
INFO - 2025-06-12 09:16:35 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-12 09:16:38 --> REQ ----------->
INFO - 2025-06-12 09:16:38 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-12 09:16:39 --> REQ ----------->
INFO - 2025-06-12 09:16:39 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-12 09:16:39 --> REQ ----------->
INFO - 2025-06-12 09:16:39 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-12 09:16:39 --> REQ ----------->
INFO - 2025-06-12 09:16:39 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2025-06-12 09:16:43 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-12 09:16:43 --> REQ ----------->
INFO - 2025-06-12 09:16:43 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-12 09:16:44 --> REQ ----------->119
DEBUG - 2025-06-12 09:16:44 --> REQ ----------->
INFO - 2025-06-12 09:16:44 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-12 09:16:44 --> REQ ----------->
INFO - 2025-06-12 09:16:44 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-12 09:16:45 --> REQ ----------->
INFO - 2025-06-12 09:16:45 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-12 09:16:45 --> REQ ----------->
INFO - 2025-06-12 09:16:45 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-12 09:16:46 --> REQ ----------->
INFO - 2025-06-12 09:16:46 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-12 09:16:46 --> REQ ----------->
INFO - 2025-06-12 09:16:46 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-12 09:16:47 --> REQ ----------->
INFO - 2025-06-12 09:16:47 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-12 09:16:47 --> REQ ----------->
INFO - 2025-06-12 09:16:47 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-12 09:16:49 --> REQ ----------->
INFO - 2025-06-12 09:16:49 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-12 09:17:23 --> REQ ----------->
INFO - 2025-06-12 09:17:23 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2025-06-12 09:17:25 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-12 09:17:25 --> REQ ----------->
INFO - 2025-06-12 09:17:25 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-12 09:17:26 --> REQ ----------->71
DEBUG - 2025-06-12 09:17:26 --> REQ ----------->
INFO - 2025-06-12 09:17:26 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-12 09:17:26 --> REQ ----------->
INFO - 2025-06-12 09:17:26 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-12 09:17:26 --> REQ ----------->
INFO - 2025-06-12 09:17:26 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-12 09:17:28 --> REQ ----------->
INFO - 2025-06-12 09:17:28 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-12 09:17:29 --> REQ ----------->
INFO - 2025-06-12 09:17:29 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-12 09:17:31 --> REQ ----------->
INFO - 2025-06-12 09:17:31 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-12 09:17:32 --> REQ ----------->
INFO - 2025-06-12 09:17:32 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-12 09:17:34 --> REQ ----------->
INFO - 2025-06-12 09:17:34 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-12 09:17:36 --> REQ ----------->
INFO - 2025-06-12 09:17:36 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-12 09:17:36 --> REQ ----------->
INFO - 2025-06-12 09:17:36 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-12 09:17:37 --> REQ ----------->
INFO - 2025-06-12 09:17:37 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-12 09:17:43 --> REQ ----------->
INFO - 2025-06-12 09:17:43 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2025-06-12 09:17:45 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-12 09:17:46 --> REQ ----------->
INFO - 2025-06-12 09:17:46 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-12 09:17:46 --> REQ ----------->97
DEBUG - 2025-06-12 09:17:46 --> REQ ----------->
INFO - 2025-06-12 09:17:46 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-12 09:17:46 --> REQ ----------->
INFO - 2025-06-12 09:17:46 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-12 09:17:47 --> REQ ----------->
INFO - 2025-06-12 09:17:47 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-12 09:17:48 --> REQ ----------->
INFO - 2025-06-12 09:17:48 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-12 09:17:50 --> REQ ----------->
INFO - 2025-06-12 09:17:50 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-12 09:17:52 --> REQ ----------->
INFO - 2025-06-12 09:17:52 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-12 09:17:53 --> REQ ----------->
INFO - 2025-06-12 09:17:53 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-12 09:17:54 --> REQ ----------->
INFO - 2025-06-12 09:17:54 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-12 09:17:55 --> REQ ----------->
INFO - 2025-06-12 09:17:55 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-12 09:17:56 --> REQ ----------->
INFO - 2025-06-12 09:17:56 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-12 09:17:57 --> REQ ----------->
INFO - 2025-06-12 09:17:57 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-12 09:17:59 --> REQ ----------->
INFO - 2025-06-12 09:17:59 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2025-06-12 09:18:02 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-12 09:18:03 --> REQ ----------->
INFO - 2025-06-12 09:18:03 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-12 09:18:03 --> REQ ----------->118
DEBUG - 2025-06-12 09:18:03 --> REQ ----------->
INFO - 2025-06-12 09:18:03 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-12 09:18:03 --> REQ ----------->
INFO - 2025-06-12 09:18:03 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-12 09:18:04 --> REQ ----------->
INFO - 2025-06-12 09:18:04 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-12 09:18:06 --> REQ ----------->
INFO - 2025-06-12 09:18:06 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-12 09:18:11 --> REQ ----------->
INFO - 2025-06-12 09:18:11 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-12 09:18:12 --> REQ ----------->
INFO - 2025-06-12 09:18:12 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2025-06-12 09:18:12 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-12 09:18:12 --> REQ ----------->71
DEBUG - 2025-06-12 09:18:13 --> REQ ----------->
INFO - 2025-06-12 09:18:13 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2025-06-12 09:18:14 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-12 09:18:14 --> REQ ----------->
INFO - 2025-06-12 09:18:14 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-12 09:18:14 --> REQ ----------->
INFO - 2025-06-12 09:18:14 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-12 09:18:15 --> REQ ----------->14
DEBUG - 2025-06-12 09:18:15 --> REQ ----------->
INFO - 2025-06-12 09:18:15 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-12 09:18:15 --> REQ ----------->
INFO - 2025-06-12 09:18:15 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-12 09:18:19 --> REQ ----------->
INFO - 2025-06-12 09:18:19 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2025-06-12 09:18:20 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-12 09:18:20 --> REQ ----------->14
DEBUG - 2025-06-12 09:18:20 --> REQ ----------->
INFO - 2025-06-12 09:18:20 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-12 09:18:52 --> REQ ----------->
INFO - 2025-06-12 09:18:52 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-12 09:18:53 --> REQ ----------->
INFO - 2025-06-12 09:18:53 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-12 09:18:54 --> REQ ----------->
INFO - 2025-06-12 09:18:54 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-12 09:18:55 --> REQ ----------->
INFO - 2025-06-12 09:18:55 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-12 09:18:55 --> REQ ----------->
INFO - 2025-06-12 09:18:55 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-12 09:18:57 --> REQ ----------->
INFO - 2025-06-12 09:18:57 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-12 09:19:07 --> REQ ----------->
INFO - 2025-06-12 09:19:07 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2025-06-12 09:19:17 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-12 09:19:17 --> REQ ----------->
INFO - 2025-06-12 09:19:17 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-12 09:19:18 --> REQ ----------->03
DEBUG - 2025-06-12 09:19:18 --> REQ ----------->
INFO - 2025-06-12 09:19:18 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-12 09:19:21 --> REQ ----------->
INFO - 2025-06-12 09:19:21 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-12 09:19:22 --> REQ ----------->
INFO - 2025-06-12 09:19:22 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-12 09:19:28 --> REQ ----------->
INFO - 2025-06-12 09:19:28 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-12 09:19:29 --> REQ ----------->
INFO - 2025-06-12 09:19:29 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-12 09:19:30 --> REQ ----------->
INFO - 2025-06-12 09:19:30 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-12 09:19:32 --> REQ ----------->
INFO - 2025-06-12 09:19:32 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-12 09:19:32 --> REQ ----------->
INFO - 2025-06-12 09:19:32 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-12 09:19:33 --> REQ ----------->
INFO - 2025-06-12 09:19:33 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-12 09:19:35 --> REQ ----------->
INFO - 2025-06-12 09:19:35 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-12 09:19:35 --> REQ ----------->
INFO - 2025-06-12 09:19:35 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2025-06-12 09:19:36 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-12 09:19:37 --> REQ ----------->97
DEBUG - 2025-06-12 09:19:37 --> REQ ----------->
INFO - 2025-06-12 09:19:37 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-12 09:19:39 --> REQ ----------->
INFO - 2025-06-12 09:19:39 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2025-06-12 09:19:39 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-12 09:19:40 --> REQ ----------->118
DEBUG - 2025-06-12 09:19:40 --> REQ ----------->
INFO - 2025-06-12 09:19:40 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-12 09:19:43 --> REQ ----------->
INFO - 2025-06-12 09:19:43 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-12 09:19:44 --> REQ ----------->
INFO - 2025-06-12 09:19:44 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-12 09:19:45 --> REQ ----------->
INFO - 2025-06-12 09:19:45 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2025-06-12 09:19:45 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-12 09:19:46 --> REQ ----------->119
DEBUG - 2025-06-12 09:19:46 --> REQ ----------->
INFO - 2025-06-12 09:19:46 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-12 09:19:59 --> REQ ----------->
INFO - 2025-06-12 09:19:59 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-12 09:20:04 --> REQ ----------->
INFO - 2025-06-12 09:20:04 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-12 09:20:20 --> REQ ----------->
INFO - 2025-06-12 09:20:20 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-12 09:20:27 --> REQ ----------->
INFO - 2025-06-12 09:20:27 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-12 09:20:28 --> REQ ----------->
INFO - 2025-06-12 09:20:28 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2025-06-12 09:20:30 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-12 09:20:30 --> REQ ----------->119
DEBUG - 2025-06-12 09:20:31 --> REQ ----------->
INFO - 2025-06-12 09:20:31 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-12 09:20:33 --> REQ ----------->
INFO - 2025-06-12 09:20:33 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2025-06-12 09:20:35 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-12 09:20:35 --> REQ ----------->
INFO - 2025-06-12 09:20:35 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-12 09:20:36 --> REQ ----------->100
DEBUG - 2025-06-12 09:20:36 --> REQ ----------->
INFO - 2025-06-12 09:20:36 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2025-06-12 09:20:38 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-12 09:20:38 --> REQ ----------->100
DEBUG - 2025-06-12 09:20:39 --> REQ ----------->
INFO - 2025-06-12 09:20:39 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-12 09:20:43 --> REQ ----------->
INFO - 2025-06-12 09:20:43 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-12 09:20:57 --> REQ ----------->
INFO - 2025-06-12 09:20:57 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2025-06-12 09:20:59 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-12 09:20:59 --> REQ ----------->
INFO - 2025-06-12 09:20:59 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-12 09:21:00 --> REQ ----------->
INFO - 2025-06-12 09:21:00 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-12 09:21:00 --> REQ ----------->100
DEBUG - 2025-06-12 09:21:00 --> REQ ----------->
INFO - 2025-06-12 09:21:00 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-12 09:21:02 --> REQ ----------->
INFO - 2025-06-12 09:21:02 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-12 09:21:03 --> REQ ----------->
INFO - 2025-06-12 09:21:03 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-12 09:21:04 --> REQ ----------->
INFO - 2025-06-12 09:21:04 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-12 09:21:05 --> REQ ----------->
INFO - 2025-06-12 09:21:05 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-12 09:21:07 --> REQ ----------->
INFO - 2025-06-12 09:21:07 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-12 09:21:13 --> REQ ----------->
INFO - 2025-06-12 09:21:13 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2025-06-12 09:21:16 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-12 09:21:16 --> REQ ----------->
INFO - 2025-06-12 09:21:16 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-12 09:21:17 --> REQ ----------->102
DEBUG - 2025-06-12 09:21:17 --> REQ ----------->
INFO - 2025-06-12 09:21:17 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-12 09:21:18 --> REQ ----------->
INFO - 2025-06-12 09:21:18 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-12 09:21:32 --> REQ ----------->
INFO - 2025-06-12 09:21:32 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-12 09:32:19 --> REQ ----------->
INFO - 2025-06-12 09:32:19 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-12 09:32:20 --> REQ ----------->
INFO - 2025-06-12 09:32:20 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-12 09:32:21 --> REQ ----------->
INFO - 2025-06-12 09:32:21 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-12 09:35:38 --> REQ ----------->
INFO - 2025-06-12 09:35:38 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-12 09:35:39 --> REQ ----------->
INFO - 2025-06-12 09:35:39 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-12 09:36:39 --> REQ ----------->
INFO - 2025-06-12 09:36:39 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-12 09:36:42 --> REQ ----------->
INFO - 2025-06-12 09:36:42 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-12 09:41:19 --> REQ ----------->
INFO - 2025-06-12 09:41:19 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2025-06-12 09:41:23 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-12 09:41:24 --> REQ ----------->03
DEBUG - 2025-06-12 09:41:25 --> REQ ----------->
INFO - 2025-06-12 09:41:25 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-12 09:43:15 --> REQ ----------->
INFO - 2025-06-12 09:43:15 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2025-06-12 09:43:16 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-12 09:43:17 --> REQ ----------->03
DEBUG - 2025-06-12 09:43:17 --> REQ ----------->
INFO - 2025-06-12 09:43:17 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-12 09:44:22 --> REQ ----------->
INFO - 2025-06-12 09:44:22 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2025-06-12 09:44:22 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-12 09:44:23 --> REQ ----------->03
DEBUG - 2025-06-12 09:44:24 --> REQ ----------->
INFO - 2025-06-12 09:44:24 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-12 09:44:26 --> REQ ----------->
INFO - 2025-06-12 09:44:26 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-12 09:44:28 --> REQ ----------->
INFO - 2025-06-12 09:44:28 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-12 09:44:29 --> REQ ----------->
INFO - 2025-06-12 09:44:29 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2025-06-12 09:44:29 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-12 09:44:30 --> REQ ----------->03
DEBUG - 2025-06-12 09:44:30 --> REQ ----------->
INFO - 2025-06-12 09:44:30 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-12 09:44:33 --> REQ ----------->
INFO - 2025-06-12 09:44:33 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-12 09:44:35 --> REQ ----------->
INFO - 2025-06-12 09:44:35 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-12 09:44:35 --> REQ ----------->
INFO - 2025-06-12 09:44:35 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-12 09:44:36 --> REQ ----------->
INFO - 2025-06-12 09:44:36 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-12 09:44:37 --> REQ ----------->
INFO - 2025-06-12 09:44:37 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-12 09:44:38 --> REQ ----------->
INFO - 2025-06-12 09:44:38 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-12 09:44:39 --> REQ ----------->
INFO - 2025-06-12 09:44:39 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-12 09:44:40 --> REQ ----------->
INFO - 2025-06-12 09:44:40 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-12 09:44:43 --> REQ ----------->
INFO - 2025-06-12 09:44:43 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-12 09:44:44 --> REQ ----------->
INFO - 2025-06-12 09:44:44 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-12 09:44:44 --> REQ ----------->
INFO - 2025-06-12 09:44:44 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-12 09:44:47 --> REQ ----------->
INFO - 2025-06-12 09:44:47 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-12 09:44:47 --> REQ ----------->
INFO - 2025-06-12 09:44:47 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-12 09:44:48 --> REQ ----------->
INFO - 2025-06-12 09:44:48 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-12 09:44:50 --> REQ ----------->
INFO - 2025-06-12 09:44:50 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-12 09:44:51 --> REQ ----------->
INFO - 2025-06-12 09:44:51 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-12 09:44:52 --> REQ ----------->
INFO - 2025-06-12 09:44:52 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-12 09:44:54 --> REQ ----------->
INFO - 2025-06-12 09:44:54 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-12 09:44:55 --> REQ ----------->
INFO - 2025-06-12 09:44:55 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-12 09:45:10 --> REQ ----------->
INFO - 2025-06-12 09:45:10 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-12 09:45:10 --> REQ ----------->
INFO - 2025-06-12 09:45:10 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-12 09:45:15 --> REQ ----------->
INFO - 2025-06-12 09:45:15 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-12 09:45:16 --> REQ ----------->
INFO - 2025-06-12 09:45:16 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-12 09:45:17 --> REQ ----------->
INFO - 2025-06-12 09:45:17 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-12 09:45:19 --> REQ ----------->
INFO - 2025-06-12 09:45:19 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-12 09:45:20 --> REQ ----------->
INFO - 2025-06-12 09:45:20 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-12 09:45:24 --> REQ ----------->
INFO - 2025-06-12 09:45:24 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-12 09:45:25 --> REQ ----------->
INFO - 2025-06-12 09:45:25 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-12 09:45:27 --> REQ ----------->
INFO - 2025-06-12 09:45:27 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-12 09:45:28 --> REQ ----------->
INFO - 2025-06-12 09:45:28 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-12 09:45:29 --> REQ ----------->
INFO - 2025-06-12 09:45:29 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-12 09:45:33 --> REQ ----------->
INFO - 2025-06-12 09:45:33 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-12 09:45:34 --> REQ ----------->
INFO - 2025-06-12 09:45:34 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2025-06-12 09:45:34 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-12 09:45:35 --> REQ ----------->14
DEBUG - 2025-06-12 09:45:35 --> REQ ----------->
INFO - 2025-06-12 09:45:35 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-12 09:45:37 --> REQ ----------->
INFO - 2025-06-12 09:45:37 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-12 09:45:38 --> REQ ----------->
INFO - 2025-06-12 09:45:38 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-12 09:45:39 --> REQ ----------->
INFO - 2025-06-12 09:45:39 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-12 09:45:40 --> REQ ----------->
INFO - 2025-06-12 09:45:40 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2025-06-12 09:45:40 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-12 09:45:41 --> REQ ----------->71
DEBUG - 2025-06-12 09:45:41 --> REQ ----------->
INFO - 2025-06-12 09:45:41 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-12 09:45:43 --> REQ ----------->
INFO - 2025-06-12 09:45:43 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-12 09:45:44 --> REQ ----------->
INFO - 2025-06-12 09:45:44 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-12 09:45:53 --> REQ ----------->
INFO - 2025-06-12 09:45:53 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-12 09:46:11 --> REQ ----------->
INFO - 2025-06-12 09:46:11 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2025-06-12 09:46:11 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-12 09:46:12 --> REQ ----------->97
DEBUG - 2025-06-12 09:46:12 --> REQ ----------->
INFO - 2025-06-12 09:46:12 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-12 09:46:15 --> REQ ----------->
INFO - 2025-06-12 09:46:15 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-12 09:46:15 --> REQ ----------->
INFO - 2025-06-12 09:46:15 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-12 09:46:16 --> REQ ----------->
INFO - 2025-06-12 09:46:16 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-12 09:46:17 --> REQ ----------->
INFO - 2025-06-12 09:46:17 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-12 09:46:20 --> REQ ----------->
INFO - 2025-06-12 09:46:20 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-12 09:46:21 --> REQ ----------->
INFO - 2025-06-12 09:46:21 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-12 09:46:23 --> REQ ----------->
INFO - 2025-06-12 09:46:23 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-12 09:46:24 --> REQ ----------->
INFO - 2025-06-12 09:46:24 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-12 09:46:26 --> REQ ----------->
INFO - 2025-06-12 09:46:26 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2025-06-12 09:46:26 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-12 09:46:27 --> REQ ----------->118
DEBUG - 2025-06-12 09:46:27 --> REQ ----------->
INFO - 2025-06-12 09:46:27 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-12 09:46:30 --> REQ ----------->
INFO - 2025-06-12 09:46:30 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-12 09:46:30 --> REQ ----------->
INFO - 2025-06-12 09:46:30 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2025-06-12 09:46:31 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-12 09:46:32 --> REQ ----------->119
DEBUG - 2025-06-12 09:46:32 --> REQ ----------->
INFO - 2025-06-12 09:46:32 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-12 09:46:34 --> REQ ----------->
INFO - 2025-06-12 09:46:34 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-12 09:46:35 --> REQ ----------->
INFO - 2025-06-12 09:46:35 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-12 09:46:38 --> REQ ----------->
INFO - 2025-06-12 09:46:38 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-12 09:46:41 --> REQ ----------->
INFO - 2025-06-12 09:46:41 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-12 09:49:20 --> REQ ----------->
INFO - 2025-06-12 09:49:20 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-12 09:49:21 --> REQ ----------->
INFO - 2025-06-12 09:49:21 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2025-06-12 09:49:22 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-12 09:49:23 --> REQ ----------->118
DEBUG - 2025-06-12 09:49:23 --> REQ ----------->
INFO - 2025-06-12 09:49:23 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-12 09:49:23 --> REQ ----------->
INFO - 2025-06-12 09:49:23 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-12 09:49:28 --> REQ ----------->
INFO - 2025-06-12 09:49:28 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2025-06-12 09:49:28 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-12 09:49:29 --> REQ ----------->119
DEBUG - 2025-06-12 09:49:29 --> REQ ----------->
INFO - 2025-06-12 09:49:29 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-12 09:49:38 --> REQ ----------->
INFO - 2025-06-12 09:49:38 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-12 09:49:41 --> REQ ----------->
INFO - 2025-06-12 09:49:41 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2025-06-12 09:49:41 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-12 09:49:41 --> REQ ----------->118
DEBUG - 2025-06-12 09:49:42 --> REQ ----------->
INFO - 2025-06-12 09:49:42 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-12 09:50:02 --> REQ ----------->
INFO - 2025-06-12 09:50:02 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2025-06-12 09:50:02 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-12 09:50:03 --> REQ ----------->118
DEBUG - 2025-06-12 09:50:03 --> REQ ----------->
INFO - 2025-06-12 09:50:03 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-12 09:50:05 --> REQ ----------->
INFO - 2025-06-12 09:50:05 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2025-06-12 09:50:05 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-12 09:50:06 --> REQ ----------->119
DEBUG - 2025-06-12 09:50:06 --> REQ ----------->
INFO - 2025-06-12 09:50:06 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-12 09:50:08 --> REQ ----------->
INFO - 2025-06-12 09:50:08 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-12 09:57:32 --> REQ ----------->
INFO - 2025-06-12 09:57:32 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-12 09:57:33 --> REQ ----------->
INFO - 2025-06-12 09:57:33 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-12 10:03:42 --> REQ ----------->
INFO - 2025-06-12 10:03:42 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2025-06-12 10:03:43 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-12 10:03:43 --> REQ ----------->119
DEBUG - 2025-06-12 10:03:44 --> REQ ----------->
INFO - 2025-06-12 10:03:44 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2025-06-12 10:03:46 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-12 10:03:46 --> REQ ----------->119
DEBUG - 2025-06-12 10:03:46 --> REQ ----------->
INFO - 2025-06-12 10:03:46 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-12 10:17:41 --> REQ ----------->
INFO - 2025-06-12 10:17:41 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2025-06-12 10:17:41 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-12 10:17:42 --> REQ ----------->119
DEBUG - 2025-06-12 10:17:42 --> REQ ----------->
INFO - 2025-06-12 10:17:42 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
