# InsertPlanTableV2 Database Seeder

## Overview

The `InsertPlanTableV2` is a CodeIgniter 4 database seeder class designed to populate the `plan` table with comprehensive subscription plan data. This seeder manages pricing plans for different subscription tiers across multiple payment providers and currencies.

## Purpose

This seeder is responsible for:
- Inserting/updating subscription plan data in the `plan` table
- Managing plans across multiple payment providers (Stripe, Paddle, Recurly, etc.)
- Supporting multiple currencies and countries
- Handling different plan types (Basic, Pro, ProMax, Enterprise, Advanced)
- Automatically calculating token limits based on plan types
- Managing localization data for different regions

## File Location

```
btapp/app/Database/Seeds/InsertPlanTableV2.php
```

## How to Use

### Basic Usage

Run the seeder using CodeIgniter's Spark CLI:

```bash
php spark db:seed InsertPlanTableV2
```

### From Project Root

Make sure you're in the correct directory:

```bash
cd btapp
php spark db:seed InsertPlanTableV2
```

## Key Features

### 1. Selective Data Management
- **Configurable Plan ID Threshold**: Only plans with `plan_id >= 1` are processed (configurable via `$plan_id` property)
- **Safe Deletion**: Existing plans with IDs >= threshold are deleted before re-insertion
- **Transaction Safety**: All operations are wrapped in database transactions

### 2. Multi-Provider Support
The seeder handles multiple payment providers:
- **Stripe**: Primary payment processor with test/live environment support
- **Paddle**: Alternative payment processor
- **Recurly**: Subscription management platform
- **FastSpring (fs_plan_id)**: Digital commerce platform
- **Stripe Checkout (sct_plan_id)**: Stripe's hosted checkout

### 3. Plan Types Supported
- **Basic**: Entry-level plans (500,000 tokens)
- **Pro**: Standard professional plans (1,000,000 tokens)
- **ProMax**: Premium plans (99,999,999 tokens)
- **Enterprise**: Team/business plans (740,000 tokens)
- **Advanced**: Advanced tier plans (2,500,000 tokens)

### 4. Multi-Currency Support
Plans are available in multiple currencies:
- USD (United States Dollar)
- EUR (Euro)
- GBP (British Pound)
- CAD (Canadian Dollar)
- AUD (Australian Dollar)
- SEK (Swedish Krona)
- TRY (Turkish Lira)
- And more...

## Data Structure

### Required Fields
Each plan entry includes:

```php
[
    'plan_id'           => 'Unique plan identifier',
    'plan_pid'          => 'Plan UUID',
    'plan_name'         => 'Plan display name (PRO, BASIC, etc.)',
    'plan_type'         => 'Plan type enum (Pro, Basic, ProMax, etc.)',
    'label'             => 'Plan label for UI',
    'payment_interval'  => 'Monthly/Yearly',
    'price'             => 'Plan price',
    'currency'          => 'Currency code (USD, EUR, etc.)',
    'currency_symbol'   => 'Currency symbol ($, €, etc.)',
    'country'           => 'Country code (US, GB, etc.)',
    'display_txt1'      => 'Primary display text',
    // ... additional fields
]
```

### Optional Fields
- `sub_text`: Subtitle or description
- `trial_days`: Free trial period
- `trial_price`: Trial pricing
- `max_members`: Maximum team members
- `price_per_member`: Per-member pricing
- `display_txt2`, `display_txt3`, `display_txt4`: Additional display texts
- `options`: JSON configuration options
- `locales`: Supported localization regions

## Dependencies

### Required Classes
- `App\Enums\PlanType`: Plan type enumeration
- `App\Enums\MaxTokens`: Token limit enumeration
- `App\Support\Plan`: Plan data processing helper
- `App\Support\StripePlanId`: Stripe plan ID management

### Automatic Processing
The seeder automatically:
1. **Validates Plan Types**: Uses `PlanType` enum for validation
2. **Sets Token Limits**: Automatically assigns `max_tokens` based on plan type
3. **Processes Stripe IDs**: Handles Stripe plan ID assignment
4. **Manages Locales**: Processes localization data
5. **Sets Timestamps**: Adds `created_at` and `updated_at` timestamps

## Post-Processing

After successful insertion, the seeder automatically calls:
```php
$this->call('UpdatePlanDescriptionSeeder');
```

This updates plan descriptions and localized content based on the inserted data.

## Error Handling

### Transaction Management
- All database operations are wrapped in transactions
- Automatic rollback on errors
- Exception handling for database errors

### Logging
- Success messages are displayed via CLI
- Error details are logged to CodeIgniter logs
- Insert count is reported upon completion

## Configuration

### Modifying Plan ID Threshold
To change which plans are processed, modify the `$plan_id` property:

```php
protected int $plan_id = 1; // Process plans with ID >= 1
```

### Adding New Plans
Add new plan data to the `$data` array in the `run()` method:

```php
[
    'plan_id'           => 'NEW_ID',
    'plan_name'         => 'NEW_PLAN',
    'plan_type'         => 'Pro', // Must match PlanType enum
    // ... other required fields
]
```

## Troubleshooting

### Common Issues

1. **Column Count Mismatch**: Ensure all required columns are present in plan data
2. **Invalid Plan Type**: Plan type must match values in `PlanType` enum
3. **Missing Dependencies**: Ensure all required support classes are available
4. **Database Connection**: Verify database configuration in `.env` file

### Debugging
Check the CodeIgniter logs for detailed error information:
```
btapp/writable/logs/log-YYYY-MM-DD.log
```

## Related Files

- `app/Models/PlanModel.php`: Plan model for database operations
- `app/Enums/PlanType.php`: Plan type enumeration
- `app/Enums/MaxTokens.php`: Token limit definitions
- `app/Support/Plan.php`: Plan data processing helper
- `app/Support/StripePlanId.php`: Stripe integration helper
- `app/Database/Seeds/UpdatePlanDescriptionSeeder.php`: Post-processing seeder

## Best Practices

1. **Backup Database**: Always backup before running the seeder
2. **Test Environment**: Test in development before production
3. **Verify Data**: Check inserted data after seeder completion
4. **Monitor Logs**: Review logs for any warnings or errors
5. **Validate Enums**: Ensure plan types match enum definitions

## Example Output

Successful execution will display:
```
Successfully inserted X plans.
```

Where X is the number of plans processed and inserted into the database.
