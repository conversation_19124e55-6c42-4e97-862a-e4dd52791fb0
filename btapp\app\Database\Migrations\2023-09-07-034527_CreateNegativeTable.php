<?php

namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;

class CreateNegativeTable extends Migration
{
    private $table = "negative";

    public function up()
    {
		$this->forge->addField([
			'negative_id' => [
				'type' => 'INT',
				'constraint' => 11,
				'unsigned' => false,
				'auto_increment' => true,
			],
			'user_id' => [
				'type' => 'INT',
				'constraint' => 11
			],
			'email' => [
				'type' => 'VARCHAR',
				'constraint' => 100,
				'null' => true,
			],
			'cc_number_encrypted' => [
				'type' => 'VARCHAR',
				'constraint' => 100,
				'null' => true,
			],
			'created_at' => [
				'type' => 'DATETIME',
				'null' => false,
			],
			'updated_at' => [
				'type' => 'DATETIME',
				'null' => false,
			],
			'deleted_at' => [
				'type' => 'DATETIME',
				'null' => false,
			],
		]);

		$this->forge->addKey('negative_id', true);
		$this->forge->addKey(['email'], false, false, 'start_negative_email_IDX');
		$this->forge->addKey(['cc_number_encrypted'], false, false, 'start_negative_cc_number_encrypted_IDX');

		$this->forge->createTable($this->table);
		$this->db->enableForeignKeyChecks();
    }

    public function down()
    {
        //
    }
}
