DEBUG - 2025-06-23 02:41:50 --> REQ ----------->
INFO - 2025-06-23 02:41:50 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 02:41:51 --> REQ ----------->
INFO - 2025-06-23 02:41:51 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2025-06-23 02:41:53 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 02:41:53 --> REQ ----------->
INFO - 2025-06-23 02:41:53 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 02:41:56 --> REQ ----------->
INFO - 2025-06-23 02:41:56 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 02:41:56 --> REQ ----------->
INFO - 2025-06-23 02:41:56 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 02:42:01 --> REQ ----------->
INFO - 2025-06-23 02:42:01 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 02:42:02 --> REQ ----------->
INFO - 2025-06-23 02:42:02 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2025-06-23 02:42:03 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 02:42:04 --> REQ ----------->
INFO - 2025-06-23 02:42:04 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 02:42:05 --> REQ ----------->
INFO - 2025-06-23 02:42:05 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 02:42:05 --> REQ ----------->
INFO - 2025-06-23 02:42:05 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 02:42:10 --> REQ ----------->
INFO - 2025-06-23 02:42:10 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 02:42:12 --> REQ ----------->testuser061125_02@mailinator.com123123
DEBUG - 2025-06-23 02:42:13 --> btdbFindBy ---> 
DEBUG - 2025-06-23 02:42:13 --> btdbFindBy ---> SELECT *
FROM `start-user`
WHERE `email` = '<EMAIL>'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
INFO - 2025-06-23 02:42:13 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 02:42:13 --> btdbFindBy ---> SELECT `start-plan`.*, `start-account`.*, DATE_FORMAT(resumed_at , "%M %e, %Y") as "resumed_at_formatted", `start-user`.`mode`, case when DATE_ADD(end_date, INTERVAL 1 DAY) < now() then 'yes' else 'no' end as 'expired'
FROM `start-account`
JOIN `start-user` ON `start-user`.`user_id` = `start-account`.`user_id`
JOIN `start-plan` ON `start-plan`.`plan_id` = `start-account`.`plan_id`
WHERE `start-account`.`user_id` = '3'
ORDER BY `start-account`.`end_date` DESC
DEBUG - 2025-06-23 02:42:13 --> btdbFindBy ---> SELECT *
FROM `start-plan`
WHERE `plan_id` = '144'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-23 02:42:13 --> login-------> <EMAIL>
DEBUG - 2025-06-23 02:42:13 --> btdbFindBy ---> SELECT *
FROM `start-user_app`
WHERE `email` = '<EMAIL>'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
INFO - 2025-06-23 02:42:13 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 02:42:13 --> btdbFindBy ---> 
DEBUG - 2025-06-23 02:42:13 --> btdbFindBy ---> SELECT `start-plan`.*, `start-account`.*, DATE_FORMAT(resumed_at , "%M %e, %Y") as "resumed_at_formatted", `start-user`.`mode`, case when DATE_ADD(end_date, INTERVAL 1 DAY) < now() then 'yes' else 'no' end as 'expired'
FROM `start-account`
JOIN `start-user` ON `start-user`.`user_id` = `start-account`.`user_id`
JOIN `start-plan` ON `start-plan`.`plan_id` = `start-account`.`plan_id`
WHERE `start-account`.`user_id` = '3'
ORDER BY `start-account`.`end_date` DESC
DEBUG - 2025-06-23 02:42:13 --> btdbFindBy ---> SELECT *
FROM `start-plan`
WHERE `plan_id` = '144'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-23 02:42:14 --> REQ ----------->$2y$09$5NQl9xDYdIbEUIpx946W9uSLz
INFO - 2025-06-23 02:42:14 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 02:42:14 --> REQ ----------->
INFO - 2025-06-23 02:42:14 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 02:42:17 --> REQ ----------->
DEBUG - 2025-06-23 02:42:17 --> REQ ----------->$2y$09$5NQl9xDYdIbEUIpx946W9uSLz
DEBUG - 2025-06-23 02:42:17 --> btdbFindBy ---> 
INFO - 2025-06-23 02:42:17 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2025-06-23 02:42:17 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 02:42:17 --> btdbFindBy ---> 
DEBUG - 2025-06-23 02:42:17 --> btdbFindBy ---> SELECT `start-plan`.*, `start-account`.*, DATE_FORMAT(resumed_at , "%M %e, %Y") as "resumed_at_formatted", `start-user`.`mode`, case when DATE_ADD(end_date, INTERVAL 1 DAY) < now() then 'yes' else 'no' end as 'expired'
FROM `start-account`
JOIN `start-user` ON `start-user`.`user_id` = `start-account`.`user_id`
JOIN `start-plan` ON `start-plan`.`plan_id` = `start-account`.`plan_id`
WHERE `start-account`.`user_id` = '3'
ORDER BY `start-account`.`end_date` DESC
DEBUG - 2025-06-23 02:42:18 --> REQ ----------->$2y$09$5NQl9xDYdIbEUIpx946W9uSLz
INFO - 2025-06-23 02:42:18 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 02:42:18 --> REQ ----------->
INFO - 2025-06-23 02:42:18 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 02:42:20 --> REQ ----------->$2y$09$5NQl9xDYdIbEUIpx946W9uSLz
DEBUG - 2025-06-23 02:42:20 --> btdbFindBy ---> 
INFO - 2025-06-23 02:42:20 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 02:42:21 --> REQ ----------->$2y$09$5NQl9xDYdIbEUIpx946W9uSLz
DEBUG - 2025-06-23 02:42:21 --> btdbFindBy ---> 
INFO - 2025-06-23 02:42:21 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 02:42:21 --> REQ ----------->$2y$09$5NQl9xDYdIbEUIpx946W9uSLz
INFO - 2025-06-23 02:42:21 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 02:42:21 --> REQ ----------->$2y$09$5NQl9xDYdIbEUIpx946W9uSLz
INFO - 2025-06-23 02:42:21 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 02:43:20 --> REQ ----------->$2y$09$5NQl9xDYdIbEUIpx946W9uSLz
INFO - 2025-06-23 02:43:20 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 02:43:20 --> REQ ----------->
INFO - 2025-06-23 02:43:20 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 02:44:06 --> REQ ----------->$2y$09$5NQl9xDYdIbEUIpx946W9uSLz
INFO - 2025-06-23 02:44:06 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 02:44:06 --> REQ ----------->
INFO - 2025-06-23 02:44:06 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2025-06-23 02:44:08 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 02:44:08 --> btdbFindBy ---> 
DEBUG - 2025-06-23 02:44:09 --> btdbFindBy ---> SELECT `start-plan`.*, `start-account`.*, DATE_FORMAT(resumed_at , "%M %e, %Y") as "resumed_at_formatted", `start-user`.`mode`, case when DATE_ADD(end_date, INTERVAL 1 DAY) < now() then 'yes' else 'no' end as 'expired'
FROM `start-account`
JOIN `start-user` ON `start-user`.`user_id` = `start-account`.`user_id`
JOIN `start-plan` ON `start-plan`.`plan_id` = `start-account`.`plan_id`
WHERE `start-account`.`user_id` = '3'
ORDER BY `start-account`.`end_date` DESC
DEBUG - 2025-06-23 02:44:09 --> REQ ----------->$2y$09$5NQl9xDYdIbEUIpx946W9uSLz
INFO - 2025-06-23 02:44:09 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 02:44:09 --> REQ ----------->
INFO - 2025-06-23 02:44:09 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 02:44:12 --> REQ ----------->$2y$09$5NQl9xDYdIbEUIpx946W9uSLz
DEBUG - 2025-06-23 02:44:12 --> btdbFindBy ---> 
INFO - 2025-06-23 02:44:12 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 02:44:12 --> REQ ----------->$2y$09$5NQl9xDYdIbEUIpx946W9uSLz
DEBUG - 2025-06-23 02:44:12 --> btdbFindBy ---> 
INFO - 2025-06-23 02:44:12 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 02:44:12 --> REQ ----------->$2y$09$5NQl9xDYdIbEUIpx946W9uSLz
INFO - 2025-06-23 02:44:12 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 02:44:12 --> REQ ----------->$2y$09$5NQl9xDYdIbEUIpx946W9uSLz
INFO - 2025-06-23 02:44:12 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 02:44:21 --> REQ ----------->$2y$09$5NQl9xDYdIbEUIpx946W9uSLz
INFO - 2025-06-23 02:44:21 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 02:44:21 --> REQ ----------->
INFO - 2025-06-23 02:44:21 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 02:44:44 --> REQ ----------->$2y$09$5NQl9xDYdIbEUIpx946W9uSLz
INFO - 2025-06-23 02:44:44 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 02:44:45 --> REQ ----------->
INFO - 2025-06-23 02:44:45 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2025-06-23 02:44:47 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 02:44:47 --> btdbFindBy ---> 
DEBUG - 2025-06-23 02:44:47 --> btdbFindBy ---> SELECT `start-plan`.*, `start-account`.*, DATE_FORMAT(resumed_at , "%M %e, %Y") as "resumed_at_formatted", `start-user`.`mode`, case when DATE_ADD(end_date, INTERVAL 1 DAY) < now() then 'yes' else 'no' end as 'expired'
FROM `start-account`
JOIN `start-user` ON `start-user`.`user_id` = `start-account`.`user_id`
JOIN `start-plan` ON `start-plan`.`plan_id` = `start-account`.`plan_id`
WHERE `start-account`.`user_id` = '3'
ORDER BY `start-account`.`end_date` DESC
DEBUG - 2025-06-23 02:44:48 --> REQ ----------->$2y$09$5NQl9xDYdIbEUIpx946W9uSLz
INFO - 2025-06-23 02:44:48 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 02:44:48 --> REQ ----------->
INFO - 2025-06-23 02:44:48 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 02:44:50 --> REQ ----------->$2y$09$5NQl9xDYdIbEUIpx946W9uSLz
DEBUG - 2025-06-23 02:44:50 --> btdbFindBy ---> 
INFO - 2025-06-23 02:44:50 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 02:44:50 --> REQ ----------->$2y$09$5NQl9xDYdIbEUIpx946W9uSLz
DEBUG - 2025-06-23 02:44:50 --> btdbFindBy ---> 
INFO - 2025-06-23 02:44:50 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 02:44:50 --> REQ ----------->$2y$09$5NQl9xDYdIbEUIpx946W9uSLz
INFO - 2025-06-23 02:44:50 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 02:44:50 --> REQ ----------->$2y$09$5NQl9xDYdIbEUIpx946W9uSLz
INFO - 2025-06-23 02:44:50 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 02:44:55 --> REQ ----------->$2y$09$5NQl9xDYdIbEUIpx946W9uSLz
INFO - 2025-06-23 02:44:55 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 02:44:55 --> REQ ----------->
INFO - 2025-06-23 02:44:55 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 02:45:30 --> REQ ----------->$2y$09$5NQl9xDYdIbEUIpx946W9uSLz
INFO - 2025-06-23 02:45:30 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 02:45:30 --> REQ ----------->
INFO - 2025-06-23 02:45:30 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 02:48:55 --> REQ ----------->$2y$09$5NQl9xDYdIbEUIpx946W9uSLz
INFO - 2025-06-23 02:48:55 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 02:48:55 --> REQ ----------->
INFO - 2025-06-23 02:48:55 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 02:48:57 --> REQ ----------->$2y$09$5NQl9xDYdIbEUIpx946W9uSLz
INFO - 2025-06-23 02:48:57 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2025-06-23 02:48:58 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 02:48:58 --> btdbFindBy ---> 
DEBUG - 2025-06-23 02:48:58 --> REQ ----------->
INFO - 2025-06-23 02:48:58 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 02:48:59 --> REQ ----------->
INFO - 2025-06-23 02:48:59 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 02:49:02 --> REQ ----------->testuser061125_01@mailinator.com123123
DEBUG - 2025-06-23 02:49:02 --> btdbFindBy ---> 
DEBUG - 2025-06-23 02:49:02 --> btdbFindBy ---> SELECT *
FROM `start-user`
WHERE `email` = '<EMAIL>'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
INFO - 2025-06-23 02:49:02 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 02:49:02 --> btdbFindBy ---> SELECT `start-plan`.*, `start-account`.*, DATE_FORMAT(resumed_at , "%M %e, %Y") as "resumed_at_formatted", `start-user`.`mode`, case when DATE_ADD(end_date, INTERVAL 1 DAY) < now() then 'yes' else 'no' end as 'expired'
FROM `start-account`
JOIN `start-user` ON `start-user`.`user_id` = `start-account`.`user_id`
JOIN `start-plan` ON `start-plan`.`plan_id` = `start-account`.`plan_id`
WHERE `start-account`.`user_id` = '2'
ORDER BY `start-account`.`end_date` DESC
DEBUG - 2025-06-23 02:49:02 --> btdbFindBy ---> SELECT *
FROM `start-plan`
WHERE `plan_id` = '143'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-23 02:49:02 --> login-------> <EMAIL>
DEBUG - 2025-06-23 02:49:02 --> btdbFindBy ---> SELECT *
FROM `start-user_app`
WHERE `email` = '<EMAIL>'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
INFO - 2025-06-23 02:49:02 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 02:49:02 --> btdbFindBy ---> 
DEBUG - 2025-06-23 02:49:02 --> btdbFindBy ---> SELECT `start-plan`.*, `start-account`.*, DATE_FORMAT(resumed_at , "%M %e, %Y") as "resumed_at_formatted", `start-user`.`mode`, case when DATE_ADD(end_date, INTERVAL 1 DAY) < now() then 'yes' else 'no' end as 'expired'
FROM `start-account`
JOIN `start-user` ON `start-user`.`user_id` = `start-account`.`user_id`
JOIN `start-plan` ON `start-plan`.`plan_id` = `start-account`.`plan_id`
WHERE `start-account`.`user_id` = '2'
ORDER BY `start-account`.`end_date` DESC
DEBUG - 2025-06-23 02:49:02 --> btdbFindBy ---> SELECT *
FROM `start-plan`
WHERE `plan_id` = '143'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-23 02:49:03 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-23 02:49:03 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 02:49:03 --> REQ ----------->
INFO - 2025-06-23 02:49:03 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 02:49:05 --> REQ ----------->
INFO - 2025-06-23 02:49:06 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 02:49:06 --> btdbFindBy ---> 
DEBUG - 2025-06-23 02:49:06 --> btdbFindBy ---> SELECT `start-plan`.*, `start-account`.*, DATE_FORMAT(resumed_at , "%M %e, %Y") as "resumed_at_formatted", `start-user`.`mode`, case when DATE_ADD(end_date, INTERVAL 1 DAY) < now() then 'yes' else 'no' end as 'expired'
FROM `start-account`
JOIN `start-user` ON `start-user`.`user_id` = `start-account`.`user_id`
JOIN `start-plan` ON `start-plan`.`plan_id` = `start-account`.`plan_id`
WHERE `start-account`.`user_id` = '2'
ORDER BY `start-account`.`end_date` DESC
DEBUG - 2025-06-23 02:49:06 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-23 02:49:06 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 02:49:06 --> REQ ----------->
INFO - 2025-06-23 02:49:06 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 02:49:09 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
DEBUG - 2025-06-23 02:49:09 --> btdbFindBy ---> 
INFO - 2025-06-23 02:49:09 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 02:49:09 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
DEBUG - 2025-06-23 02:49:09 --> btdbFindBy ---> 
INFO - 2025-06-23 02:49:09 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 02:49:09 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-23 02:49:09 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 02:49:09 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-23 02:49:09 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 02:52:24 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-23 02:52:24 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 02:52:24 --> REQ ----------->
INFO - 2025-06-23 02:52:24 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2025-06-23 02:52:27 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 02:52:27 --> btdbFindBy ---> 
DEBUG - 2025-06-23 02:52:27 --> btdbFindBy ---> SELECT `start-plan`.*, `start-account`.*, DATE_FORMAT(resumed_at , "%M %e, %Y") as "resumed_at_formatted", `start-user`.`mode`, case when DATE_ADD(end_date, INTERVAL 1 DAY) < now() then 'yes' else 'no' end as 'expired'
FROM `start-account`
JOIN `start-user` ON `start-user`.`user_id` = `start-account`.`user_id`
JOIN `start-plan` ON `start-plan`.`plan_id` = `start-account`.`plan_id`
WHERE `start-account`.`user_id` = '2'
ORDER BY `start-account`.`end_date` DESC
DEBUG - 2025-06-23 02:52:28 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-23 02:52:28 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 02:52:28 --> REQ ----------->
INFO - 2025-06-23 02:52:28 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 02:52:30 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
DEBUG - 2025-06-23 02:52:30 --> btdbFindBy ---> 
INFO - 2025-06-23 02:52:30 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 02:52:30 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
DEBUG - 2025-06-23 02:52:30 --> btdbFindBy ---> 
INFO - 2025-06-23 02:52:30 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 02:52:31 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-23 02:52:31 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 02:52:31 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-23 02:52:31 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 02:53:06 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-23 02:53:06 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 02:53:06 --> REQ ----------->
INFO - 2025-06-23 02:53:06 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 02:54:34 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-23 02:54:34 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 02:54:34 --> REQ ----------->
INFO - 2025-06-23 02:54:34 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 02:54:37 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-23 02:54:37 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 02:54:37 --> REQ ----------->
INFO - 2025-06-23 02:54:37 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2025-06-23 02:54:40 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 02:54:40 --> btdbFindBy ---> 
DEBUG - 2025-06-23 02:54:40 --> btdbFindBy ---> SELECT `start-plan`.*, `start-account`.*, DATE_FORMAT(resumed_at , "%M %e, %Y") as "resumed_at_formatted", `start-user`.`mode`, case when DATE_ADD(end_date, INTERVAL 1 DAY) < now() then 'yes' else 'no' end as 'expired'
FROM `start-account`
JOIN `start-user` ON `start-user`.`user_id` = `start-account`.`user_id`
JOIN `start-plan` ON `start-plan`.`plan_id` = `start-account`.`plan_id`
WHERE `start-account`.`user_id` = '2'
ORDER BY `start-account`.`end_date` DESC
DEBUG - 2025-06-23 02:54:41 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-23 02:54:41 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 02:54:41 --> REQ ----------->
INFO - 2025-06-23 02:54:41 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 02:54:43 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
DEBUG - 2025-06-23 02:54:43 --> btdbFindBy ---> 
INFO - 2025-06-23 02:54:43 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 02:54:43 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
DEBUG - 2025-06-23 02:54:43 --> btdbFindBy ---> 
INFO - 2025-06-23 02:54:43 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 02:54:43 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-23 02:54:43 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 02:54:43 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-23 02:54:43 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 02:54:45 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-23 02:54:45 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 02:54:45 --> REQ ----------->
INFO - 2025-06-23 02:54:45 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 02:55:05 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-23 02:55:05 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 02:55:05 --> REQ ----------->
INFO - 2025-06-23 02:55:06 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 02:56:20 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-23 02:56:20 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 02:56:20 --> REQ ----------->
INFO - 2025-06-23 02:56:20 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2025-06-23 02:56:22 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 02:56:22 --> btdbFindBy ---> 
DEBUG - 2025-06-23 02:56:22 --> btdbFindBy ---> SELECT `start-plan`.*, `start-account`.*, DATE_FORMAT(resumed_at , "%M %e, %Y") as "resumed_at_formatted", `start-user`.`mode`, case when DATE_ADD(end_date, INTERVAL 1 DAY) < now() then 'yes' else 'no' end as 'expired'
FROM `start-account`
JOIN `start-user` ON `start-user`.`user_id` = `start-account`.`user_id`
JOIN `start-plan` ON `start-plan`.`plan_id` = `start-account`.`plan_id`
WHERE `start-account`.`user_id` = '2'
ORDER BY `start-account`.`end_date` DESC
DEBUG - 2025-06-23 02:56:23 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-23 02:56:23 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 02:56:23 --> REQ ----------->
INFO - 2025-06-23 02:56:23 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 02:56:26 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
DEBUG - 2025-06-23 02:56:26 --> btdbFindBy ---> 
INFO - 2025-06-23 02:56:26 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 02:56:26 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
DEBUG - 2025-06-23 02:56:26 --> btdbFindBy ---> 
INFO - 2025-06-23 02:56:26 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 02:56:26 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-23 02:56:26 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 02:56:26 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-23 02:56:26 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 03:02:13 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-23 03:02:13 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 03:02:13 --> REQ ----------->
INFO - 2025-06-23 03:02:13 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 03:15:35 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-23 03:15:35 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 03:15:35 --> REQ ----------->
INFO - 2025-06-23 03:15:35 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2025-06-23 03:15:37 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 03:15:37 --> btdbFindBy ---> 
DEBUG - 2025-06-23 03:15:37 --> btdbFindBy ---> SELECT `start-plan`.*, `start-account`.*, DATE_FORMAT(resumed_at , "%M %e, %Y") as "resumed_at_formatted", `start-user`.`mode`, case when DATE_ADD(end_date, INTERVAL 1 DAY) < now() then 'yes' else 'no' end as 'expired'
FROM `start-account`
JOIN `start-user` ON `start-user`.`user_id` = `start-account`.`user_id`
JOIN `start-plan` ON `start-plan`.`plan_id` = `start-account`.`plan_id`
WHERE `start-account`.`user_id` = '2'
ORDER BY `start-account`.`end_date` DESC
DEBUG - 2025-06-23 03:15:38 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-23 03:15:38 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 03:15:38 --> REQ ----------->
INFO - 2025-06-23 03:15:38 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 03:15:41 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
DEBUG - 2025-06-23 03:15:41 --> btdbFindBy ---> 
INFO - 2025-06-23 03:15:41 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 03:15:41 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
DEBUG - 2025-06-23 03:15:41 --> btdbFindBy ---> 
INFO - 2025-06-23 03:15:41 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 03:15:41 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-23 03:15:41 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 03:15:41 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-23 03:15:41 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 03:15:45 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-23 03:15:45 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 03:15:45 --> REQ ----------->
INFO - 2025-06-23 03:15:45 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 03:17:59 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-23 03:17:59 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 03:17:59 --> REQ ----------->
INFO - 2025-06-23 03:17:59 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 03:22:25 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-23 03:22:25 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 03:22:25 --> REQ ----------->
INFO - 2025-06-23 03:22:25 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 03:44:16 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-23 03:44:16 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 03:44:16 --> REQ ----------->
INFO - 2025-06-23 03:44:16 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 03:44:18 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-23 03:44:18 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2025-06-23 03:44:18 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 03:44:18 --> btdbFindBy ---> 
DEBUG - 2025-06-23 03:44:18 --> btdbFindBy ---> SELECT `start-plan`.*, `start-account`.*, DATE_FORMAT(resumed_at , "%M %e, %Y") as "resumed_at_formatted", `start-user`.`mode`, case when DATE_ADD(end_date, INTERVAL 1 DAY) < now() then 'yes' else 'no' end as 'expired'
FROM `start-account`
JOIN `start-user` ON `start-user`.`user_id` = `start-account`.`user_id`
JOIN `start-plan` ON `start-plan`.`plan_id` = `start-account`.`plan_id`
WHERE `start-account`.`user_id` = '2'
ORDER BY `start-account`.`end_date` DESC
DEBUG - 2025-06-23 03:44:19 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-23 03:44:19 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 03:44:19 --> REQ ----------->
INFO - 2025-06-23 03:44:19 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 03:44:22 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
DEBUG - 2025-06-23 03:44:22 --> btdbFindBy ---> 
INFO - 2025-06-23 03:44:22 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 03:44:22 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
DEBUG - 2025-06-23 03:44:22 --> btdbFindBy ---> 
INFO - 2025-06-23 03:44:22 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 03:44:22 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-23 03:44:22 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2025-06-23 03:46:23 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 03:46:23 --> btdbFindBy ---> 
DEBUG - 2025-06-23 03:46:23 --> btdbFindBy ---> SELECT `start-plan`.*, `start-account`.*, DATE_FORMAT(resumed_at , "%M %e, %Y") as "resumed_at_formatted", `start-user`.`mode`, case when DATE_ADD(end_date, INTERVAL 1 DAY) < now() then 'yes' else 'no' end as 'expired'
FROM `start-account`
JOIN `start-user` ON `start-user`.`user_id` = `start-account`.`user_id`
JOIN `start-plan` ON `start-plan`.`plan_id` = `start-account`.`plan_id`
WHERE `start-account`.`user_id` = '2'
ORDER BY `start-account`.`end_date` DESC
DEBUG - 2025-06-23 03:46:24 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-23 03:46:24 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 03:46:24 --> REQ ----------->
INFO - 2025-06-23 03:46:24 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 03:46:26 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
DEBUG - 2025-06-23 03:46:26 --> btdbFindBy ---> 
INFO - 2025-06-23 03:46:26 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 03:46:26 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
DEBUG - 2025-06-23 03:46:26 --> btdbFindBy ---> 
INFO - 2025-06-23 03:46:26 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 03:46:26 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-23 03:46:26 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 03:46:26 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-23 03:46:26 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 03:46:36 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-23 03:46:36 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 03:46:37 --> REQ ----------->
INFO - 2025-06-23 03:46:37 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2025-06-23 03:48:04 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 03:48:04 --> btdbFindBy ---> 
DEBUG - 2025-06-23 03:48:04 --> btdbFindBy ---> SELECT `start-plan`.*, `start-account`.*, DATE_FORMAT(resumed_at , "%M %e, %Y") as "resumed_at_formatted", `start-user`.`mode`, case when DATE_ADD(end_date, INTERVAL 1 DAY) < now() then 'yes' else 'no' end as 'expired'
FROM `start-account`
JOIN `start-user` ON `start-user`.`user_id` = `start-account`.`user_id`
JOIN `start-plan` ON `start-plan`.`plan_id` = `start-account`.`plan_id`
WHERE `start-account`.`user_id` = '2'
ORDER BY `start-account`.`end_date` DESC
DEBUG - 2025-06-23 03:48:05 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-23 03:48:05 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 03:48:06 --> REQ ----------->
INFO - 2025-06-23 03:48:06 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 03:48:07 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
DEBUG - 2025-06-23 03:48:07 --> btdbFindBy ---> 
INFO - 2025-06-23 03:48:07 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 03:48:08 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
DEBUG - 2025-06-23 03:48:08 --> btdbFindBy ---> 
INFO - 2025-06-23 03:48:08 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 03:48:08 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-23 03:48:08 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 03:48:08 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-23 03:48:08 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2025-06-23 03:48:56 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 03:48:56 --> btdbFindBy ---> 
DEBUG - 2025-06-23 03:48:56 --> btdbFindBy ---> SELECT `start-plan`.*, `start-account`.*, DATE_FORMAT(resumed_at , "%M %e, %Y") as "resumed_at_formatted", `start-user`.`mode`, case when DATE_ADD(end_date, INTERVAL 1 DAY) < now() then 'yes' else 'no' end as 'expired'
FROM `start-account`
JOIN `start-user` ON `start-user`.`user_id` = `start-account`.`user_id`
JOIN `start-plan` ON `start-plan`.`plan_id` = `start-account`.`plan_id`
WHERE `start-account`.`user_id` = '2'
ORDER BY `start-account`.`end_date` DESC
DEBUG - 2025-06-23 03:48:57 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-23 03:48:57 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 03:48:57 --> REQ ----------->
INFO - 2025-06-23 03:48:57 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 03:48:59 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
DEBUG - 2025-06-23 03:48:59 --> btdbFindBy ---> 
INFO - 2025-06-23 03:48:59 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 03:48:59 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
DEBUG - 2025-06-23 03:48:59 --> btdbFindBy ---> 
INFO - 2025-06-23 03:48:59 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 03:49:00 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-23 03:49:00 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2025-06-23 03:50:12 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 03:50:12 --> btdbFindBy ---> 
DEBUG - 2025-06-23 03:50:12 --> btdbFindBy ---> SELECT `start-plan`.*, `start-account`.*, DATE_FORMAT(resumed_at , "%M %e, %Y") as "resumed_at_formatted", `start-user`.`mode`, case when DATE_ADD(end_date, INTERVAL 1 DAY) < now() then 'yes' else 'no' end as 'expired'
FROM `start-account`
JOIN `start-user` ON `start-user`.`user_id` = `start-account`.`user_id`
JOIN `start-plan` ON `start-plan`.`plan_id` = `start-account`.`plan_id`
WHERE `start-account`.`user_id` = '2'
ORDER BY `start-account`.`end_date` DESC
DEBUG - 2025-06-23 03:50:13 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-23 03:50:13 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 03:50:13 --> REQ ----------->
INFO - 2025-06-23 03:50:13 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 03:50:15 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
DEBUG - 2025-06-23 03:50:15 --> btdbFindBy ---> 
INFO - 2025-06-23 03:50:15 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 03:50:15 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
DEBUG - 2025-06-23 03:50:15 --> btdbFindBy ---> 
INFO - 2025-06-23 03:50:15 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 03:50:16 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-23 03:50:16 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 03:50:16 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-23 03:50:16 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 06:02:12 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-23 06:02:12 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 06:02:12 --> btdbFindBy ---> 
DEBUG - 2025-06-23 06:02:12 --> btdbFindBy ---> SELECT *
FROM `start-user`
WHERE `login_token` = '$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-23 06:02:12 --> btdbFindBy ---> SELECT `start-plan`.*, `start-account`.*, DATE_FORMAT(resumed_at , "%M %e, %Y") as "resumed_at_formatted", `start-user`.`mode`, case when DATE_ADD(end_date, INTERVAL 1 DAY) < now() then 'yes' else 'no' end as 'expired'
FROM `start-account`
JOIN `start-user` ON `start-user`.`user_id` = `start-account`.`user_id`
JOIN `start-plan` ON `start-plan`.`plan_id` = `start-account`.`plan_id`
WHERE `start-account`.`user_id` = '2'
ORDER BY `start-account`.`end_date` DESC
DEBUG - 2025-06-23 06:02:14 --> REQ ----------->
INFO - 2025-06-23 06:02:14 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 06:07:40 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-23 06:07:40 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 06:07:40 --> btdbFindBy ---> 
DEBUG - 2025-06-23 06:07:40 --> btdbFindBy ---> SELECT *
FROM `start-user`
WHERE `login_token` = '$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-23 06:07:40 --> btdbFindBy ---> SELECT `start-plan`.*, `start-account`.*, DATE_FORMAT(resumed_at , "%M %e, %Y") as "resumed_at_formatted", `start-user`.`mode`, case when DATE_ADD(end_date, INTERVAL 1 DAY) < now() then 'yes' else 'no' end as 'expired'
FROM `start-account`
JOIN `start-user` ON `start-user`.`user_id` = `start-account`.`user_id`
JOIN `start-plan` ON `start-plan`.`plan_id` = `start-account`.`plan_id`
WHERE `start-account`.`user_id` = '2'
ORDER BY `start-account`.`end_date` DESC
DEBUG - 2025-06-23 06:07:43 --> REQ ----------->
INFO - 2025-06-23 06:07:43 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2025-06-23 06:07:43 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 06:07:44 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-23 06:07:44 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2025-06-23 06:07:44 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 06:07:44 --> btdbFindBy ---> 
DEBUG - 2025-06-23 06:07:45 --> btdbFindBy ---> SELECT `start-plan`.*, `start-account`.*, DATE_FORMAT(resumed_at , "%M %e, %Y") as "resumed_at_formatted", `start-user`.`mode`, case when DATE_ADD(end_date, INTERVAL 1 DAY) < now() then 'yes' else 'no' end as 'expired'
FROM `start-account`
JOIN `start-user` ON `start-user`.`user_id` = `start-account`.`user_id`
JOIN `start-plan` ON `start-plan`.`plan_id` = `start-account`.`plan_id`
WHERE `start-account`.`user_id` = '2'
ORDER BY `start-account`.`end_date` DESC
DEBUG - 2025-06-23 06:07:45 --> btdbFindBy ---> SELECT *
FROM `start-plan`
WHERE `plan_id` = '143'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-23 06:07:46 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-23 06:07:46 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 06:07:46 --> REQ ----------->
INFO - 2025-06-23 06:07:46 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 06:07:48 --> REQ ----------->
DEBUG - 2025-06-23 06:07:48 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
DEBUG - 2025-06-23 06:07:48 --> btdbFindBy ---> 
INFO - 2025-06-23 06:07:48 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 06:07:48 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-23 06:07:48 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 06:08:02 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-23 06:08:02 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 06:08:02 --> REQ ----------->
INFO - 2025-06-23 06:08:02 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2025-06-23 06:08:07 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 06:08:07 --> btdbFindBy ---> 
DEBUG - 2025-06-23 06:08:07 --> btdbFindBy ---> SELECT `start-plan`.*, `start-account`.*, DATE_FORMAT(resumed_at , "%M %e, %Y") as "resumed_at_formatted", `start-user`.`mode`, case when DATE_ADD(end_date, INTERVAL 1 DAY) < now() then 'yes' else 'no' end as 'expired'
FROM `start-account`
JOIN `start-user` ON `start-user`.`user_id` = `start-account`.`user_id`
JOIN `start-plan` ON `start-plan`.`plan_id` = `start-account`.`plan_id`
WHERE `start-account`.`user_id` = '2'
ORDER BY `start-account`.`end_date` DESC
DEBUG - 2025-06-23 06:08:08 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-23 06:08:08 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 06:08:08 --> REQ ----------->
INFO - 2025-06-23 06:08:08 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 06:08:10 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
DEBUG - 2025-06-23 06:08:10 --> btdbFindBy ---> 
INFO - 2025-06-23 06:08:10 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 06:08:10 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
DEBUG - 2025-06-23 06:08:10 --> btdbFindBy ---> 
INFO - 2025-06-23 06:08:10 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 06:08:10 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-23 06:08:10 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 06:08:10 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-23 06:08:10 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 06:08:12 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-23 06:08:12 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 06:08:12 --> REQ ----------->
INFO - 2025-06-23 06:08:12 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 06:08:14 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-23 06:08:14 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 06:08:22 --> REQ ----------->
INFO - 2025-06-23 06:08:22 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 06:08:22 --> REQ ----------->
INFO - 2025-06-23 06:08:22 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 06:08:25 --> REQ ----------->
INFO - 2025-06-23 06:08:25 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 06:08:25 --> REQ ----------->
INFO - 2025-06-23 06:08:25 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 06:08:28 --> REQ ----------->testuser061125_01@mailinator.com123123
DEBUG - 2025-06-23 06:08:28 --> btdbFindBy ---> 
DEBUG - 2025-06-23 06:08:28 --> btdbFindBy ---> SELECT *
FROM `start-user`
WHERE `email` = '<EMAIL>'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
INFO - 2025-06-23 06:08:28 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 06:08:28 --> btdbFindBy ---> SELECT `start-plan`.*, `start-account`.*, DATE_FORMAT(resumed_at , "%M %e, %Y") as "resumed_at_formatted", `start-user`.`mode`, case when DATE_ADD(end_date, INTERVAL 1 DAY) < now() then 'yes' else 'no' end as 'expired'
FROM `start-account`
JOIN `start-user` ON `start-user`.`user_id` = `start-account`.`user_id`
JOIN `start-plan` ON `start-plan`.`plan_id` = `start-account`.`plan_id`
WHERE `start-account`.`user_id` = '2'
ORDER BY `start-account`.`end_date` DESC
DEBUG - 2025-06-23 06:08:28 --> btdbFindBy ---> SELECT *
FROM `start-plan`
WHERE `plan_id` = '143'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-23 06:08:28 --> login-------> <EMAIL>
DEBUG - 2025-06-23 06:08:28 --> btdbFindBy ---> SELECT *
FROM `start-user_app`
WHERE `email` = '<EMAIL>'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
INFO - 2025-06-23 06:08:28 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 06:08:28 --> btdbFindBy ---> 
DEBUG - 2025-06-23 06:08:28 --> btdbFindBy ---> SELECT `start-plan`.*, `start-account`.*, DATE_FORMAT(resumed_at , "%M %e, %Y") as "resumed_at_formatted", `start-user`.`mode`, case when DATE_ADD(end_date, INTERVAL 1 DAY) < now() then 'yes' else 'no' end as 'expired'
FROM `start-account`
JOIN `start-user` ON `start-user`.`user_id` = `start-account`.`user_id`
JOIN `start-plan` ON `start-plan`.`plan_id` = `start-account`.`plan_id`
WHERE `start-account`.`user_id` = '2'
ORDER BY `start-account`.`end_date` DESC
DEBUG - 2025-06-23 06:08:28 --> btdbFindBy ---> SELECT *
FROM `start-plan`
WHERE `plan_id` = '143'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-23 06:08:28 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-23 06:08:28 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 06:08:29 --> REQ ----------->
INFO - 2025-06-23 06:08:29 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 06:08:31 --> REQ ----------->
DEBUG - 2025-06-23 06:08:31 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
DEBUG - 2025-06-23 06:08:31 --> btdbFindBy ---> 
INFO - 2025-06-23 06:08:31 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2025-06-23 06:08:31 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 06:08:31 --> btdbFindBy ---> 
DEBUG - 2025-06-23 06:08:31 --> btdbFindBy ---> SELECT `start-plan`.*, `start-account`.*, DATE_FORMAT(resumed_at , "%M %e, %Y") as "resumed_at_formatted", `start-user`.`mode`, case when DATE_ADD(end_date, INTERVAL 1 DAY) < now() then 'yes' else 'no' end as 'expired'
FROM `start-account`
JOIN `start-user` ON `start-user`.`user_id` = `start-account`.`user_id`
JOIN `start-plan` ON `start-plan`.`plan_id` = `start-account`.`plan_id`
WHERE `start-account`.`user_id` = '2'
ORDER BY `start-account`.`end_date` DESC
DEBUG - 2025-06-23 06:08:32 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-23 06:08:32 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 06:08:32 --> REQ ----------->
INFO - 2025-06-23 06:08:32 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 06:08:35 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
DEBUG - 2025-06-23 06:08:35 --> btdbFindBy ---> 
INFO - 2025-06-23 06:08:35 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 06:08:35 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
DEBUG - 2025-06-23 06:08:35 --> btdbFindBy ---> 
INFO - 2025-06-23 06:08:35 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 06:08:35 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-23 06:08:35 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 06:08:35 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-23 06:08:35 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 06:08:40 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-23 06:08:40 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 06:08:40 --> REQ ----------->
INFO - 2025-06-23 06:08:40 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 06:08:42 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-23 06:08:42 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2025-06-23 06:08:44 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 06:08:44 --> btdbFindBy ---> 
DEBUG - 2025-06-23 06:08:44 --> btdbFindBy ---> SELECT `start-plan`.*, `start-account`.*, DATE_FORMAT(resumed_at , "%M %e, %Y") as "resumed_at_formatted", `start-user`.`mode`, case when DATE_ADD(end_date, INTERVAL 1 DAY) < now() then 'yes' else 'no' end as 'expired'
FROM `start-account`
JOIN `start-user` ON `start-user`.`user_id` = `start-account`.`user_id`
JOIN `start-plan` ON `start-plan`.`plan_id` = `start-account`.`plan_id`
WHERE `start-account`.`user_id` = '2'
ORDER BY `start-account`.`end_date` DESC
DEBUG - 2025-06-23 06:08:45 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-23 06:08:45 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 06:08:46 --> REQ ----------->
INFO - 2025-06-23 06:08:46 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 06:08:48 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
DEBUG - 2025-06-23 06:08:48 --> btdbFindBy ---> 
INFO - 2025-06-23 06:08:48 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 06:08:48 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
DEBUG - 2025-06-23 06:08:48 --> btdbFindBy ---> 
INFO - 2025-06-23 06:08:48 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 06:08:49 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-23 06:08:49 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 06:08:49 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-23 06:08:49 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 06:09:46 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-23 06:09:46 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 06:09:46 --> REQ ----------->
INFO - 2025-06-23 06:09:46 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2025-06-23 06:09:48 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 06:09:48 --> btdbFindBy ---> 
DEBUG - 2025-06-23 06:09:48 --> btdbFindBy ---> SELECT `start-plan`.*, `start-account`.*, DATE_FORMAT(resumed_at , "%M %e, %Y") as "resumed_at_formatted", `start-user`.`mode`, case when DATE_ADD(end_date, INTERVAL 1 DAY) < now() then 'yes' else 'no' end as 'expired'
FROM `start-account`
JOIN `start-user` ON `start-user`.`user_id` = `start-account`.`user_id`
JOIN `start-plan` ON `start-plan`.`plan_id` = `start-account`.`plan_id`
WHERE `start-account`.`user_id` = '2'
ORDER BY `start-account`.`end_date` DESC
DEBUG - 2025-06-23 06:09:49 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-23 06:09:49 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 06:09:49 --> REQ ----------->
INFO - 2025-06-23 06:09:49 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 06:09:52 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
DEBUG - 2025-06-23 06:09:52 --> btdbFindBy ---> 
INFO - 2025-06-23 06:09:52 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 06:09:52 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
DEBUG - 2025-06-23 06:09:52 --> btdbFindBy ---> 
INFO - 2025-06-23 06:09:52 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 06:09:52 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-23 06:09:52 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 06:09:52 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-23 06:09:52 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 06:09:56 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-23 06:09:56 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 06:09:56 --> REQ ----------->
INFO - 2025-06-23 06:09:56 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 06:10:04 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-23 06:10:04 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 06:10:04 --> REQ ----------->
INFO - 2025-06-23 06:10:04 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 06:10:47 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-23 06:10:47 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 06:10:47 --> REQ ----------->
INFO - 2025-06-23 06:10:47 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2025-06-23 06:10:49 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 06:10:49 --> btdbFindBy ---> 
DEBUG - 2025-06-23 06:10:49 --> btdbFindBy ---> SELECT `start-plan`.*, `start-account`.*, DATE_FORMAT(resumed_at , "%M %e, %Y") as "resumed_at_formatted", `start-user`.`mode`, case when DATE_ADD(end_date, INTERVAL 1 DAY) < now() then 'yes' else 'no' end as 'expired'
FROM `start-account`
JOIN `start-user` ON `start-user`.`user_id` = `start-account`.`user_id`
JOIN `start-plan` ON `start-plan`.`plan_id` = `start-account`.`plan_id`
WHERE `start-account`.`user_id` = '2'
ORDER BY `start-account`.`end_date` DESC
DEBUG - 2025-06-23 06:10:50 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-23 06:10:50 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 06:10:50 --> REQ ----------->
INFO - 2025-06-23 06:10:50 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 06:10:52 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
DEBUG - 2025-06-23 06:10:52 --> btdbFindBy ---> 
INFO - 2025-06-23 06:10:52 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 06:10:52 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
DEBUG - 2025-06-23 06:10:52 --> btdbFindBy ---> 
INFO - 2025-06-23 06:10:52 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 06:10:53 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-23 06:10:53 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 06:10:53 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-23 06:10:53 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 06:10:58 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-23 06:10:58 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 06:10:58 --> REQ ----------->
INFO - 2025-06-23 06:10:58 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 06:11:09 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-23 06:11:09 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 06:11:09 --> REQ ----------->
INFO - 2025-06-23 06:11:09 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 06:13:26 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-23 06:13:26 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 06:13:26 --> REQ ----------->
INFO - 2025-06-23 06:13:26 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2025-06-23 06:13:31 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 06:13:31 --> btdbFindBy ---> 
DEBUG - 2025-06-23 06:13:31 --> btdbFindBy ---> SELECT `start-plan`.*, `start-account`.*, DATE_FORMAT(resumed_at , "%M %e, %Y") as "resumed_at_formatted", `start-user`.`mode`, case when DATE_ADD(end_date, INTERVAL 1 DAY) < now() then 'yes' else 'no' end as 'expired'
FROM `start-account`
JOIN `start-user` ON `start-user`.`user_id` = `start-account`.`user_id`
JOIN `start-plan` ON `start-plan`.`plan_id` = `start-account`.`plan_id`
WHERE `start-account`.`user_id` = '2'
ORDER BY `start-account`.`end_date` DESC
DEBUG - 2025-06-23 06:13:32 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-23 06:13:32 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 06:13:33 --> REQ ----------->
INFO - 2025-06-23 06:13:33 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 06:13:36 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
DEBUG - 2025-06-23 06:13:36 --> btdbFindBy ---> 
INFO - 2025-06-23 06:13:36 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 06:13:36 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
DEBUG - 2025-06-23 06:13:36 --> btdbFindBy ---> 
INFO - 2025-06-23 06:13:36 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2025-06-23 06:13:36 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 06:13:36 --> btdbFindBy ---> 
DEBUG - 2025-06-23 06:13:36 --> btdbFindBy ---> SELECT `start-plan`.*, `start-account`.*, DATE_FORMAT(resumed_at , "%M %e, %Y") as "resumed_at_formatted", `start-user`.`mode`, case when DATE_ADD(end_date, INTERVAL 1 DAY) < now() then 'yes' else 'no' end as 'expired'
FROM `start-account`
JOIN `start-user` ON `start-user`.`user_id` = `start-account`.`user_id`
JOIN `start-plan` ON `start-plan`.`plan_id` = `start-account`.`plan_id`
WHERE `start-account`.`user_id` = '2'
ORDER BY `start-account`.`end_date` DESC
DEBUG - 2025-06-23 06:13:36 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-23 06:13:36 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 06:13:37 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-23 06:13:37 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2025-06-23 06:13:39 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 06:13:39 --> btdbFindBy ---> 
DEBUG - 2025-06-23 06:13:39 --> btdbFindBy ---> SELECT `start-plan`.*, `start-account`.*, DATE_FORMAT(resumed_at , "%M %e, %Y") as "resumed_at_formatted", `start-user`.`mode`, case when DATE_ADD(end_date, INTERVAL 1 DAY) < now() then 'yes' else 'no' end as 'expired'
FROM `start-account`
JOIN `start-user` ON `start-user`.`user_id` = `start-account`.`user_id`
JOIN `start-plan` ON `start-plan`.`plan_id` = `start-account`.`plan_id`
WHERE `start-account`.`user_id` = '2'
ORDER BY `start-account`.`end_date` DESC
INFO - 2025-06-23 06:13:41 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 06:13:41 --> btdbFindBy ---> 
DEBUG - 2025-06-23 06:13:41 --> btdbFindBy ---> SELECT `start-plan`.*, `start-account`.*, DATE_FORMAT(resumed_at , "%M %e, %Y") as "resumed_at_formatted", `start-user`.`mode`, case when DATE_ADD(end_date, INTERVAL 1 DAY) < now() then 'yes' else 'no' end as 'expired'
FROM `start-account`
JOIN `start-user` ON `start-user`.`user_id` = `start-account`.`user_id`
JOIN `start-plan` ON `start-plan`.`plan_id` = `start-account`.`plan_id`
WHERE `start-account`.`user_id` = '2'
ORDER BY `start-account`.`end_date` DESC
DEBUG - 2025-06-23 06:13:42 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-23 06:13:42 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 06:13:42 --> REQ ----------->
INFO - 2025-06-23 06:13:42 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 06:13:44 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
DEBUG - 2025-06-23 06:13:44 --> btdbFindBy ---> 
INFO - 2025-06-23 06:13:44 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 06:13:44 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
DEBUG - 2025-06-23 06:13:44 --> btdbFindBy ---> 
INFO - 2025-06-23 06:13:44 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 06:13:45 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-23 06:13:45 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 06:13:45 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-23 06:13:45 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2025-06-23 06:13:51 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 06:13:51 --> btdbFindBy ---> 
DEBUG - 2025-06-23 06:13:51 --> btdbFindBy ---> SELECT `start-plan`.*, `start-account`.*, DATE_FORMAT(resumed_at , "%M %e, %Y") as "resumed_at_formatted", `start-user`.`mode`, case when DATE_ADD(end_date, INTERVAL 1 DAY) < now() then 'yes' else 'no' end as 'expired'
FROM `start-account`
JOIN `start-user` ON `start-user`.`user_id` = `start-account`.`user_id`
JOIN `start-plan` ON `start-plan`.`plan_id` = `start-account`.`plan_id`
WHERE `start-account`.`user_id` = '2'
ORDER BY `start-account`.`end_date` DESC
DEBUG - 2025-06-23 06:13:52 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-23 06:13:52 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 06:13:52 --> REQ ----------->
INFO - 2025-06-23 06:13:52 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 06:13:54 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
DEBUG - 2025-06-23 06:13:54 --> btdbFindBy ---> 
INFO - 2025-06-23 06:13:54 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 06:13:55 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
DEBUG - 2025-06-23 06:13:55 --> btdbFindBy ---> 
INFO - 2025-06-23 06:13:55 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 06:13:55 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-23 06:13:55 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 06:13:55 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-23 06:13:55 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 06:17:30 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-23 06:17:30 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 06:17:30 --> REQ ----------->
INFO - 2025-06-23 06:17:30 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 06:17:33 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-23 06:17:33 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 06:17:33 --> REQ ----------->
INFO - 2025-06-23 06:17:33 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 06:25:55 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-23 06:25:55 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 06:25:55 --> REQ ----------->
INFO - 2025-06-23 06:25:55 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 06:26:49 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-23 06:26:49 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 06:26:49 --> REQ ----------->
INFO - 2025-06-23 06:26:49 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 06:31:22 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-23 06:31:22 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 06:31:22 --> REQ ----------->
INFO - 2025-06-23 06:31:22 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 06:34:19 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-23 06:34:19 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 06:34:19 --> REQ ----------->
INFO - 2025-06-23 06:34:19 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 06:35:27 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-23 06:35:27 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 06:35:27 --> REQ ----------->
INFO - 2025-06-23 06:35:27 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 06:44:43 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-23 06:44:43 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 06:44:43 --> REQ ----------->
INFO - 2025-06-23 06:44:43 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 06:46:06 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-23 06:46:07 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 06:46:07 --> REQ ----------->
INFO - 2025-06-23 06:46:07 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 07:02:53 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-23 07:02:53 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 07:02:53 --> REQ ----------->
INFO - 2025-06-23 07:02:53 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 07:02:55 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-23 07:02:55 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2025-06-23 07:02:55 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 07:02:55 --> btdbFindBy ---> 
DEBUG - 2025-06-23 07:02:55 --> btdbFindBy ---> SELECT `start-plan`.*, `start-account`.*, DATE_FORMAT(resumed_at , "%M %e, %Y") as "resumed_at_formatted", `start-user`.`mode`, case when DATE_ADD(end_date, INTERVAL 1 DAY) < now() then 'yes' else 'no' end as 'expired'
FROM `start-account`
JOIN `start-user` ON `start-user`.`user_id` = `start-account`.`user_id`
JOIN `start-plan` ON `start-plan`.`plan_id` = `start-account`.`plan_id`
WHERE `start-account`.`user_id` = '2'
ORDER BY `start-account`.`end_date` DESC
DEBUG - 2025-06-23 07:02:56 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-23 07:02:56 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 07:02:56 --> REQ ----------->
INFO - 2025-06-23 07:02:56 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 07:02:58 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
DEBUG - 2025-06-23 07:02:58 --> btdbFindBy ---> 
INFO - 2025-06-23 07:02:58 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 07:02:58 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
DEBUG - 2025-06-23 07:02:59 --> btdbFindBy ---> 
INFO - 2025-06-23 07:02:59 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 07:02:59 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-23 07:02:59 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 07:02:59 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-23 07:02:59 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2025-06-23 07:05:02 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 07:05:02 --> btdbFindBy ---> 
DEBUG - 2025-06-23 07:05:02 --> btdbFindBy ---> SELECT `start-plan`.*, `start-account`.*, DATE_FORMAT(resumed_at , "%M %e, %Y") as "resumed_at_formatted", `start-user`.`mode`, case when DATE_ADD(end_date, INTERVAL 1 DAY) < now() then 'yes' else 'no' end as 'expired'
FROM `start-account`
JOIN `start-user` ON `start-user`.`user_id` = `start-account`.`user_id`
JOIN `start-plan` ON `start-plan`.`plan_id` = `start-account`.`plan_id`
WHERE `start-account`.`user_id` = '2'
ORDER BY `start-account`.`end_date` DESC
DEBUG - 2025-06-23 07:05:03 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-23 07:05:03 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 07:05:03 --> REQ ----------->
INFO - 2025-06-23 07:05:03 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 07:05:05 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
DEBUG - 2025-06-23 07:05:05 --> btdbFindBy ---> 
INFO - 2025-06-23 07:05:05 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 07:05:05 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
DEBUG - 2025-06-23 07:05:05 --> btdbFindBy ---> 
INFO - 2025-06-23 07:05:05 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 07:05:05 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-23 07:05:05 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 07:05:05 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-23 07:05:05 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 07:06:19 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-23 07:06:19 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 07:06:19 --> REQ ----------->
INFO - 2025-06-23 07:06:19 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2025-06-23 07:06:20 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 07:06:20 --> btdbFindBy ---> 
DEBUG - 2025-06-23 07:06:20 --> btdbFindBy ---> SELECT `start-plan`.*, `start-account`.*, DATE_FORMAT(resumed_at , "%M %e, %Y") as "resumed_at_formatted", `start-user`.`mode`, case when DATE_ADD(end_date, INTERVAL 1 DAY) < now() then 'yes' else 'no' end as 'expired'
FROM `start-account`
JOIN `start-user` ON `start-user`.`user_id` = `start-account`.`user_id`
JOIN `start-plan` ON `start-plan`.`plan_id` = `start-account`.`plan_id`
WHERE `start-account`.`user_id` = '2'
ORDER BY `start-account`.`end_date` DESC
DEBUG - 2025-06-23 07:06:21 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-23 07:06:21 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 07:06:22 --> REQ ----------->
INFO - 2025-06-23 07:06:22 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 07:06:24 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
DEBUG - 2025-06-23 07:06:24 --> btdbFindBy ---> 
INFO - 2025-06-23 07:06:24 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 07:06:24 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
DEBUG - 2025-06-23 07:06:24 --> btdbFindBy ---> 
INFO - 2025-06-23 07:06:24 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 07:06:24 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-23 07:06:24 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 07:06:24 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-23 07:06:24 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 07:08:03 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-23 07:08:03 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 07:08:03 --> REQ ----------->
INFO - 2025-06-23 07:08:03 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2025-06-23 07:08:06 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 07:08:06 --> btdbFindBy ---> 
DEBUG - 2025-06-23 07:08:06 --> btdbFindBy ---> SELECT `start-plan`.*, `start-account`.*, DATE_FORMAT(resumed_at , "%M %e, %Y") as "resumed_at_formatted", `start-user`.`mode`, case when DATE_ADD(end_date, INTERVAL 1 DAY) < now() then 'yes' else 'no' end as 'expired'
FROM `start-account`
JOIN `start-user` ON `start-user`.`user_id` = `start-account`.`user_id`
JOIN `start-plan` ON `start-plan`.`plan_id` = `start-account`.`plan_id`
WHERE `start-account`.`user_id` = '2'
ORDER BY `start-account`.`end_date` DESC
DEBUG - 2025-06-23 07:08:07 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-23 07:08:07 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 07:08:07 --> REQ ----------->
INFO - 2025-06-23 07:08:07 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 07:08:09 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
DEBUG - 2025-06-23 07:08:09 --> btdbFindBy ---> 
INFO - 2025-06-23 07:08:09 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 07:08:10 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
DEBUG - 2025-06-23 07:08:10 --> btdbFindBy ---> 
INFO - 2025-06-23 07:08:10 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 07:08:10 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-23 07:08:10 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 07:08:10 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-23 07:08:10 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 07:10:08 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-23 07:10:08 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 07:10:08 --> REQ ----------->
INFO - 2025-06-23 07:10:08 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 07:10:10 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-23 07:10:10 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2025-06-23 07:10:10 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 07:10:10 --> btdbFindBy ---> 
DEBUG - 2025-06-23 07:10:10 --> btdbFindBy ---> SELECT `start-plan`.*, `start-account`.*, DATE_FORMAT(resumed_at , "%M %e, %Y") as "resumed_at_formatted", `start-user`.`mode`, case when DATE_ADD(end_date, INTERVAL 1 DAY) < now() then 'yes' else 'no' end as 'expired'
FROM `start-account`
JOIN `start-user` ON `start-user`.`user_id` = `start-account`.`user_id`
JOIN `start-plan` ON `start-plan`.`plan_id` = `start-account`.`plan_id`
WHERE `start-account`.`user_id` = '2'
ORDER BY `start-account`.`end_date` DESC
DEBUG - 2025-06-23 07:10:11 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-23 07:10:11 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 07:10:11 --> REQ ----------->
INFO - 2025-06-23 07:10:11 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 07:10:13 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
DEBUG - 2025-06-23 07:10:13 --> btdbFindBy ---> 
INFO - 2025-06-23 07:10:13 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 07:10:13 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
DEBUG - 2025-06-23 07:10:13 --> btdbFindBy ---> 
INFO - 2025-06-23 07:10:13 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 07:10:13 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-23 07:10:13 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 07:10:14 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-23 07:10:14 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 07:10:20 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-23 07:10:20 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 07:10:20 --> REQ ----------->
INFO - 2025-06-23 07:10:20 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2025-06-23 07:10:22 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 07:10:22 --> btdbFindBy ---> 
DEBUG - 2025-06-23 07:10:22 --> btdbFindBy ---> SELECT `start-plan`.*, `start-account`.*, DATE_FORMAT(resumed_at , "%M %e, %Y") as "resumed_at_formatted", `start-user`.`mode`, case when DATE_ADD(end_date, INTERVAL 1 DAY) < now() then 'yes' else 'no' end as 'expired'
FROM `start-account`
JOIN `start-user` ON `start-user`.`user_id` = `start-account`.`user_id`
JOIN `start-plan` ON `start-plan`.`plan_id` = `start-account`.`plan_id`
WHERE `start-account`.`user_id` = '2'
ORDER BY `start-account`.`end_date` DESC
DEBUG - 2025-06-23 07:10:23 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-23 07:10:23 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 07:10:23 --> REQ ----------->
INFO - 2025-06-23 07:10:23 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 07:10:25 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
DEBUG - 2025-06-23 07:10:25 --> btdbFindBy ---> 
INFO - 2025-06-23 07:10:25 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 07:10:25 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
DEBUG - 2025-06-23 07:10:25 --> btdbFindBy ---> 
INFO - 2025-06-23 07:10:25 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 07:10:25 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-23 07:10:25 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 07:10:26 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-23 07:10:26 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 07:10:27 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-23 07:10:27 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 07:10:28 --> REQ ----------->
INFO - 2025-06-23 07:10:28 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2025-06-23 07:10:52 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 07:10:52 --> btdbFindBy ---> 
DEBUG - 2025-06-23 07:10:52 --> btdbFindBy ---> SELECT `start-plan`.*, `start-account`.*, DATE_FORMAT(resumed_at , "%M %e, %Y") as "resumed_at_formatted", `start-user`.`mode`, case when DATE_ADD(end_date, INTERVAL 1 DAY) < now() then 'yes' else 'no' end as 'expired'
FROM `start-account`
JOIN `start-user` ON `start-user`.`user_id` = `start-account`.`user_id`
JOIN `start-plan` ON `start-plan`.`plan_id` = `start-account`.`plan_id`
WHERE `start-account`.`user_id` = '2'
ORDER BY `start-account`.`end_date` DESC
DEBUG - 2025-06-23 07:10:53 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-23 07:10:53 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 07:10:53 --> REQ ----------->
INFO - 2025-06-23 07:10:53 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 07:10:55 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
DEBUG - 2025-06-23 07:10:55 --> btdbFindBy ---> 
INFO - 2025-06-23 07:10:55 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 07:10:56 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
DEBUG - 2025-06-23 07:10:56 --> btdbFindBy ---> 
INFO - 2025-06-23 07:10:56 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 07:10:56 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-23 07:10:56 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 07:10:56 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-23 07:10:56 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 07:11:17 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-23 07:11:17 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 07:11:17 --> REQ ----------->
INFO - 2025-06-23 07:11:17 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 07:11:37 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-23 07:11:37 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 07:11:37 --> REQ ----------->
INFO - 2025-06-23 07:11:37 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 07:12:20 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-23 07:12:20 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 07:12:20 --> REQ ----------->
INFO - 2025-06-23 07:12:20 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2025-06-23 07:12:22 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 07:12:22 --> btdbFindBy ---> 
DEBUG - 2025-06-23 07:12:22 --> btdbFindBy ---> SELECT `start-plan`.*, `start-account`.*, DATE_FORMAT(resumed_at , "%M %e, %Y") as "resumed_at_formatted", `start-user`.`mode`, case when DATE_ADD(end_date, INTERVAL 1 DAY) < now() then 'yes' else 'no' end as 'expired'
FROM `start-account`
JOIN `start-user` ON `start-user`.`user_id` = `start-account`.`user_id`
JOIN `start-plan` ON `start-plan`.`plan_id` = `start-account`.`plan_id`
WHERE `start-account`.`user_id` = '2'
ORDER BY `start-account`.`end_date` DESC
DEBUG - 2025-06-23 07:12:23 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-23 07:12:23 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 07:12:23 --> REQ ----------->
INFO - 2025-06-23 07:12:23 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 07:12:26 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
DEBUG - 2025-06-23 07:12:26 --> btdbFindBy ---> 
INFO - 2025-06-23 07:12:26 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 07:12:26 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
DEBUG - 2025-06-23 07:12:26 --> btdbFindBy ---> 
INFO - 2025-06-23 07:12:26 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 07:12:26 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-23 07:12:26 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 07:12:26 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-23 07:12:26 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 07:12:28 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-23 07:12:28 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 07:12:28 --> REQ ----------->
INFO - 2025-06-23 07:12:28 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 07:13:42 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-23 07:13:42 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 07:13:42 --> REQ ----------->
INFO - 2025-06-23 07:13:42 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 07:15:25 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-23 07:15:25 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 07:15:25 --> REQ ----------->
INFO - 2025-06-23 07:15:25 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 07:15:41 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-23 07:15:41 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 07:15:41 --> REQ ----------->
INFO - 2025-06-23 07:15:41 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 07:16:09 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-23 07:16:09 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 07:16:09 --> REQ ----------->
INFO - 2025-06-23 07:16:09 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 07:17:37 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-23 07:17:37 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 07:17:37 --> REQ ----------->
INFO - 2025-06-23 07:17:37 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2025-06-23 07:17:39 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 07:17:39 --> btdbFindBy ---> 
DEBUG - 2025-06-23 07:17:39 --> btdbFindBy ---> SELECT `start-plan`.*, `start-account`.*, DATE_FORMAT(resumed_at , "%M %e, %Y") as "resumed_at_formatted", `start-user`.`mode`, case when DATE_ADD(end_date, INTERVAL 1 DAY) < now() then 'yes' else 'no' end as 'expired'
FROM `start-account`
JOIN `start-user` ON `start-user`.`user_id` = `start-account`.`user_id`
JOIN `start-plan` ON `start-plan`.`plan_id` = `start-account`.`plan_id`
WHERE `start-account`.`user_id` = '2'
ORDER BY `start-account`.`end_date` DESC
DEBUG - 2025-06-23 07:17:40 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-23 07:17:40 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 07:17:41 --> REQ ----------->
INFO - 2025-06-23 07:17:41 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 07:17:42 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
DEBUG - 2025-06-23 07:17:42 --> btdbFindBy ---> 
INFO - 2025-06-23 07:17:42 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 07:17:43 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
DEBUG - 2025-06-23 07:17:43 --> btdbFindBy ---> 
INFO - 2025-06-23 07:17:43 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 07:17:43 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-23 07:17:43 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 07:17:43 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-23 07:17:43 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 07:17:45 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-23 07:17:45 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 07:17:45 --> REQ ----------->
INFO - 2025-06-23 07:17:45 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 07:18:48 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-23 07:18:48 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 07:18:48 --> REQ ----------->
INFO - 2025-06-23 07:18:48 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 07:19:05 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-23 07:19:05 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 07:19:05 --> REQ ----------->
INFO - 2025-06-23 07:19:05 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2025-06-23 07:19:08 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 07:19:08 --> btdbFindBy ---> 
DEBUG - 2025-06-23 07:19:08 --> btdbFindBy ---> SELECT `start-plan`.*, `start-account`.*, DATE_FORMAT(resumed_at , "%M %e, %Y") as "resumed_at_formatted", `start-user`.`mode`, case when DATE_ADD(end_date, INTERVAL 1 DAY) < now() then 'yes' else 'no' end as 'expired'
FROM `start-account`
JOIN `start-user` ON `start-user`.`user_id` = `start-account`.`user_id`
JOIN `start-plan` ON `start-plan`.`plan_id` = `start-account`.`plan_id`
WHERE `start-account`.`user_id` = '2'
ORDER BY `start-account`.`end_date` DESC
DEBUG - 2025-06-23 07:19:09 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-23 07:19:09 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 07:19:09 --> REQ ----------->
INFO - 2025-06-23 07:19:09 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 07:19:12 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
DEBUG - 2025-06-23 07:19:12 --> btdbFindBy ---> 
INFO - 2025-06-23 07:19:12 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 07:19:12 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
DEBUG - 2025-06-23 07:19:12 --> btdbFindBy ---> 
INFO - 2025-06-23 07:19:12 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 07:19:12 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-23 07:19:12 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 07:19:12 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-23 07:19:12 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2025-06-23 07:19:15 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 07:19:15 --> btdbFindBy ---> 
DEBUG - 2025-06-23 07:19:15 --> btdbFindBy ---> SELECT `start-plan`.*, `start-account`.*, DATE_FORMAT(resumed_at , "%M %e, %Y") as "resumed_at_formatted", `start-user`.`mode`, case when DATE_ADD(end_date, INTERVAL 1 DAY) < now() then 'yes' else 'no' end as 'expired'
FROM `start-account`
JOIN `start-user` ON `start-user`.`user_id` = `start-account`.`user_id`
JOIN `start-plan` ON `start-plan`.`plan_id` = `start-account`.`plan_id`
WHERE `start-account`.`user_id` = '2'
ORDER BY `start-account`.`end_date` DESC
DEBUG - 2025-06-23 07:19:16 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-23 07:19:16 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 07:19:16 --> REQ ----------->
INFO - 2025-06-23 07:19:16 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 07:19:18 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
DEBUG - 2025-06-23 07:19:18 --> btdbFindBy ---> 
INFO - 2025-06-23 07:19:18 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 07:19:18 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
DEBUG - 2025-06-23 07:19:18 --> btdbFindBy ---> 
INFO - 2025-06-23 07:19:18 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 07:19:18 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-23 07:19:18 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 07:19:19 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-23 07:19:19 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 07:21:27 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-23 07:21:27 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 07:21:27 --> REQ ----------->
INFO - 2025-06-23 07:21:27 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 07:21:33 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-23 07:21:33 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 07:21:33 --> REQ ----------->
INFO - 2025-06-23 07:21:33 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 07:21:52 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-23 07:21:52 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 07:21:52 --> REQ ----------->
INFO - 2025-06-23 07:21:52 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2025-06-23 07:21:55 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 07:21:55 --> btdbFindBy ---> 
DEBUG - 2025-06-23 07:21:55 --> btdbFindBy ---> SELECT `start-plan`.*, `start-account`.*, DATE_FORMAT(resumed_at , "%M %e, %Y") as "resumed_at_formatted", `start-user`.`mode`, case when DATE_ADD(end_date, INTERVAL 1 DAY) < now() then 'yes' else 'no' end as 'expired'
FROM `start-account`
JOIN `start-user` ON `start-user`.`user_id` = `start-account`.`user_id`
JOIN `start-plan` ON `start-plan`.`plan_id` = `start-account`.`plan_id`
WHERE `start-account`.`user_id` = '2'
ORDER BY `start-account`.`end_date` DESC
DEBUG - 2025-06-23 07:21:56 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-23 07:21:56 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 07:21:56 --> REQ ----------->
INFO - 2025-06-23 07:21:56 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 07:21:58 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
DEBUG - 2025-06-23 07:21:58 --> btdbFindBy ---> 
INFO - 2025-06-23 07:21:58 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 07:21:58 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
DEBUG - 2025-06-23 07:21:58 --> btdbFindBy ---> 
INFO - 2025-06-23 07:21:58 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 07:21:58 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-23 07:21:58 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 07:21:59 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-23 07:21:59 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 07:25:39 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-23 07:25:39 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 07:25:39 --> REQ ----------->
INFO - 2025-06-23 07:25:39 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 07:25:59 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-23 07:25:59 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 07:25:59 --> REQ ----------->
INFO - 2025-06-23 07:25:59 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2025-06-23 07:26:01 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 07:26:01 --> btdbFindBy ---> 
DEBUG - 2025-06-23 07:26:01 --> btdbFindBy ---> SELECT `start-plan`.*, `start-account`.*, DATE_FORMAT(resumed_at , "%M %e, %Y") as "resumed_at_formatted", `start-user`.`mode`, case when DATE_ADD(end_date, INTERVAL 1 DAY) < now() then 'yes' else 'no' end as 'expired'
FROM `start-account`
JOIN `start-user` ON `start-user`.`user_id` = `start-account`.`user_id`
JOIN `start-plan` ON `start-plan`.`plan_id` = `start-account`.`plan_id`
WHERE `start-account`.`user_id` = '2'
ORDER BY `start-account`.`end_date` DESC
DEBUG - 2025-06-23 07:26:02 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-23 07:26:02 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 07:26:02 --> REQ ----------->
INFO - 2025-06-23 07:26:02 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 07:26:05 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
DEBUG - 2025-06-23 07:26:05 --> btdbFindBy ---> 
INFO - 2025-06-23 07:26:05 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 07:26:05 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
DEBUG - 2025-06-23 07:26:05 --> btdbFindBy ---> 
INFO - 2025-06-23 07:26:05 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 07:26:05 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-23 07:26:05 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 07:26:05 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-23 07:26:05 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 07:27:06 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-23 07:27:06 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 07:27:06 --> REQ ----------->
INFO - 2025-06-23 07:27:06 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2025-06-23 07:27:28 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 07:27:28 --> btdbFindBy ---> 
DEBUG - 2025-06-23 07:27:28 --> btdbFindBy ---> SELECT `start-plan`.*, `start-account`.*, DATE_FORMAT(resumed_at , "%M %e, %Y") as "resumed_at_formatted", `start-user`.`mode`, case when DATE_ADD(end_date, INTERVAL 1 DAY) < now() then 'yes' else 'no' end as 'expired'
FROM `start-account`
JOIN `start-user` ON `start-user`.`user_id` = `start-account`.`user_id`
JOIN `start-plan` ON `start-plan`.`plan_id` = `start-account`.`plan_id`
WHERE `start-account`.`user_id` = '2'
ORDER BY `start-account`.`end_date` DESC
DEBUG - 2025-06-23 07:27:29 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-23 07:27:29 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 07:27:29 --> REQ ----------->
INFO - 2025-06-23 07:27:29 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 07:27:31 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
DEBUG - 2025-06-23 07:27:31 --> btdbFindBy ---> 
INFO - 2025-06-23 07:27:31 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 07:27:31 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
DEBUG - 2025-06-23 07:27:31 --> btdbFindBy ---> 
INFO - 2025-06-23 07:27:31 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 07:27:32 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-23 07:27:32 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 07:27:32 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-23 07:27:32 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 07:30:09 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-23 07:30:09 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 07:30:09 --> REQ ----------->
INFO - 2025-06-23 07:30:09 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 07:30:12 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-23 07:30:12 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2025-06-23 07:30:12 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 07:30:12 --> btdbFindBy ---> 
DEBUG - 2025-06-23 07:30:12 --> btdbFindBy ---> SELECT `start-plan`.*, `start-account`.*, DATE_FORMAT(resumed_at , "%M %e, %Y") as "resumed_at_formatted", `start-user`.`mode`, case when DATE_ADD(end_date, INTERVAL 1 DAY) < now() then 'yes' else 'no' end as 'expired'
FROM `start-account`
JOIN `start-user` ON `start-user`.`user_id` = `start-account`.`user_id`
JOIN `start-plan` ON `start-plan`.`plan_id` = `start-account`.`plan_id`
WHERE `start-account`.`user_id` = '2'
ORDER BY `start-account`.`end_date` DESC
DEBUG - 2025-06-23 07:30:13 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-23 07:30:13 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 07:30:13 --> REQ ----------->
INFO - 2025-06-23 07:30:13 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 07:30:15 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
DEBUG - 2025-06-23 07:30:15 --> btdbFindBy ---> 
INFO - 2025-06-23 07:30:15 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 07:30:15 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
DEBUG - 2025-06-23 07:30:15 --> btdbFindBy ---> 
INFO - 2025-06-23 07:30:15 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 07:30:15 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-23 07:30:15 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 07:30:15 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-23 07:30:15 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 07:32:23 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-23 07:32:23 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 07:32:23 --> REQ ----------->
INFO - 2025-06-23 07:32:23 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2025-06-23 07:32:46 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 07:32:46 --> btdbFindBy ---> 
DEBUG - 2025-06-23 07:32:46 --> btdbFindBy ---> SELECT `start-plan`.*, `start-account`.*, DATE_FORMAT(resumed_at , "%M %e, %Y") as "resumed_at_formatted", `start-user`.`mode`, case when DATE_ADD(end_date, INTERVAL 1 DAY) < now() then 'yes' else 'no' end as 'expired'
FROM `start-account`
JOIN `start-user` ON `start-user`.`user_id` = `start-account`.`user_id`
JOIN `start-plan` ON `start-plan`.`plan_id` = `start-account`.`plan_id`
WHERE `start-account`.`user_id` = '2'
ORDER BY `start-account`.`end_date` DESC
DEBUG - 2025-06-23 07:32:47 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-23 07:32:47 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 07:32:47 --> REQ ----------->
INFO - 2025-06-23 07:32:47 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 07:32:49 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
DEBUG - 2025-06-23 07:32:49 --> btdbFindBy ---> 
INFO - 2025-06-23 07:32:49 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 07:32:49 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
DEBUG - 2025-06-23 07:32:49 --> btdbFindBy ---> 
INFO - 2025-06-23 07:32:49 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 07:32:49 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-23 07:32:49 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 07:32:49 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-23 07:32:49 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 07:34:23 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-23 07:34:23 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 07:34:23 --> REQ ----------->
INFO - 2025-06-23 07:34:23 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2025-06-23 07:34:36 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 07:34:36 --> btdbFindBy ---> 
DEBUG - 2025-06-23 07:34:36 --> btdbFindBy ---> SELECT `start-plan`.*, `start-account`.*, DATE_FORMAT(resumed_at , "%M %e, %Y") as "resumed_at_formatted", `start-user`.`mode`, case when DATE_ADD(end_date, INTERVAL 1 DAY) < now() then 'yes' else 'no' end as 'expired'
FROM `start-account`
JOIN `start-user` ON `start-user`.`user_id` = `start-account`.`user_id`
JOIN `start-plan` ON `start-plan`.`plan_id` = `start-account`.`plan_id`
WHERE `start-account`.`user_id` = '2'
ORDER BY `start-account`.`end_date` DESC
DEBUG - 2025-06-23 07:34:37 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-23 07:34:37 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 07:34:37 --> REQ ----------->
INFO - 2025-06-23 07:34:37 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 07:34:40 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
DEBUG - 2025-06-23 07:34:40 --> btdbFindBy ---> 
INFO - 2025-06-23 07:34:40 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 07:34:40 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
DEBUG - 2025-06-23 07:34:40 --> btdbFindBy ---> 
INFO - 2025-06-23 07:34:40 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 07:34:40 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-23 07:34:40 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 07:34:40 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-23 07:34:40 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 07:39:48 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-23 07:39:48 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 07:39:48 --> REQ ----------->
INFO - 2025-06-23 07:39:48 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 07:48:35 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-23 07:48:35 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 07:48:35 --> REQ ----------->
INFO - 2025-06-23 07:48:35 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 07:48:38 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-23 07:48:38 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2025-06-23 07:48:38 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 07:48:38 --> btdbFindBy ---> 
DEBUG - 2025-06-23 07:48:38 --> btdbFindBy ---> SELECT `start-plan`.*, `start-account`.*, DATE_FORMAT(resumed_at , "%M %e, %Y") as "resumed_at_formatted", `start-user`.`mode`, case when DATE_ADD(end_date, INTERVAL 1 DAY) < now() then 'yes' else 'no' end as 'expired'
FROM `start-account`
JOIN `start-user` ON `start-user`.`user_id` = `start-account`.`user_id`
JOIN `start-plan` ON `start-plan`.`plan_id` = `start-account`.`plan_id`
WHERE `start-account`.`user_id` = '2'
ORDER BY `start-account`.`end_date` DESC
DEBUG - 2025-06-23 07:48:38 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-23 07:48:38 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 07:48:39 --> REQ ----------->
INFO - 2025-06-23 07:48:39 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 07:48:41 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
DEBUG - 2025-06-23 07:48:41 --> btdbFindBy ---> 
INFO - 2025-06-23 07:48:41 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 07:48:41 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
DEBUG - 2025-06-23 07:48:41 --> btdbFindBy ---> 
INFO - 2025-06-23 07:48:41 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 07:48:41 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-23 07:48:42 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 07:48:42 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-23 07:48:42 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 07:48:47 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-23 07:48:47 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 07:48:47 --> REQ ----------->
INFO - 2025-06-23 07:48:47 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2025-06-23 07:48:52 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 07:48:52 --> btdbFindBy ---> 
DEBUG - 2025-06-23 07:48:52 --> btdbFindBy ---> SELECT `start-plan`.*, `start-account`.*, DATE_FORMAT(resumed_at , "%M %e, %Y") as "resumed_at_formatted", `start-user`.`mode`, case when DATE_ADD(end_date, INTERVAL 1 DAY) < now() then 'yes' else 'no' end as 'expired'
FROM `start-account`
JOIN `start-user` ON `start-user`.`user_id` = `start-account`.`user_id`
JOIN `start-plan` ON `start-plan`.`plan_id` = `start-account`.`plan_id`
WHERE `start-account`.`user_id` = '2'
ORDER BY `start-account`.`end_date` DESC
DEBUG - 2025-06-23 07:48:53 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-23 07:48:53 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 07:48:53 --> REQ ----------->
INFO - 2025-06-23 07:48:53 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 07:48:56 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
DEBUG - 2025-06-23 07:48:56 --> btdbFindBy ---> 
INFO - 2025-06-23 07:48:56 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 07:48:56 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
DEBUG - 2025-06-23 07:48:56 --> btdbFindBy ---> 
INFO - 2025-06-23 07:48:56 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 07:48:56 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-23 07:48:56 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 07:48:56 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-23 07:48:56 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 07:51:02 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-23 07:51:02 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 07:51:02 --> REQ ----------->
INFO - 2025-06-23 07:51:02 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 07:52:10 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-23 07:52:10 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 07:52:10 --> REQ ----------->
INFO - 2025-06-23 07:52:10 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 07:53:16 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-23 07:53:16 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 07:53:16 --> REQ ----------->
INFO - 2025-06-23 07:53:16 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2025-06-23 07:53:18 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 07:53:18 --> btdbFindBy ---> 
DEBUG - 2025-06-23 07:53:18 --> btdbFindBy ---> SELECT `start-plan`.*, `start-account`.*, DATE_FORMAT(resumed_at , "%M %e, %Y") as "resumed_at_formatted", `start-user`.`mode`, case when DATE_ADD(end_date, INTERVAL 1 DAY) < now() then 'yes' else 'no' end as 'expired'
FROM `start-account`
JOIN `start-user` ON `start-user`.`user_id` = `start-account`.`user_id`
JOIN `start-plan` ON `start-plan`.`plan_id` = `start-account`.`plan_id`
WHERE `start-account`.`user_id` = '2'
ORDER BY `start-account`.`end_date` DESC
DEBUG - 2025-06-23 07:53:19 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-23 07:53:19 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 07:53:19 --> REQ ----------->
INFO - 2025-06-23 07:53:19 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 07:53:21 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
DEBUG - 2025-06-23 07:53:21 --> btdbFindBy ---> 
INFO - 2025-06-23 07:53:21 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 07:53:21 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
DEBUG - 2025-06-23 07:53:21 --> btdbFindBy ---> 
INFO - 2025-06-23 07:53:21 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 07:53:21 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-23 07:53:21 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 07:53:22 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-23 07:53:22 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 07:53:56 --> REQ ----------->
INFO - 2025-06-23 07:53:56 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 07:53:56 --> REQ ----------->
INFO - 2025-06-23 07:53:56 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 07:53:59 --> REQ ----------->
INFO - 2025-06-23 07:53:59 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 07:53:59 --> REQ ----------->
INFO - 2025-06-23 07:53:59 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 07:54:03 --> REQ ----------->testuser061125_02@mailinator.com123123
DEBUG - 2025-06-23 07:54:03 --> btdbFindBy ---> 
DEBUG - 2025-06-23 07:54:03 --> btdbFindBy ---> SELECT *
FROM `start-user`
WHERE `email` = '<EMAIL>'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
INFO - 2025-06-23 07:54:03 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 07:54:03 --> btdbFindBy ---> SELECT `start-plan`.*, `start-account`.*, DATE_FORMAT(resumed_at , "%M %e, %Y") as "resumed_at_formatted", `start-user`.`mode`, case when DATE_ADD(end_date, INTERVAL 1 DAY) < now() then 'yes' else 'no' end as 'expired'
FROM `start-account`
JOIN `start-user` ON `start-user`.`user_id` = `start-account`.`user_id`
JOIN `start-plan` ON `start-plan`.`plan_id` = `start-account`.`plan_id`
WHERE `start-account`.`user_id` = '3'
ORDER BY `start-account`.`end_date` DESC
DEBUG - 2025-06-23 07:54:03 --> btdbFindBy ---> SELECT *
FROM `start-plan`
WHERE `plan_id` = '144'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-23 07:54:03 --> login-------> <EMAIL>
DEBUG - 2025-06-23 07:54:03 --> btdbFindBy ---> SELECT *
FROM `start-user_app`
WHERE `email` = '<EMAIL>'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
INFO - 2025-06-23 07:54:03 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 07:54:03 --> btdbFindBy ---> 
DEBUG - 2025-06-23 07:54:03 --> btdbFindBy ---> SELECT `start-plan`.*, `start-account`.*, DATE_FORMAT(resumed_at , "%M %e, %Y") as "resumed_at_formatted", `start-user`.`mode`, case when DATE_ADD(end_date, INTERVAL 1 DAY) < now() then 'yes' else 'no' end as 'expired'
FROM `start-account`
JOIN `start-user` ON `start-user`.`user_id` = `start-account`.`user_id`
JOIN `start-plan` ON `start-plan`.`plan_id` = `start-account`.`plan_id`
WHERE `start-account`.`user_id` = '3'
ORDER BY `start-account`.`end_date` DESC
DEBUG - 2025-06-23 07:54:03 --> btdbFindBy ---> SELECT *
FROM `start-plan`
WHERE `plan_id` = '144'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-23 07:54:03 --> REQ ----------->$2y$09$5NQl9xDYdIbEUIpx946W9uSLz
INFO - 2025-06-23 07:54:03 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 07:54:04 --> REQ ----------->
INFO - 2025-06-23 07:54:04 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 07:54:07 --> REQ ----------->
DEBUG - 2025-06-23 07:54:07 --> REQ ----------->$2y$09$5NQl9xDYdIbEUIpx946W9uSLz
DEBUG - 2025-06-23 07:54:07 --> btdbFindBy ---> 
INFO - 2025-06-23 07:54:07 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 07:54:07 --> REQ ----------->$2y$09$5NQl9xDYdIbEUIpx946W9uSLz
INFO - 2025-06-23 07:54:07 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 07:54:09 --> REQ ----------->$2y$09$5NQl9xDYdIbEUIpx946W9uSLz
INFO - 2025-06-23 07:54:09 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 07:54:09 --> REQ ----------->
INFO - 2025-06-23 07:54:09 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2025-06-23 07:54:12 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 07:54:12 --> btdbFindBy ---> 
DEBUG - 2025-06-23 07:54:12 --> btdbFindBy ---> SELECT `start-plan`.*, `start-account`.*, DATE_FORMAT(resumed_at , "%M %e, %Y") as "resumed_at_formatted", `start-user`.`mode`, case when DATE_ADD(end_date, INTERVAL 1 DAY) < now() then 'yes' else 'no' end as 'expired'
FROM `start-account`
JOIN `start-user` ON `start-user`.`user_id` = `start-account`.`user_id`
JOIN `start-plan` ON `start-plan`.`plan_id` = `start-account`.`plan_id`
WHERE `start-account`.`user_id` = '3'
ORDER BY `start-account`.`end_date` DESC
DEBUG - 2025-06-23 07:54:12 --> REQ ----------->$2y$09$5NQl9xDYdIbEUIpx946W9uSLz
INFO - 2025-06-23 07:54:12 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 07:54:12 --> REQ ----------->
INFO - 2025-06-23 07:54:12 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 07:54:14 --> REQ ----------->$2y$09$5NQl9xDYdIbEUIpx946W9uSLz
DEBUG - 2025-06-23 07:54:14 --> btdbFindBy ---> 
INFO - 2025-06-23 07:54:14 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 07:54:14 --> REQ ----------->$2y$09$5NQl9xDYdIbEUIpx946W9uSLz
DEBUG - 2025-06-23 07:54:14 --> btdbFindBy ---> 
INFO - 2025-06-23 07:54:14 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 07:54:14 --> REQ ----------->$2y$09$5NQl9xDYdIbEUIpx946W9uSLz
INFO - 2025-06-23 07:54:14 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 07:54:15 --> REQ ----------->$2y$09$5NQl9xDYdIbEUIpx946W9uSLz
INFO - 2025-06-23 07:54:15 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 07:54:31 --> REQ ----------->$2y$09$5NQl9xDYdIbEUIpx946W9uSLz
INFO - 2025-06-23 07:54:31 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 07:54:31 --> REQ ----------->
INFO - 2025-06-23 07:54:31 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2025-06-23 07:54:33 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 07:54:33 --> btdbFindBy ---> 
DEBUG - 2025-06-23 07:54:33 --> btdbFindBy ---> SELECT `start-plan`.*, `start-account`.*, DATE_FORMAT(resumed_at , "%M %e, %Y") as "resumed_at_formatted", `start-user`.`mode`, case when DATE_ADD(end_date, INTERVAL 1 DAY) < now() then 'yes' else 'no' end as 'expired'
FROM `start-account`
JOIN `start-user` ON `start-user`.`user_id` = `start-account`.`user_id`
JOIN `start-plan` ON `start-plan`.`plan_id` = `start-account`.`plan_id`
WHERE `start-account`.`user_id` = '3'
ORDER BY `start-account`.`end_date` DESC
DEBUG - 2025-06-23 07:54:34 --> REQ ----------->$2y$09$5NQl9xDYdIbEUIpx946W9uSLz
INFO - 2025-06-23 07:54:34 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 07:54:34 --> REQ ----------->
INFO - 2025-06-23 07:54:34 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 07:54:37 --> REQ ----------->$2y$09$5NQl9xDYdIbEUIpx946W9uSLz
DEBUG - 2025-06-23 07:54:37 --> btdbFindBy ---> 
INFO - 2025-06-23 07:54:37 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 07:54:37 --> REQ ----------->$2y$09$5NQl9xDYdIbEUIpx946W9uSLz
DEBUG - 2025-06-23 07:54:37 --> btdbFindBy ---> 
INFO - 2025-06-23 07:54:37 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 07:54:37 --> REQ ----------->$2y$09$5NQl9xDYdIbEUIpx946W9uSLz
INFO - 2025-06-23 07:54:37 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 07:54:37 --> REQ ----------->$2y$09$5NQl9xDYdIbEUIpx946W9uSLz
INFO - 2025-06-23 07:54:37 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 08:00:42 --> REQ ----------->$2y$09$5NQl9xDYdIbEUIpx946W9uSLz
INFO - 2025-06-23 08:00:42 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 08:00:42 --> REQ ----------->
INFO - 2025-06-23 08:00:42 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 08:04:06 --> REQ ----------->$2y$09$5NQl9xDYdIbEUIpx946W9uSLz
INFO - 2025-06-23 08:04:06 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 08:04:06 --> REQ ----------->
INFO - 2025-06-23 08:04:06 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 08:04:28 --> REQ ----------->$2y$09$5NQl9xDYdIbEUIpx946W9uSLz
INFO - 2025-06-23 08:04:28 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 08:04:28 --> REQ ----------->
INFO - 2025-06-23 08:04:28 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2025-06-23 08:04:30 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 08:04:30 --> btdbFindBy ---> 
DEBUG - 2025-06-23 08:04:30 --> btdbFindBy ---> SELECT `start-plan`.*, `start-account`.*, DATE_FORMAT(resumed_at , "%M %e, %Y") as "resumed_at_formatted", `start-user`.`mode`, case when DATE_ADD(end_date, INTERVAL 1 DAY) < now() then 'yes' else 'no' end as 'expired'
FROM `start-account`
JOIN `start-user` ON `start-user`.`user_id` = `start-account`.`user_id`
JOIN `start-plan` ON `start-plan`.`plan_id` = `start-account`.`plan_id`
WHERE `start-account`.`user_id` = '3'
ORDER BY `start-account`.`end_date` DESC
DEBUG - 2025-06-23 08:04:32 --> REQ ----------->$2y$09$5NQl9xDYdIbEUIpx946W9uSLz
INFO - 2025-06-23 08:04:32 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 08:04:32 --> REQ ----------->
INFO - 2025-06-23 08:04:32 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 08:04:33 --> REQ ----------->$2y$09$5NQl9xDYdIbEUIpx946W9uSLz
DEBUG - 2025-06-23 08:04:33 --> btdbFindBy ---> 
INFO - 2025-06-23 08:04:33 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 08:04:34 --> REQ ----------->$2y$09$5NQl9xDYdIbEUIpx946W9uSLz
DEBUG - 2025-06-23 08:04:34 --> btdbFindBy ---> 
INFO - 2025-06-23 08:04:34 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 08:04:34 --> REQ ----------->$2y$09$5NQl9xDYdIbEUIpx946W9uSLz
INFO - 2025-06-23 08:04:34 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 08:04:34 --> REQ ----------->$2y$09$5NQl9xDYdIbEUIpx946W9uSLz
INFO - 2025-06-23 08:04:34 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 08:05:55 --> REQ ----------->$2y$09$5NQl9xDYdIbEUIpx946W9uSLz
INFO - 2025-06-23 08:05:55 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 08:05:55 --> REQ ----------->
INFO - 2025-06-23 08:05:55 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2025-06-23 08:05:58 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 08:05:58 --> btdbFindBy ---> 
DEBUG - 2025-06-23 08:05:58 --> btdbFindBy ---> SELECT `start-plan`.*, `start-account`.*, DATE_FORMAT(resumed_at , "%M %e, %Y") as "resumed_at_formatted", `start-user`.`mode`, case when DATE_ADD(end_date, INTERVAL 1 DAY) < now() then 'yes' else 'no' end as 'expired'
FROM `start-account`
JOIN `start-user` ON `start-user`.`user_id` = `start-account`.`user_id`
JOIN `start-plan` ON `start-plan`.`plan_id` = `start-account`.`plan_id`
WHERE `start-account`.`user_id` = '3'
ORDER BY `start-account`.`end_date` DESC
DEBUG - 2025-06-23 08:05:59 --> REQ ----------->$2y$09$5NQl9xDYdIbEUIpx946W9uSLz
INFO - 2025-06-23 08:05:59 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 08:05:59 --> REQ ----------->
INFO - 2025-06-23 08:05:59 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 08:06:01 --> REQ ----------->$2y$09$5NQl9xDYdIbEUIpx946W9uSLz
DEBUG - 2025-06-23 08:06:01 --> btdbFindBy ---> 
INFO - 2025-06-23 08:06:01 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 08:06:02 --> REQ ----------->$2y$09$5NQl9xDYdIbEUIpx946W9uSLz
DEBUG - 2025-06-23 08:06:02 --> btdbFindBy ---> 
INFO - 2025-06-23 08:06:02 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 08:06:02 --> REQ ----------->$2y$09$5NQl9xDYdIbEUIpx946W9uSLz
INFO - 2025-06-23 08:06:02 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 08:06:02 --> REQ ----------->$2y$09$5NQl9xDYdIbEUIpx946W9uSLz
INFO - 2025-06-23 08:06:02 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 08:06:07 --> REQ ----------->$2y$09$5NQl9xDYdIbEUIpx946W9uSLz
INFO - 2025-06-23 08:06:07 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 08:06:07 --> REQ ----------->
INFO - 2025-06-23 08:06:07 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 08:06:52 --> REQ ----------->$2y$09$5NQl9xDYdIbEUIpx946W9uSLz
INFO - 2025-06-23 08:06:52 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 08:06:52 --> REQ ----------->
INFO - 2025-06-23 08:06:52 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2025-06-23 08:07:10 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 08:07:10 --> btdbFindBy ---> 
DEBUG - 2025-06-23 08:07:11 --> REQ ----------->
INFO - 2025-06-23 08:07:11 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 08:07:11 --> REQ ----------->
INFO - 2025-06-23 08:07:11 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 08:07:15 --> REQ ----------->testuser061125_01@mailinator.com123123
DEBUG - 2025-06-23 08:07:15 --> btdbFindBy ---> 
DEBUG - 2025-06-23 08:07:15 --> btdbFindBy ---> SELECT *
FROM `start-user`
WHERE `email` = '<EMAIL>'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
INFO - 2025-06-23 08:07:15 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 08:07:15 --> btdbFindBy ---> SELECT `start-plan`.*, `start-account`.*, DATE_FORMAT(resumed_at , "%M %e, %Y") as "resumed_at_formatted", `start-user`.`mode`, case when DATE_ADD(end_date, INTERVAL 1 DAY) < now() then 'yes' else 'no' end as 'expired'
FROM `start-account`
JOIN `start-user` ON `start-user`.`user_id` = `start-account`.`user_id`
JOIN `start-plan` ON `start-plan`.`plan_id` = `start-account`.`plan_id`
WHERE `start-account`.`user_id` = '2'
ORDER BY `start-account`.`end_date` DESC
DEBUG - 2025-06-23 08:07:15 --> btdbFindBy ---> SELECT *
FROM `start-plan`
WHERE `plan_id` = '143'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-23 08:07:15 --> login-------> <EMAIL>
DEBUG - 2025-06-23 08:07:15 --> btdbFindBy ---> SELECT *
FROM `start-user_app`
WHERE `email` = '<EMAIL>'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
INFO - 2025-06-23 08:07:15 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 08:07:15 --> btdbFindBy ---> 
DEBUG - 2025-06-23 08:07:15 --> btdbFindBy ---> SELECT `start-plan`.*, `start-account`.*, DATE_FORMAT(resumed_at , "%M %e, %Y") as "resumed_at_formatted", `start-user`.`mode`, case when DATE_ADD(end_date, INTERVAL 1 DAY) < now() then 'yes' else 'no' end as 'expired'
FROM `start-account`
JOIN `start-user` ON `start-user`.`user_id` = `start-account`.`user_id`
JOIN `start-plan` ON `start-plan`.`plan_id` = `start-account`.`plan_id`
WHERE `start-account`.`user_id` = '2'
ORDER BY `start-account`.`end_date` DESC
DEBUG - 2025-06-23 08:07:15 --> btdbFindBy ---> SELECT *
FROM `start-plan`
WHERE `plan_id` = '143'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-23 08:07:16 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-23 08:07:16 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 08:07:16 --> REQ ----------->
INFO - 2025-06-23 08:07:16 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 08:07:19 --> REQ ----------->
DEBUG - 2025-06-23 08:07:19 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
DEBUG - 2025-06-23 08:07:19 --> btdbFindBy ---> 
INFO - 2025-06-23 08:07:19 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2025-06-23 08:07:19 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 08:07:19 --> btdbFindBy ---> 
DEBUG - 2025-06-23 08:07:19 --> btdbFindBy ---> SELECT `start-plan`.*, `start-account`.*, DATE_FORMAT(resumed_at , "%M %e, %Y") as "resumed_at_formatted", `start-user`.`mode`, case when DATE_ADD(end_date, INTERVAL 1 DAY) < now() then 'yes' else 'no' end as 'expired'
FROM `start-account`
JOIN `start-user` ON `start-user`.`user_id` = `start-account`.`user_id`
JOIN `start-plan` ON `start-plan`.`plan_id` = `start-account`.`plan_id`
WHERE `start-account`.`user_id` = '2'
ORDER BY `start-account`.`end_date` DESC
DEBUG - 2025-06-23 08:07:20 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-23 08:07:20 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 08:07:20 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-23 08:07:20 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 08:07:20 --> REQ ----------->
INFO - 2025-06-23 08:07:20 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 08:07:23 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
DEBUG - 2025-06-23 08:07:23 --> btdbFindBy ---> 
INFO - 2025-06-23 08:07:23 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 08:07:23 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
DEBUG - 2025-06-23 08:07:23 --> btdbFindBy ---> 
INFO - 2025-06-23 08:07:23 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 08:07:23 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-23 08:07:23 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 08:07:24 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-23 08:07:24 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 08:07:34 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-23 08:07:34 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 08:07:35 --> REQ ----------->
INFO - 2025-06-23 08:07:35 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2025-06-23 08:07:36 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 08:07:36 --> btdbFindBy ---> 
DEBUG - 2025-06-23 08:07:36 --> btdbFindBy ---> SELECT `start-plan`.*, `start-account`.*, DATE_FORMAT(resumed_at , "%M %e, %Y") as "resumed_at_formatted", `start-user`.`mode`, case when DATE_ADD(end_date, INTERVAL 1 DAY) < now() then 'yes' else 'no' end as 'expired'
FROM `start-account`
JOIN `start-user` ON `start-user`.`user_id` = `start-account`.`user_id`
JOIN `start-plan` ON `start-plan`.`plan_id` = `start-account`.`plan_id`
WHERE `start-account`.`user_id` = '2'
ORDER BY `start-account`.`end_date` DESC
DEBUG - 2025-06-23 08:07:37 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-23 08:07:37 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 08:07:37 --> REQ ----------->
INFO - 2025-06-23 08:07:37 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 08:07:39 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
DEBUG - 2025-06-23 08:07:39 --> btdbFindBy ---> 
INFO - 2025-06-23 08:07:39 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 08:07:39 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
DEBUG - 2025-06-23 08:07:39 --> btdbFindBy ---> 
INFO - 2025-06-23 08:07:40 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 08:07:40 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-23 08:07:40 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 08:07:40 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-23 08:07:40 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 08:08:55 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-23 08:08:55 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 08:08:55 --> REQ ----------->
INFO - 2025-06-23 08:08:55 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 08:08:57 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-23 08:08:57 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2025-06-23 08:08:57 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 08:08:57 --> btdbFindBy ---> 
DEBUG - 2025-06-23 08:08:57 --> btdbFindBy ---> SELECT `start-plan`.*, `start-account`.*, DATE_FORMAT(resumed_at , "%M %e, %Y") as "resumed_at_formatted", `start-user`.`mode`, case when DATE_ADD(end_date, INTERVAL 1 DAY) < now() then 'yes' else 'no' end as 'expired'
FROM `start-account`
JOIN `start-user` ON `start-user`.`user_id` = `start-account`.`user_id`
JOIN `start-plan` ON `start-plan`.`plan_id` = `start-account`.`plan_id`
WHERE `start-account`.`user_id` = '2'
ORDER BY `start-account`.`end_date` DESC
DEBUG - 2025-06-23 08:08:57 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-23 08:08:57 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 08:08:57 --> REQ ----------->
INFO - 2025-06-23 08:08:57 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 08:09:00 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
DEBUG - 2025-06-23 08:09:00 --> btdbFindBy ---> 
INFO - 2025-06-23 08:09:00 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 08:09:00 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
DEBUG - 2025-06-23 08:09:00 --> btdbFindBy ---> 
INFO - 2025-06-23 08:09:00 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2025-06-23 08:09:00 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 08:09:00 --> btdbFindBy ---> 
DEBUG - 2025-06-23 08:09:00 --> btdbFindBy ---> SELECT `start-plan`.*, `start-account`.*, DATE_FORMAT(resumed_at , "%M %e, %Y") as "resumed_at_formatted", `start-user`.`mode`, case when DATE_ADD(end_date, INTERVAL 1 DAY) < now() then 'yes' else 'no' end as 'expired'
FROM `start-account`
JOIN `start-user` ON `start-user`.`user_id` = `start-account`.`user_id`
JOIN `start-plan` ON `start-plan`.`plan_id` = `start-account`.`plan_id`
WHERE `start-account`.`user_id` = '2'
ORDER BY `start-account`.`end_date` DESC
DEBUG - 2025-06-23 08:09:01 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-23 08:09:01 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 08:09:01 --> REQ ----------->
INFO - 2025-06-23 08:09:01 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 08:09:04 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
DEBUG - 2025-06-23 08:09:04 --> btdbFindBy ---> 
INFO - 2025-06-23 08:09:04 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 08:09:04 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
DEBUG - 2025-06-23 08:09:04 --> btdbFindBy ---> 
INFO - 2025-06-23 08:09:04 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 08:09:04 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-23 08:09:04 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 08:09:05 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-23 08:09:05 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 08:09:07 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-23 08:09:07 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 08:09:07 --> REQ ----------->
INFO - 2025-06-23 08:09:07 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 08:09:12 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-23 08:09:12 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 08:09:12 --> REQ ----------->
INFO - 2025-06-23 08:09:12 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 08:09:16 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-23 08:09:16 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 08:09:16 --> REQ ----------->
INFO - 2025-06-23 08:09:16 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2025-06-23 08:09:18 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 08:09:18 --> btdbFindBy ---> 
DEBUG - 2025-06-23 08:09:18 --> btdbFindBy ---> SELECT `start-plan`.*, `start-account`.*, DATE_FORMAT(resumed_at , "%M %e, %Y") as "resumed_at_formatted", `start-user`.`mode`, case when DATE_ADD(end_date, INTERVAL 1 DAY) < now() then 'yes' else 'no' end as 'expired'
FROM `start-account`
JOIN `start-user` ON `start-user`.`user_id` = `start-account`.`user_id`
JOIN `start-plan` ON `start-plan`.`plan_id` = `start-account`.`plan_id`
WHERE `start-account`.`user_id` = '2'
ORDER BY `start-account`.`end_date` DESC
DEBUG - 2025-06-23 08:09:18 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-23 08:09:18 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 08:09:19 --> REQ ----------->
INFO - 2025-06-23 08:09:19 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 08:09:21 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
DEBUG - 2025-06-23 08:09:21 --> btdbFindBy ---> 
INFO - 2025-06-23 08:09:21 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 08:09:21 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
DEBUG - 2025-06-23 08:09:21 --> btdbFindBy ---> 
INFO - 2025-06-23 08:09:21 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 08:09:21 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-23 08:09:21 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 08:09:21 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-23 08:09:21 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 08:09:35 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-23 08:09:35 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 08:09:35 --> REQ ----------->
INFO - 2025-06-23 08:09:35 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 08:09:45 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-23 08:09:45 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 08:09:45 --> REQ ----------->
INFO - 2025-06-23 08:09:45 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 08:11:03 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-23 08:11:03 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 08:11:03 --> REQ ----------->
INFO - 2025-06-23 08:11:03 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 23:10:41 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-23 23:10:41 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 23:10:42 --> btdbFindBy ---> 
DEBUG - 2025-06-23 23:10:42 --> btdbFindBy ---> SELECT *
FROM `start-user`
WHERE `login_token` = '$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-23 23:10:42 --> btdbFindBy ---> SELECT `start-plan`.*, `start-account`.*, DATE_FORMAT(resumed_at , "%M %e, %Y") as "resumed_at_formatted", `start-user`.`mode`, case when DATE_ADD(end_date, INTERVAL 1 DAY) < now() then 'yes' else 'no' end as 'expired'
FROM `start-account`
JOIN `start-user` ON `start-user`.`user_id` = `start-account`.`user_id`
JOIN `start-plan` ON `start-plan`.`plan_id` = `start-account`.`plan_id`
WHERE `start-account`.`user_id` = '2'
ORDER BY `start-account`.`end_date` DESC
DEBUG - 2025-06-23 23:10:49 --> REQ ----------->
INFO - 2025-06-23 23:10:49 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 23:11:03 --> REQ ----------->
INFO - 2025-06-23 23:11:03 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 23:11:04 --> REQ ----------->
INFO - 2025-06-23 23:11:04 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 23:11:07 --> REQ ----------->
INFO - 2025-06-23 23:11:07 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 23:11:07 --> REQ ----------->
INFO - 2025-06-23 23:11:07 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 23:11:13 --> REQ ----------->
INFO - 2025-06-23 23:11:13 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 23:11:18 --> REQ ----------->
INFO - 2025-06-23 23:11:18 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 23:11:20 --> REQ ----------->testuser061125_01@mailinator.com123123
DEBUG - 2025-06-23 23:11:20 --> btdbFindBy ---> 
DEBUG - 2025-06-23 23:11:21 --> btdbFindBy ---> SELECT *
FROM `start-user`
WHERE `email` = '<EMAIL>'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
INFO - 2025-06-23 23:11:21 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 23:11:21 --> btdbFindBy ---> SELECT `start-plan`.*, `start-account`.*, DATE_FORMAT(resumed_at , "%M %e, %Y") as "resumed_at_formatted", `start-user`.`mode`, case when DATE_ADD(end_date, INTERVAL 1 DAY) < now() then 'yes' else 'no' end as 'expired'
FROM `start-account`
JOIN `start-user` ON `start-user`.`user_id` = `start-account`.`user_id`
JOIN `start-plan` ON `start-plan`.`plan_id` = `start-account`.`plan_id`
WHERE `start-account`.`user_id` = '2'
ORDER BY `start-account`.`end_date` DESC
DEBUG - 2025-06-23 23:11:21 --> btdbFindBy ---> SELECT *
FROM `start-plan`
WHERE `plan_id` = '143'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-23 23:11:21 --> login-------> <EMAIL>
DEBUG - 2025-06-23 23:11:21 --> btdbFindBy ---> SELECT *
FROM `start-user_app`
WHERE `email` = '<EMAIL>'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
INFO - 2025-06-23 23:11:21 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 23:11:21 --> btdbFindBy ---> 
DEBUG - 2025-06-23 23:11:21 --> btdbFindBy ---> SELECT `start-plan`.*, `start-account`.*, DATE_FORMAT(resumed_at , "%M %e, %Y") as "resumed_at_formatted", `start-user`.`mode`, case when DATE_ADD(end_date, INTERVAL 1 DAY) < now() then 'yes' else 'no' end as 'expired'
FROM `start-account`
JOIN `start-user` ON `start-user`.`user_id` = `start-account`.`user_id`
JOIN `start-plan` ON `start-plan`.`plan_id` = `start-account`.`plan_id`
WHERE `start-account`.`user_id` = '2'
ORDER BY `start-account`.`end_date` DESC
DEBUG - 2025-06-23 23:11:21 --> btdbFindBy ---> SELECT *
FROM `start-plan`
WHERE `plan_id` = '143'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-23 23:11:22 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-23 23:11:22 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
WARNING - 2025-06-23 23:11:22 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('aiproStart', null, **********, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(401): btflag_set('aiproStart', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-23 23:11:22 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('user_email', null, **********, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(402): btflag_set('user_email', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-23 23:11:22 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('chatpro_KJLF4XgSL8wjlGm', null, **********, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(403): btflag_set('chatpro_KJLF4XgSL8wjlGm', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-06-23 23:11:22 --> REQ ----------->
INFO - 2025-06-23 23:11:22 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 23:11:28 --> REQ ----------->
INFO - 2025-06-23 23:11:28 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 23:11:28 --> btdbFindBy ---> 
DEBUG - 2025-06-23 23:11:28 --> btdbFindBy ---> SELECT `start-plan`.*, `start-account`.*, DATE_FORMAT(resumed_at , "%M %e, %Y") as "resumed_at_formatted", `start-user`.`mode`, case when DATE_ADD(end_date, INTERVAL 1 DAY) < now() then 'yes' else 'no' end as 'expired'
FROM `start-account`
JOIN `start-user` ON `start-user`.`user_id` = `start-account`.`user_id`
JOIN `start-plan` ON `start-plan`.`plan_id` = `start-account`.`plan_id`
WHERE `start-account`.`user_id` = '2'
ORDER BY `start-account`.`end_date` DESC
DEBUG - 2025-06-23 23:11:29 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-23 23:11:29 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
WARNING - 2025-06-23 23:11:29 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('aiproStart', null, **********, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(401): btflag_set('aiproStart', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-23 23:11:29 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('user_email', null, **********, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(402): btflag_set('user_email', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-23 23:11:29 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('chatpro_KJLF4XgSL8wjlGm', null, **********, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(403): btflag_set('chatpro_KJLF4XgSL8wjlGm', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-06-23 23:11:29 --> REQ ----------->
INFO - 2025-06-23 23:11:29 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 23:11:35 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
DEBUG - 2025-06-23 23:11:35 --> btdbFindBy ---> 
INFO - 2025-06-23 23:11:35 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 23:11:35 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
DEBUG - 2025-06-23 23:11:35 --> btdbFindBy ---> 
INFO - 2025-06-23 23:11:35 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 23:11:36 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-23 23:11:36 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
WARNING - 2025-06-23 23:11:36 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('aiproStart', null, 1753312296, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(401): btflag_set('aiproStart', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-23 23:11:36 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('user_email', null, 1753312296, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(402): btflag_set('user_email', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-23 23:11:36 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('chatpro_KJLF4XgSL8wjlGm', null, 1753312296, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(403): btflag_set('chatpro_KJLF4XgSL8wjlGm', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-06-23 23:11:36 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-23 23:11:36 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 23:17:35 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-23 23:17:35 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
WARNING - 2025-06-23 23:17:35 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('aiproStart', null, 1753312655, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(401): btflag_set('aiproStart', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-23 23:17:35 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('user_email', null, 1753312655, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(402): btflag_set('user_email', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-23 23:17:35 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('chatpro_KJLF4XgSL8wjlGm', null, 1753312655, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(403): btflag_set('chatpro_KJLF4XgSL8wjlGm', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-06-23 23:17:35 --> REQ ----------->
INFO - 2025-06-23 23:17:35 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 23:48:08 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-23 23:48:08 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
WARNING - 2025-06-23 23:48:08 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('aiproStart', null, 1753314488, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(401): btflag_set('aiproStart', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-23 23:48:08 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('user_email', null, 1753314488, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(402): btflag_set('user_email', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-23 23:48:08 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('chatpro_KJLF4XgSL8wjlGm', null, 1753314488, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(403): btflag_set('chatpro_KJLF4XgSL8wjlGm', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-06-23 23:48:09 --> REQ ----------->
INFO - 2025-06-23 23:48:09 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2025-06-23 23:48:11 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 23:48:11 --> btdbFindBy ---> 
DEBUG - 2025-06-23 23:48:11 --> btdbFindBy ---> SELECT `start-plan`.*, `start-account`.*, DATE_FORMAT(resumed_at , "%M %e, %Y") as "resumed_at_formatted", `start-user`.`mode`, case when DATE_ADD(end_date, INTERVAL 1 DAY) < now() then 'yes' else 'no' end as 'expired'
FROM `start-account`
JOIN `start-user` ON `start-user`.`user_id` = `start-account`.`user_id`
JOIN `start-plan` ON `start-plan`.`plan_id` = `start-account`.`plan_id`
WHERE `start-account`.`user_id` = '2'
ORDER BY `start-account`.`end_date` DESC
DEBUG - 2025-06-23 23:48:12 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-23 23:48:12 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
WARNING - 2025-06-23 23:48:12 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('aiproStart', null, **********, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(401): btflag_set('aiproStart', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-23 23:48:12 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('user_email', null, **********, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(402): btflag_set('user_email', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-23 23:48:12 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('chatpro_KJLF4XgSL8wjlGm', null, **********, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(403): btflag_set('chatpro_KJLF4XgSL8wjlGm', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-06-23 23:48:12 --> REQ ----------->
INFO - 2025-06-23 23:48:12 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 23:48:14 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
DEBUG - 2025-06-23 23:48:14 --> btdbFindBy ---> 
INFO - 2025-06-23 23:48:14 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 23:48:14 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
DEBUG - 2025-06-23 23:48:14 --> btdbFindBy ---> 
INFO - 2025-06-23 23:48:14 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 23:48:15 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-23 23:48:15 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
WARNING - 2025-06-23 23:48:15 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('aiproStart', null, 1753314495, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(401): btflag_set('aiproStart', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-23 23:48:15 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('user_email', null, 1753314495, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(402): btflag_set('user_email', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-23 23:48:15 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('chatpro_KJLF4XgSL8wjlGm', null, 1753314495, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(403): btflag_set('chatpro_KJLF4XgSL8wjlGm', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-06-23 23:48:15 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-23 23:48:15 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 23:48:29 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-23 23:48:29 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
WARNING - 2025-06-23 23:48:29 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('aiproStart', null, 1753314509, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(401): btflag_set('aiproStart', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-23 23:48:29 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('user_email', null, 1753314509, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(402): btflag_set('user_email', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-23 23:48:29 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('chatpro_KJLF4XgSL8wjlGm', null, 1753314509, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(403): btflag_set('chatpro_KJLF4XgSL8wjlGm', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-06-23 23:48:29 --> REQ ----------->
INFO - 2025-06-23 23:48:29 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2025-06-23 23:48:31 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 23:48:31 --> btdbFindBy ---> 
DEBUG - 2025-06-23 23:48:31 --> btdbFindBy ---> SELECT `start-plan`.*, `start-account`.*, DATE_FORMAT(resumed_at , "%M %e, %Y") as "resumed_at_formatted", `start-user`.`mode`, case when DATE_ADD(end_date, INTERVAL 1 DAY) < now() then 'yes' else 'no' end as 'expired'
FROM `start-account`
JOIN `start-user` ON `start-user`.`user_id` = `start-account`.`user_id`
JOIN `start-plan` ON `start-plan`.`plan_id` = `start-account`.`plan_id`
WHERE `start-account`.`user_id` = '2'
ORDER BY `start-account`.`end_date` DESC
DEBUG - 2025-06-23 23:48:31 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-23 23:48:31 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
WARNING - 2025-06-23 23:48:31 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('aiproStart', null, **********, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(401): btflag_set('aiproStart', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-23 23:48:31 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('user_email', null, **********, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(402): btflag_set('user_email', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-23 23:48:31 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('chatpro_KJLF4XgSL8wjlGm', null, **********, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(403): btflag_set('chatpro_KJLF4XgSL8wjlGm', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-06-23 23:48:31 --> REQ ----------->
INFO - 2025-06-23 23:48:31 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 23:48:33 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
DEBUG - 2025-06-23 23:48:33 --> btdbFindBy ---> 
INFO - 2025-06-23 23:48:33 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 23:48:33 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
DEBUG - 2025-06-23 23:48:33 --> btdbFindBy ---> 
INFO - 2025-06-23 23:48:33 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 23:48:34 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-23 23:48:34 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
WARNING - 2025-06-23 23:48:34 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('aiproStart', null, 1753314514, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(401): btflag_set('aiproStart', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-23 23:48:34 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('user_email', null, 1753314514, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(402): btflag_set('user_email', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-23 23:48:34 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('chatpro_KJLF4XgSL8wjlGm', null, 1753314514, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(403): btflag_set('chatpro_KJLF4XgSL8wjlGm', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-06-23 23:48:34 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-23 23:48:34 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 23:50:20 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-23 23:50:20 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
WARNING - 2025-06-23 23:50:20 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('aiproStart', null, 1753314620, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(401): btflag_set('aiproStart', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-23 23:50:20 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('user_email', null, 1753314620, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(402): btflag_set('user_email', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-23 23:50:20 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('chatpro_KJLF4XgSL8wjlGm', null, 1753314620, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(403): btflag_set('chatpro_KJLF4XgSL8wjlGm', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-06-23 23:50:20 --> REQ ----------->
INFO - 2025-06-23 23:50:20 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 23:59:18 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-23 23:59:18 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
WARNING - 2025-06-23 23:59:18 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('aiproStart', null, 1753315158, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(401): btflag_set('aiproStart', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-23 23:59:18 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('user_email', null, 1753315158, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(402): btflag_set('user_email', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-23 23:59:18 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('chatpro_KJLF4XgSL8wjlGm', null, 1753315158, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(403): btflag_set('chatpro_KJLF4XgSL8wjlGm', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-06-23 23:59:18 --> REQ ----------->
INFO - 2025-06-23 23:59:18 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
