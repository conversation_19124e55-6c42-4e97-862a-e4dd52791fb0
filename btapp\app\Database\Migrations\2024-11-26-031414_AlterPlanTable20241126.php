<?php

namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;

class AlterPlanTable20241126 extends Migration
{
    private $table = "plan";
    public function up()
    {
        if (!$this->db->fieldExists('locales', $this->table)) {
            $fields = [
                'locales' => [
                    'type' => 'JSON',
                    'null' => TRUE,
                ]
            ];
            $this->forge->addColumn($this->table, $fields);
        };
        if (!$this->db->fieldExists('currency_symbol', $this->table)) {
            $fields = [
                'currency_symbol' => [
                    'type' => 'VARCHAR',
                    'constraint' => 10,
                    'default' => '$',
                    'after' => 'currency',
                ],
            ];
            $this->forge->addColumn($this->table, $fields);
        };
        if (!$this->db->fieldExists('country', $this->table)) {
            $fields = [
                'country' => [
                    'type' => 'VARCHAR',
                    'constraint' => 50,
                    'default' => 'US',
                    'after' => 'currency_symbol',
                ],
            ];
            $this->forge->addColumn($this->table, $fields);
        };
    }

    public function down()
    {
    }
}
