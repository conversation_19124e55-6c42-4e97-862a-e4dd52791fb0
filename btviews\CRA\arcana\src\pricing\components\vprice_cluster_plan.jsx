import "../css/style.css";
import "../css/cluster_plan.css";
import Loader from "./Loader";

export default function VPriceClusterPlan(props) {
  const data = props.data ? props.data : null;
  const setPricing = props.setPricing ? props.setPricing : () => {};

  return (
    <div className="min-h-[100vh]">
      <div className="w-full max-w-full mx-auto pr-px flex-col justify-center items-center gap-[30px] inline-flex ">
        <div className="text-[#282828] max-w-full mt-[100px] flex flex-col justify-center items-center gap-3">
          <div className="text-4xl font-semibold">Get Full Access</div>
        </div>
        <div className="sm:w-full md:w-[768px] xl:w-full justify-center gap-[22px] flex flex-wrap">
          {data?.length === 0 && (
            <>
              <Loader />
              <Loader />
              <Loader />
              <Loader />
            </>
          )}

          {data?.map((ppg) => (
            <div
              key={ppg.plan_id}
              className="pricing-container w-[272px] h-[500px] max-h-full p-[22px] bg-gradient-to-b from-neutral-50 to-[#e2e2e2] rounded-[10px] border border-[#2872fa] flex flex-col justify-between items-center"
            >
              <div className="w-full relative text-left">
                <div className="justify-center items-center inline-flex">
                  <div className="grow shrink basis-0 self-stretch flex-col justify-center items-start inline-flex">
                    <div className="relative flex-col justify-start items-start flex">
                      <div className="text-[#282828] text-lg font-medium uppercase">
                        {ppg.label}
                      </div>
                      <div className="text-[#4c4c4c] text-sm font-normal ">
                        {ppg.sub_text}
                      </div>
                    </div>
                    <div className="self-stretch mt-0.5 text-[#1b64eb] font-semibold ">
                      <span className="text-2xl ">${ppg.price}</span>
                      <span className="text-base">/</span>
                      <span className="text-xl">month</span>
                    </div>
                  </div>
                </div>
                <hr
                  style={{
                    margin: "12px 0",
                    borderTop: "1px solid #4D4D4D",
                    borderImage:
                      "repeating-linear-gradient(to right, #4D4D4D 0, #4D4D4D 2px, transparent 2px, transparent 4px) 1",
                  }}
                />

                <div className="description-container text-xs font-normal flex flex-col gap-3" dangerouslySetInnerHTML={{ __html: ppg.display_txt2 }}></div>
              </div>
              <div
                onClick={() => {
                  setPricing(ppg.plan_id);
                }}
                className="button-bg cursor-pointer w-[158px] h-10 px-5 py-2.5 rounded-[10px] shadow border border-[#c1c1c1] justify-center items-center inline-flex"
              >
                <div className="text-center text-white text-lg font-medium ">
                  CONTINUE
                </div>
              </div>
            </div>
          ))}
        </div>
        <p className="text-xs max-w-md text-center leading-relaxed mb-32 lg:mb-12">
          *The pricing is exclusive of taxes and additional local tax may be
          collected.
        </p>
      </div>
    </div>
  );
}
