<div class="description">
    <?php if (!$unlimited_access_to_models) : ?>
        <div class="hover-target">
            <p class="font-medium text-[#2872FA]">Limited access to the latest language models:</p>
            <span class="description-tooltip"><?php echo $token_limit; ?></span>
        </div>
    <?php else: ?>
        <p class="font-medium text-[#2872FA]">Unlimited access to the latest language and image models:</p>
    <?php endif; ?>

    <div class="mt-[5px] pl-2.5">
        <?php foreach ($models as $key => $model): ?>
            <?php if (!is_array($model)): ?>
                <p><?php echo $model; ?></p>
            <?php elseif ($key === 'more_models'): ?>
                <?php if (!empty($model)) : ?>
                    <div class='hover-target'>
                        <p>Plus <?php echo count($model); ?> more models</p>

                        <div class="description-tooltip flex-row gap-2">
                            <div>
                                <?php foreach ($model as $current_model): ?>
                                    <p><?php echo $current_model; ?></p>
                                <?php endforeach; ?>
                            </div>
                        </div>
                    </div>
                <?php endif; ?>
            <?php endif; ?>
        <?php endforeach; ?>
    </div>
</div>

<?php if (!is_null($prompts)) : ?>
    <div class="description">
        <p class="font-medium text-[#2872FA]"><?php echo $prompts; ?> prompts per user for advanced image and reasoning models:</p>

        <div class="mt-[5px] pl-2.5">
            <?php foreach ($models_with_prompts as $model) : ?>
                <p><?php echo $model; ?></p>
            <?php endforeach; ?>
        </div>
    </div>
<?php endif; ?>

<div class="description">
    <div class="hover-target">
        <p class="font-medium text-[#2872FA]"><?php echo !$unlimited_access_to_ai_apps ? 'Limited' : 'Unlimited'; ?> access to AI apps</p>

        <?php if (!empty($apps)): ?>
            <div class="description-tooltip flex-row gap-2">
                <?php foreach ($apps as $app_type => $current_apps) : ?>
                    <div class="w-1/2">
                        <p class="font-medium mb-1"><?php echo $app_type; ?></p>

                        <div>
                            <?php foreach ($current_apps as $app) : ?>
                                <p><?php echo $app; ?></p>
                            <?php endforeach ?>
                        </div>
                    </div>
                <?php endforeach ?>
            </div>
        <?php endif; ?>
    </div>
</div>