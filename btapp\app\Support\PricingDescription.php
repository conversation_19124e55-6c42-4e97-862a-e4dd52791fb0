<?php

namespace App\Support;

use App\Enums\Locales;
use App\Enums\PlanType;
use Exception;
use NumberFormatter;

class PricingDescription
{
    private string $directory = __DIR__ . '/PricingDescription';

    private float $price = 0;

    private ?int $trial_days = null;

    private ?float $trial_price = null;

    private Locales $locale = Locales::EN;

    public function __construct(private PlanType $plan_type, private string $currency_symbol, private bool $is_trial = false) {}

    public static function make(PlanType $plan_type, string $currency_symbol, bool $is_trial = false): self
    {
        return new self($plan_type, $currency_symbol, $is_trial);
    }

    public function setPrice(float $price)
    {
        $this->price = $price;

        return $this;
    }

    public function setTrialDays(int $trial_days)
    {
        $this->trial_days = $trial_days;

        return $this;
    }

    public function setTrialPrice(float $trial_price)
    {
        $this->trial_price = $trial_price;

        return $this;
    }

    public function setLocale(string|Locales $locale)
    {
        $locale = strtoupper($locale);
        $this->locale = !$locale instanceof Locales ? Locales::from($locale) : $locale;

        return $this;
    }

    public function get(): ?string
    {
        $plan_type = $this->plan_type;
        $locale = $this->locale;

        if ($plan_type !== PlanType::Advanced) {
            $current_plan = $plan_type->value;

            $translation_path = "$this->directory/Locales/" . strtolower($locale->value) . '.json';
            $template_path = "$this->directory/Views/Default.php";

            if (!file_exists($translation_path)) {
                throw new Exception('Translation file not found.');
            }

            if (!file_exists($template_path)) {
                throw new Exception('Template file not found.');
            }

            $data = json_decode(file_get_contents($translation_path), true);

            $access_to = $data['access_to'][$current_plan];
            $new_badge_text = $data['new_badge_text'];
            $sentences_count = $data['sentences_count'];
            $context_memory = $data['context_memory'][$current_plan];
            $context_memory_tooltip = $data['context_memory_tooltip'];
            $dialogue_limit = $data['dialogue_limit'][$current_plan];
            $dialogue_limit_tooltip = $data['dialogue_limit_tooltip'];
            $advanced_tool_access = $plan_type !== PlanType::Basic ? $data['advanced_tool_access'] : null;
            $advanced_tool_access_tooltip = $data['advanced_tool_access_tooltip'];

            $current_sentences_count = $this->getSentencesCount();

            if (!is_null($current_sentences_count)) {
                $sentences_count = $this->replacePlaceholder($sentences_count, $current_sentences_count);
            }

            $models = match ($plan_type) {
                PlanType::Basic => $this->getBasicModels(),
                PlanType::Pro => $this->getProModels(),
                PlanType::ProMax => $this->getProMaxModels(),
                PlanType::Enterprise => $this->getEnterpriseModels(),
                default => [],
            };

            foreach ($models as $model => $model_options) {
                if (is_array($model_options)) {
                    $current = [];

                    foreach ($model_options as $model_option_key => $model_option_value) {
                        if ($model_option_key === 'tooltip') {
                            if (isset($data[$model_option_value])) {
                                if ($model_option_value === 'sentences_count') {
                                    $current[$model_option_key] = $sentences_count;
                                }
                            }
                        } else {
                            if (isset($data[$model_option_key])) {
                                $current_placeholder = $data[$model_option_key];

                                $current['text'] = "$model: " . $this->replacePlaceholder($current_placeholder, $model_option_value);
                            }
                        }
                    }

                    $models[$model] = array_merge($model_options, $current);
                }
            }

            $current_dialogue_limit_tokens = $this->getDialogueLimitTokens();

            if (!is_null($current_dialogue_limit_tokens)) {
                $dialogue_limit_tooltip .= ' ' . $this->replacePlaceholder($data['dialogue_limit_tokens'], $current_dialogue_limit_tokens);
            }

            $is_trial = $this->is_trial;
            $trial_text = null;

            if ($is_trial) {
                if ($this->trial_price != 0) {
                    $price_text = $this->getPriceText($this->currency_symbol, $this->price);

                    $trial_text = $this->replacePlaceholder($data['trial_text'], [
                        'trial_days' => $this->trial_days,
                        'price_text' => $price_text,
                    ]);
                }
            }

            return $this->renderOutput($template_path, [
                'access_to' => $access_to,
                'new_badge_text' => $new_badge_text,
                'sentences_count' => $sentences_count,
                'context_memory' => $context_memory,
                'context_memory_tooltip' => $context_memory_tooltip,
                'dialogue_limit' => $dialogue_limit,
                'dialogue_limit_tooltip' => $dialogue_limit_tooltip,
                'advanced_tool_access' => $advanced_tool_access,
                'advanced_tool_access_tooltip' => $advanced_tool_access_tooltip,
                'models' => $models,
                'is_trial' => $is_trial,
                'trial_text' => $trial_text,
            ]);
        }

        return null;
    }

    public function getWithFreeTrialPlan(): ?string
    {
        $template_path = "$this->directory/Views/WithFreeTrial.php";

        if (!file_exists($template_path)) {
            throw new Exception('Template file not found.');
        }

        $plan_type = $this->plan_type->value;

        if ($plan_type === 'ProMax') {
            $plan_type = 'Pro Max';
        }

        $price_text = $this->getPriceText($this->currency_symbol, $this->price);

        $trial_template = $this->renderOutput($template_path, [
            'trial_days' => $this->trial_days,
            'plan_type' => $plan_type,
            'price_text' => $price_text,
        ]);

        return $trial_template . $this->get();
    }

    public function getWithClusterPlan(string $plan_name): ?string
    {
        $template_path = "$this->directory/Views/WithClusterPlan.php";

        if (!file_exists($template_path)) {
            throw new Exception('Template file not found.');
        }

        $token_limit = null;
        $prompts = match ($plan_name) {
            'Team Max' => 60,
            'Office Max' => 100,
            'Enterprise Max' => 120,
            default => null
        };

        $models_with_prompts = [];

        switch ($this->plan_type) {
            case PlanType::Pro:
                $unlimited_access_to_models = false;
                $token_limit = '500,000 tokens per month';

                $models = [
                    'GPT-4o mini, GPT-4o',
                    'LLaMA3-8B',
                    'Gemini Pro',
                ];
                break;
            default:
                $unlimited_access_to_models = true;

                $models = [
                    'GPT-4, GPT-4o, GPT-4o mini',
                    'Claude 3.7 Sonnet',
                    'Llama 3.1',
                    'Gemini Pro',
                    'DALL-E',
                    'more_models' => [
                        'Gemma-2',
                        'Mistral',
                        'DeepSeek',
                        'DBRX',
                        'Qwen',
                        'Mixtral',
                    ],
                ];

                $models_with_prompts = [
                    'Flux.1 Pro',
                    'OpenAI o1-preview',
                    'OpenAI o1-mini',
                ];
                break;
        }

        $unlimited_access_to_ai_apps = $unlimited_access_to_models;

        $apps = [
            'Language apps' => [
                'ChatPDF',
                'Grammar AI',
                'Teacher AI',
                'Search AI',
                'Multi-Chat',
                'SiteBot',
                'Coding AI',
                'Homework Help',
            ],
            'Image apps' => [
                'DreamPhoto',
                'Interior AI',
                'Restore Photo',
                'Remove Background',
                'Avatar Maker',
                'Storybook',
            ],
        ];

        return $this->renderOutput($template_path, [
            'unlimited_access_to_models' => $unlimited_access_to_models,
            'token_limit' => $token_limit,
            'models' => $models,
            'prompts' => $prompts,
            'models_with_prompts' => $models_with_prompts,
            'unlimited_access_to_ai_apps' => $unlimited_access_to_ai_apps,
            'apps' => $apps,
        ]);
    }

    // TODO: Modify this
    public function getWithAdvancedPlan(): ?string
    {
        $plan_type = $this->plan_type;
        $template_path = "$this->directory/Views/WithAdvancedPlan.php";

        if (!file_exists($template_path)) {
            throw new Exception('Template file not found.');
        }

        $chatbot_access = ['Access to all Pro Chatbots,'];

        switch ($plan_type) {
            case PlanType::Pro:
                $token_limit = '1,000,000 tokens per month';
                $sentences = '(approximately 40,000 sentences)';

                $chatbot_access = ['Access to Pro Chatbots:'];

                $models = [
                    'GPT-4o',
                    'DeepSeek V3',
                    'Grok AI' => [
                        'new_badge' => true
                    ],
                    'LLaMA3-70B',
                    'Gemini Pro',
                ];
                break;
            case PlanType::Advanced:
                $token_limit = '2,500,000 tokens per month';
                $sentences = '(approximately 100,000 sentences)';

                $chatbot_access = array_merge($chatbot_access, [
                    'Access to Advanced Chatbots:'
                ]);

                $models = [
                    'GPT-4',
                    'Claude 3.5',
                    'DeepSeek R1' => [
                        'new_badge' => true
                    ],
                ];
                break;
            case PlanType::ProMax:
                $token_limit = 'No token limit';
                $sentences = '(unlimited chatbot responses)';

                $chatbot_access = array_merge($chatbot_access, [
                    'Access to all Advanced Chatbots,',
                    'Access to Premium Chatbots:'
                ]);

                $models = [
                    'o1-mini',
                    'o3 mini' => [
                        'new_badge' => true
                    ],
                    'Claude 3.7' => [
                        'new_badge' => true
                    ],
                    'DALL-E 3: No image limit',
                ];
                break;
        }

        $limited_access_to = [
            'Claude 3.7: 50k tokens/month<br/><span class="text-xs">(approximately 2,000 sentences)</span>',
        ];

        $limited_access_to = match ($plan_type) {
            PlanType::Pro => array_merge($limited_access_to, [
                'DALL-E 3: 50 Images/month',
                'Flux AI: 30 Images/month',
            ]),
            PlanType::Advanced => array_merge($limited_access_to, [
                'DALL-E 3: 80 Images/month',
                'Flux AI: 80 Images/month',
            ]),
            PlanType::ProMax => [
                'Flux AI: 160 Images/month'
            ],
            default => $limited_access_to,
        };

        return $this->renderOutput($template_path, [
            'token_limit' => $token_limit,
            'sentences' => $sentences,
            'chatbot_access' => $chatbot_access,
            'models' => $models,
            'limited_access_to' => $limited_access_to,
        ]);
    }

    private function getPriceText(string $currency_symbol, float $amount): string
    {
        $amount = $this->formatNumber($amount);

        switch ($currency_symbol) {
            case 'ر.س.':
            case 'AED':
            case 'CHF':
            case 'SEK':
            case 'zł':
            case 'LEI':
            case 'Ft':
            case 'kr.':
            case 'лв':
            case '₺':
                return $amount . $currency_symbol;
            default:
                return $currency_symbol . $amount;
        }
    }

    private function replacePlaceholder(string $placeholder, string|array $replacement): string
    {
        $result = preg_replace_callback('/\{(\w+)\}/', function ($matches) use ($replacement) {
            if (is_array($replacement)) {
                $key = $matches[1];

                return $replacement[$key] ?? $matches[0];
            }

            return $replacement;
        }, $placeholder);

        return $result;
    }

    private function getBasicModels(): array
    {
        return [
            'GPT-4o',
            'DeepSeek V3',
            'Claude 3.5' => [
                'tokens_per_month' => '25k',
                'tooltip' => 'sentences_count',
            ],
            'DALL-E 3' => [
                'images_per_month' => 20,
            ],
            'Flux' => [
                'images_per_month' => 15,
            ],
            'LLaMA3-8B',
            'Gemma-7B',
        ];
    }

    private function getProModels(): array
    {
        return [
            'GPT-4o',
            'DeepSeek V3',
            'DeepSeek R1' => [
                'tokens_per_month' => '50k',
                'tooltip' => 'sentences_count',
            ],
            'Grok AI' => [
                'new_badge' => true
            ],
            'Claude 3.7' => [
                'tokens_per_month' => '50k',
                'tooltip' => 'sentences_count',
            ],
            'DALL-E 3' => [
                'images_per_month' => 50,
            ],
            'Flux' => [
                'images_per_month' => 30,
            ],
            'LLaMA3-70B',
            'Gemini Pro',
        ];
    }

    private function getProMaxModels(): array
    {
        return [
            'GPT-4o',
            'GPT-4',
            'OpenAI o1',
            'OpenAI o3 mini' => [
                'new_badge' => true
            ],
            'DeepSeek V3',
            'DeepSeek R1',
            'Grok AI' => [
                'new_badge' => true
            ],
            'Claude 3.7' => [
                'new_badge' => true
            ],
            'DALL-E 3',
            'Flux' => [
                'images_per_month' => 160,
            ],
            'LLaMA3-70B',
            'Gemini Pro',
            '(10+ other LLM Models)',
        ];
    }

    private function getEnterpriseModels(): array
    {
        return [
            'GPT-4o',
            'DeepSeek V3',
            'Grok AI' => [
                'new_badge' => true
            ],
            'Flux' => [
                'images_per_month' => 30,
            ],
            'LLaMA3-70B',
            'Gemini Pro',
            '(10+ other LLM Models)',
        ];
    }

    private function formatNumber(int|float $number): string
    {
        $formatter = new NumberFormatter($this->locale->value, NumberFormatter::DECIMAL);

        return $formatter->format($number);
    }

    private function getSentencesCount(): ?string
    {
        $sentences_count = match ($this->plan_type) {
            PlanType::Basic => 1000,
            PlanType::Pro => 2000,
            default => null,
        };

        if (is_null($sentences_count)) {
            return null;
        }

        return $this->formatNumber($sentences_count);
    }

    private function getDialogueLimitTokens(): ?string
    {
        $dialogue_limit = match ($this->plan_type) {
            PlanType::Basic => 500000,
            PlanType::Pro, PlanType::Enterprise => 1000000,
            default => null,
        };

        if (is_null($dialogue_limit)) {
            return null;
        }

        return $this->formatNumber($dialogue_limit);
    }

    private function renderOutput($template_path, $data)
    {
        extract($data);
        ob_start();

        include $template_path;

        $output = ob_get_clean();
        $output = preg_replace('/>\s+</', '><', $output);
        $output = preg_replace('/\s+/', ' ', $output);
        $output = trim($output);

        return $output;
    }
}
