<?php

namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;

class AlterUserTable20230907 extends Migration
{
    private $table = "user";

    public function up()
    {
        if ($this->db->fieldExists('disputed', $this->table)) return;
        $this->db->disableForeignKeyChecks();

        $fields = [
            'disputed' => [
                'type' => 'VARCHAR',
                'constraint' => 10,
                'null' => true,
                'default' => '',
                'after' => 'flags'
            ]
        ];
        $this->forge->addColumn($this->table, $fields);

        $this->db->enableForeignKeyChecks();

    }

    public function down()
    {
        //
    }
}
