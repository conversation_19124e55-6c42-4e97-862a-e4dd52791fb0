<?php

namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;

class AlterTableFlux extends Migration
{
    protected $DBGroup = 'aiapp';
    private $table = "tbl_flux";
    public function up()
    {
        if (!$this->db->fieldExists('image_model', $this->table)){
            $this->db->disableForeignKeyChecks();
            $fields = [
                'image_model' => [
                    'type'       => 'TEXT',
                    'null'       => true,
                    'default'    => '',
                    'after'      => 's3_link',
                ],
            ];
            $this->forge->addColumn($this->table, $fields);
        }
    }
    public function down()
    {
        // $this->forge->dropColumn($this->table, 'display_txt2');
    }
}
