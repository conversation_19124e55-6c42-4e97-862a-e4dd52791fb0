<?php

namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;

class AlterUserTable20230818 extends Migration
{
    private $table = "user";

    public function up()
    {
        if ($this->db->fieldExists('orig_email', $this->table)) return;
        $this->db->disableForeignKeyChecks();

        $fields = [
            'orig_email' => [
				'type' => 'VARCHAR',
				'constraint' => 100,
				'null' => true,
                'after' => 'login_token'
            ]
        ];
        $this->forge->addColumn($this->table, $fields);

        $this->db->enableForeignKeyChecks();
    }

    public function down()
    {
        //
    }
}
