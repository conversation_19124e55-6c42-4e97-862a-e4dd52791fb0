<?php

namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;

class AlterAccountTable20240520 extends Migration
{
    private $table = "account";
    
    public function up()
    {
        //
        $alterfields = [
			'status' => [
                'type' => "SET('active','inactive','paused')",
				'default' => 'active',
            ],
        ];
        $this->forge->modifyColumn($this->table, $alterfields);

    }

    public function down()
    {
        //
    }
}
