<?php

namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;

class AlterUserTable20240913 extends Migration
{
    private $table = "user";
    public function up()
    {
        if ($this->db->fieldExists('verify', $this->table)) return;
        $this->db->disableForeignKeyChecks();
        $fields = [
            'verify' => [
                'type' => 'VARCHAR',
                'constraint' => 100, 
                'default' => '',
                'null' => true,
                'after' => 'status',
            ]
        ];

        $this->forge->addColumn($this->table, $fields);

        $this->db->enableForeignKeyChecks();
    }

    public function down()
    {
        //
    }
}
