<?php

namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;

class AlterAccountTable20240215 extends Migration
{
    private $table = "account";

    public function up()
    {
        if ($this->db->fieldExists('max_tokens', $this->table)) return;
        $this->db->disableForeignKeyChecks();

        $fields = [
            'max_tokens' => [
                'type' => 'VARCHAR',
                'constraint' => 10,
                'null' => true,
                'default' => '',
                'after' => 'members'
            ]
        ];
        $this->forge->addColumn($this->table, $fields);

        $this->db->enableForeignKeyChecks();

    }

    public function down()
    {
        //
    }
}
