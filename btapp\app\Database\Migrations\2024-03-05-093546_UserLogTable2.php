<?php

namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;

class UserLogTable2 extends Migration
{
    private $table = "user_log";
    public function up()
    {

        $db = \Config\Database::connect('aiapp');

        $forge = \Config\Database::forge($db);

        $db->disableForeignKeyChecks();

        $forge->addField([
            'userlog_id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => false,
                'auto_increment' => true,
            ],
            'userlog_pid' => [
                'type' => 'CHAR',
                'constraint' => 36,
                'null' => true,
            ],
            'user_pid' => [
                'type' => 'CHAR',
                'constraint' => 36,
                'null' => true,
            ],
            'event' => [
                'type' => 'VARCHAR',
                'constraint' => 50,
                'null' => true,
            ],
            'event_data' => [
                'type' => 'LONGTEXT'
            ],
            'created_at' => [
                'type' => 'DATETIME',
                'null' => false,
            ],
            'updated_at' => [
                'type' => 'DATETIME',
                'null' => false,
            ],
            'deleted_at' => [
                'type' => 'DATETIME',
                'null' => false,
            ],
        ]);

        $forge->addKey('userlog_id', true);
        $forge->addKey(['user_pid'], false, false, 'start_userlog_user_pid_IDX');

        $forge->createTable($this->table);
        $db->enableForeignKeyChecks();
    }

    public function down()
    {
        // comment out this on SERVER DEPLOYMENT
        // $db = \Config\Database::connect('aiapp');
        // $forge = \Config\Database::forge($db);
        // $forge->dropTable($this->table);
    }
}
