<?php

namespace App\Controllers\FlowPages;

use BT<PERSON>ore\Payment\Paddle as BTPaddle;
use BTCore\Payment\Stripe as BTStripe;
use BTCore\Payment as BTPayment;

use App\Controllers\BaseController;

use App\Models\AccountModel;

class Payment extends BaseController
{
    private $theme = 'arcana';
    private $themeSlug = ''; //not used
    private $themePageVersion = 'v1'; //this is used for twig filename
    private $paddle_api_key = [];
    private $sct_api_key = [];
    //
    private $date_now;
    private $plan = [];
    private $mode = 'live';
    private $pageSlug = '';
    private $data = [];
    private $pageVersion = null;

    //-------------------------------------------------------------------
    //  public
    //-------------------------------------------------------------------

    public function __construct()
    {
        // parent::__construct();
        $this->paddle_api_key = [
            'vendor_id_test' => getenv("PADDLE_TEST_KEY"),
            'vendor_auth_code_test' => getenv("PADDLE_TEST_AUTH_CODE"),
            'vendor_id_live' => getenv("PADDLE_LIVE_KEY"),
            'vendor_auth_code_live' => getenv("PADDLE_LIVE_AUTH_CODE"),
        ];

        $this->sct_api_key = [
          'api_end_point' => getenv("SCT_API_ENDPOINT"),
          'api_project_code' => getenv("SCT_PROJECT_CODE"),
          'api_key' => getenv("SCT_API_KEY")
        ];

        $this->stripe_api_key = [
          'api_key_test' => getenv('STRIPE_API_KEY_TEST'),
          'api_key_live' => getenv('STRIPE_API_KEY_LIVE')
        ];

        $this->date_now = date('Y-m-d H:i:s', time());
    }

    public function index($pageV = null)
    {
        if( btsessionIsUserLoggedIn() ) {
          $user = btdbFindBy('UserModel', 'user_id', btsessionGET("USER")->user_id);
          if( $user['success'] && $user['res'] ) btsessionSetAccount($user['res'][0], 1);
          else $this->destroySession();
        }

        $this->plan = btdbFindBy('PlanModel', 'plan_id', btflag('pricing'));

        $this->pageVersion = $pageV;
        $this->pmt_redirection();

        if (is_null($this->pageVersion)) {
            $this->theme_pageVersion($this->pageVersion);
        } else {
            btflag_set('vpay', $this->pageVersion);
        }

        $this->theme = btflag($this->encryptedFlag['theme'], $this->theme);
        $uri = current_url(true);
        $this->pageSlug = (($uri->getSegment(2))) ? $uri->getSegment(2): PAGE_MIXPANELNAME_DEFAULT;

        $this->theme_data();

        switch ($this->theme) {
            case 'basilisk-03':
            case 'basilisk-02':
            case 'basilisk':
              $this->theme_basilisk();
              break;
            case 'druig':
              $this->theme_druig();
              break;
            case 'echo':
              $this->theme_echo();
              break;
            case 'arcana':
            default:
                $this->theme_arcana();
        }
    }

    public function upgrade($id = null)
    {
        $this->data['isUserTrial'] = 0;
        if(!btsessionIsUserLoggedIn()) {
          header("Location: " . base_url('register'));
          die;
        } else if( $this->checkIfSubscriptionIsPaddleTrial() ) {
          return $this->redirectToPaymentPage($id, 'upgrade');
        }
        if(btsessionGET("PLAN")->trial_days) {
            if(strtotime(btsessionGET("ACCOUNT")->trial_end) > strtotime($this->date_now)) {
                $this->data['isUserTrial'] = 1;
            }
        }
        $plan = btdbFindBy('PlanModel', 'plan_id', $id);
        if($plan['success'] && $plan['res']) {
            $this->data['plan'] = $plan['res'][0];
        } else {
            header("Location: " . base_url("upgrade")); 
            die;
        }
        $this->theme = btflag('theme', $this->theme);
        $uri = current_url(true);
        $this->pageSlug = (($uri->getSegment(2))) ? 'pay-' . $uri->getSegment(2) : PAGE_MIXPANELNAME_DEFAULT;
        $this->data['update_action'] = 'upgrade';

        $this->switch_theme();
    }

    public function downgrade($id = null)
    {
        $agent = $this->request->getUserAgent();
        $this->data['isUserTrial'] = 0;
        if(!btsessionIsUserLoggedIn()) {
          header("Location: " . base_url('register'));
          die;
        } else if( $this->checkIfSubscriptionIsPaddleTrial() ) {
          return $this->redirectToPaymentPage($id, 'downgrade');
        }
        if(btsessionGET("PLAN")->trial_days) {
            if(strtotime(btsessionGET("ACCOUNT")->trial_end) > strtotime($this->date_now)) {
                $this->data['isUserTrial'] = 1;
            }
        }
        $plan = btdbFindBy('PlanModel', 'plan_id', $id);
        if($plan['success'] && $plan['res']) {
            $this->data['plan'] = $plan['res'][0];
        } else {
            header("Location: " . base_url("downgrade"));
            die;
        }
        $this->theme = btflag('theme', $this->theme);
        $uri = current_url(true);
        $this->pageSlug = (($uri->getSegment(2))) ? 'pay-' . $uri->getSegment(2) : PAGE_MIXPANELNAME_DEFAULT;
        $this->data['update_action'] = !empty($agent->getReferrer()) && $agent->getReferrer() === base_url('/survey') ? 'cancel_downgrade' : 'downgrade';

        $this->switch_theme();
    }

    public function resume()
    {
        if(!btsessionIsUserLoggedIn()) {
          header("Location: " . base_url('register'));
          die;
        }
        
        $plan_id = btsessionHas('ACCOUNT') ? btsessionGet('ACCOUNT')->plan_id : '';
        $status = btsessionHas('ACCOUNT') ? btsessionGet('ACCOUNT')->status : '';
        $account_pid = btsessionHas('ACCOUNT') ? btsessionGet('ACCOUNT')->account_pid : '';
        $merchant_subscription_id = btsessionHas('ACCOUNT') ? btsessionGet('ACCOUNT')->merchant_subscription_id : '';
        $merchant = btsessionHas('ACCOUNT') ? btsessionGet('ACCOUNT')->merchant : '';
        
        if ($plan_id=='' || $status==''){
          header("Location: " . base_url('manage-acount'));
          die;
        }

        $plan = btdbFindBy('PlanModel', 'plan_id', $plan_id);
        if($plan['success'] && $plan['res']) {
            $this->data['plan'] = $plan['res'][0];
        }

        $this->data['plan'] = $plan['res'][0];
        $this->data['plan']->account_pid = $account_pid;
        $this->data['plan']->merchant_subscription_id = $merchant_subscription_id;
        $this->data['plan']->merchant = $merchant;

        $uri = current_url(true);
        $this->pageSlug = (($uri->getSegment(2))) ? $uri->getSegment(2) : PAGE_MIXPANELNAME_DEFAULT;
        $this->switch_theme();
    }

    //-------------------------------------------------------------------
    //  protected
    //-------------------------------------------------------------------

    protected function redirectToPaymentPage($id, $action = '')
    {
      btflag_set('pricing', $id, $option = ['domain' => '.ai-pro.org']);
      btflag_set('pricing', $id, $option = ['domain' => 'ai-pro.org']);
      btflag_set('pricing', $id, $option = ['domain' => 'localhost']);
      btflag_set('pricing', $id);
      header("Location: " . base_url('pay?pd='.$action));
      die;
    }

    //-------------------------------------------------------------------
    //  private
    //-------------------------------------------------------------------


    private function switch_theme()
    {
        $this->theme_data();
        if( !$this->checkIfSlugIsUpgradeDowngrade() ) {
          $this->theme_pageVersion();
        }
        switch ($this->theme) {
            case 'arcana':
            default:
                $this->theme_arcana();
        }
    }

    private function checkIfSlugIsUpgradeDowngrade()
    {
        if( in_array($this->pageSlug, ['pay-upgrade', 'pay-downgrade', 'resume']) ) {
            return true;
        }

        return false;
    }

    private function validFastSpringCurrency($currency){
      $out = false;
      $currency = strtolower($currency);

      if ($currency=='usd'){
        $out = true;
      }elseif($currency=='eur'){
        $out = true;
      }elseif($currency=='gbp'){
        $out = true;
      }elseif($currency=='brl'){
        $out = true;
      }elseif($currency=='chf'){
        $out = true;
      }elseif($currency=='sek'){
        $out = true;
      }elseif($currency=='pln'){
        $out = true;
      }elseif($currency=='czk'){
        $out = true;
      }elseif($currency=='dkk'){
        $out = true;
      }

      return $out;
    }
    private function pmt_flag_redirect(){
      $redirect_to_stripe='no';
      $country_code = getGeoCountryCode();

      if (btflag('force_pmt', '') !== '1') {
        if ($country_code !== 'us') {
          if (btflag('pmt', '') == 'rec' || btflag('pmt', '') == 'rec2' || btflag('pmt', '') == '') {
            $redirect_to_stripe = 'yes';
            btflag_set('pmt', 'pay2'); //set to stripe
          } else if (btflag('pmt', '') == 'mpay_rec') {
            $redirect_to_stripe = 'yes';
            btflag_set('pmt', 'mpay_st'); //set to stripe
          }
        }  
      }

      if (btflag('pmt', null) == 'fs' && $this->validFastSpringCurrency($this->plan->currency)){
        header("Location: ". base_url() ."pay/FSQ6N8V2A");
        die;
      }elseif(btflag('pmt', null) == 'sct'){
        $this->refirectToCardTransaction();
      }

      if (btflag('pmt', null) == 'rec2' && $redirect_to_stripe == 'no') {
        $this->mode = btflag('mode', 'live');

        $force_gateway = btflag('force_gateway', '');
        if($force_gateway=='1'){
          header("Location: ". base_url() ."pay/1LCXJMZNX6");
          die;
        }else if ($force_gateway=='2'){
          header("Location: ". base_url() ."pay/VS6lni4hKx");
          die;
        }

        $accountModel = new AccountModel();
        $account = $accountModel->getRecurlyTransaction($this->mode, $this->today_date_notime());

        $transaction_count = 0;
        if ($account['success']==1){
          $transaction_count = count($account['res']);
        }

        $isUseSecondMerchant = $this->isUseSecondMerchant($transaction_count,50);
        if ($isUseSecondMerchant){
          header("Location: ". base_url() ."pay/VS6lni4hKx");
          die;
        }else{
          header("Location: ". base_url() ."pay/1LCXJMZNX6");
          die;
        }
      }

      if ($this->plan->plan_type == 'Enterprise') {
        header("Location: https://ai-pro.org/enterprise-verification/");
        // header("Location: ". base_url() ."pay/mcWiDilmgQ");
        die;
      }

      if (btflag('kt8typtb') === 'druig' || btflag('kt8typtb') === 'echo') {
        if ((btflag('pmt', '') == 'rec' || btflag('pmt', '') == 'rec2' || btflag('pmt', '') == '') && $redirect_to_stripe == 'yes') {
          header("Location: ". base_url() ."pay/uHTinVqsUl");
          die;
        } elseif (btflag('pmt') == 'st' || btflag('pmt') == 'pay2' || btflag('pmt') == 'pay3d' ) {
          header("Location: ". base_url() ."pay/uHTinVqsUl");
          die;
        } elseif (btflag('pmt') == 'rec2' && btflag('force_gateway', '') == '2') {
          header("Location: ". base_url() ."pay/VS6lni4hKx");
          die;
        } elseif (btflag('pmt') == 'pp' || btflag('pmt') == 'rec2' ) {
          header("Location: ". base_url() ."pay/1LCXJMZNX6");
          die;
        } else {
          header("Location: " . base_url() . "pay/1LCXJMZNX6");
          die;
        }
      }

      if ((btflag('pmt', '') == 'rec' || btflag('pmt', '') == 'rec2' || btflag('pmt', '') == '') && $redirect_to_stripe == 'yes') {
        header("Location: ". base_url() ."pay/uHTinVqsUl");
        die;
      }

      if (btflag('pmt', null) == 'st' || btflag('pmt', null) == 'pay2' || btflag('pmt') == 'pay3d') {
          header("Location: ". base_url() ."pay/uHTinVqsUl");
          die;
      }

      if (btflag('pmt', null) == 'pp') {
        header("Location: ". base_url() ."pay/1LCXJMZNX6");
        die;
      }

      if (btflag('force_pmt', '') !== '1') {
        if ((btflag('pmt', '') !== 'pad' && btflag('pmt', '') !== 'pp') && $this->plan->currency !== 'USD') {
          if ((btflag('pmt', '') === 'mpay_st' || btflag('pmt', '') === 'mpay_rec') && $this->plan->currency !== 'USD') {
            btflag_set('pmt', 'mpay_st');
            header("Location: " . base_url() . "pay/2FQH9T5Y7P");
          } else {
            header("Location: " . base_url() . "pay/uHTinVqsUl");
          }
          die();
        }
      }

      if (btflag('pmt', null) == 'mpay_rec') {
        if ($redirect_to_stripe == 'yes') {
          header("Location: " . base_url() . "pay/2FQH9T5Y7P");
        } else {
          header("Location: " . base_url() . "pay/LZQ6N8V2A");
        }
        die;
      }
      
      if (btflag('pmt', null) == 'mpay_st') {
        header("Location: ". base_url() ."pay/2FQH9T5Y7P");
        die;
      }
    }

    private function refirectToCardTransaction(){
      $meta_data = [
        "user_pid" => btsessionGET("USER")->user_pid,
        "plan_id" => $this->plan->plan_id,
      ];
      
      if ($this->plan->sct_plan_id==''){
        return;
        // echo "Plan not setup for merchant";
        // exit;
      }

      if (btflag('mode', 'live') == 'test'){
        $sct_mode = getenv("SCT_TEST_KEY");
      }else{
        $sct_mode = '';
      }
      $params = array(
        "email" => btsessionGET("USER")->email,
        "project_code" => $this->sct_api_key['api_project_code'],
        "plan_code" => $this->plan->sct_plan_id,
        "merchant_code" => 'mrcnt-rdhdszknsu',
        "mode" => $sct_mode,
        "meta_data" => json_encode($meta_data),
        "callback_url" => base_url() ."api/process-sct-dl"
      );
      $params = http_build_query($params);

      $ch = curl_init();
      curl_setopt($ch, CURLOPT_URL,$this->sct_api_key['api_end_point']."/api/gen-payment-link");
      curl_setopt($ch, CURLOPT_POST, 1);
      curl_setopt($ch, CURLOPT_POSTFIELDS,$params);
      curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
      curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);        
      curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);

      $headers = [
            'Authorization: Bearer ' . $this->sct_api_key['api_key'],
            'Content-Type: application/x-www-form-urlencoded'
      ];

      curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
      $response = curl_exec($ch);

      $error_msg = '';
      if (curl_errno($ch)) {
        $error_msg = curl_error($ch);
      }        
      curl_close($ch);

      if ($error_msg==''){
        $arr_response = json_decode($response, true);
        if ($arr_response['success']=='1'){
          die(header("Location: ".$arr_response['data']));
        }
      }

      echo "Invalid link:". $error_msg;
      exit;
    }

    private function isUseSecondMerchant($asTotalTransaction=0, $asPercentage=100){
      $digit = substr($asTotalTransaction, -1);

      if ($asPercentage==10 && $digit==9){
        return true;
      }
      if ($asPercentage==20 && ($digit<=9&&$digit>=8)){
        return true;
      }
      if ($asPercentage==30 && ($digit<=9&&$digit>=7)){
        return true;
      }
      if ($asPercentage==40 && ($digit<=9&&$digit>=6)){
        return true;
      }
      if ($asPercentage==50 && ($digit<=9&&$digit>=5)){
        return true;
      }
      if ($asPercentage==60 && ($digit<=9&&$digit>=4)){
        return true;
      }
      if ($asPercentage==70 && ($digit<=9&&$digit>=3)){
        return true;
      }
      if ($asPercentage==80 && ($digit<=9&&$digit>=2)){
        return true;
      }
      if ($asPercentage==90 && ($digit<=9&&$digit>=1)){
        return true;
      }
      if ($asPercentage==100 && ($digit<=9&&$digit>=0)){
        return true;
      }
      return false;
    }

    private function today_date_notime($zone = 'PST'){
        $pst = new \DateTimeZone($zone);
        $date_now = date("F j, Y, g:i a");
        $objDateTo = new \DateTime($date_now);
        $objDateTo->setTimezone($pst);
        return $objDateTo->format('Y-m-d');
    }

    private function theme_arcana()
    {
        if (!$this->checkIfSlugIsUpgradeDowngrade() && btflag('pmt', FLAG_PMT_DEFAULT) === 'pad' ) {
          if ($this->plan->plan_type != 'Enterprise') {
            return $this->theme_arcana_paddle();
          }
        }


        $viewData = [
            'BASE' => $this->themeBaseData,
            'PAGE' => $this->themePageData,
            'DATA' => json_encode($this->data)
        ];

        $twigFile = $this->themeSlug;
        // do not edit this
        $this->bttwig->view("/theme_arcana/index_{$this->themePageVersion}.twig", $viewData);
    }

    private function theme_arcana_paddle()
    {
        $plan_name = isset($this->plan->label) ? $this->plan->label :'';
        $plan_name = str_replace(" ", "", $plan_name);

        $viewData = [
            'BASE' => $this->themeBaseData,
            'PAGE' => $this->themePageData,
            'USER' => [
                'user_pid' => btsessionGet('USER')->user_pid,
                'email' => btsessionGet('USER')->email,
                'mode' => $this->mode
            ],
            'DATA' => [
                'paddle_test_key' => getenv('PADDLE_TEST_KEY'),
                'paddle_live_key' => getenv('PADDLE_LIVE_KEY'),
                'paddle_override' => '',
                'ppg' => btflag('ppg', FLAG_PPG_DEFAULT),
                'plan' => $this->plan,
                'plan_label' => $plan_name,
                'pd' => isset($_GET['pd']) ? $_GET['pd'] : ''
            ]
        ];
        $plan = $this->plan;
        $paddle_override = '';

        if($plan->trial_days) {
            $paddle_id = json_decode($plan->paddle_plan_id, true);
            $paymenttype = new BTPaddle($this->paddle_api_key, $this->mode);
            $payment = new BTPayment($paymenttype);

            $params = [
                'product_id' => $paddle_id[$this->mode],
                'initial_price' => $plan->trial_price,
                'currency' => $plan->currency
            ];
            $result = $payment->generatePayLink($params);
            $paddle_override = $result['data'] ? $result['data'] : '';
        }
        $viewData['DATA']['paddle_override'] = $paddle_override;

        $twigFile = $this->themeSlug;
        // do not edit this
        $this->bttwig->view("/theme_arcana_paddle/index_{$this->themePageVersion}.twig", $viewData);
    }


    private function theme_basilisk()
    {
        if (btflag('pmt', FLAG_PMT_DEFAULT) === 'pad') {
            return $this->theme_arcana_paddle();
        }
 
        $viewData = [
            'BASE' => $this->themeBaseData,
            'PAGE' => $this->themePageData,
        ];

        $twigFile = $this->themeSlug;
        // do not edit this
        $this->bttwig->view("/theme_basilisk/index_{$this->themePageVersion}.twig", $viewData);
    }

    private function theme_druig()
    {
        if (btflag('pmt', FLAG_PMT_DEFAULT) === 'pad') {
            return $this->theme_arcana_paddle();
        }
 
        $viewData = [
            'BASE' => $this->themeBaseData,
            'PAGE' => $this->themePageData,
        ];

        $twigFile = $this->themeSlug;
        // do not edit this
        $this->bttwig->view("/theme_druig/index_{$this->themePageVersion}.twig", $viewData);
    } 
    private function theme_echo()
    {
        $viewData = [
            'BASE' => $this->themeBaseData,
            'PAGE' => $this->themePageData,
        ];

        $twigFile = $this->themeSlug;
        // do not edit this
        $this->bttwig->view("/theme_echo/index_{$this->themePageVersion}.twig", $viewData);
    }

    private function pmt_redirection()
    {
        if (is_null(btflag('pricing', null))) {
          if(btflag('lp', '') == 'aihub') {
            $redirect_url = strpos(base_url(), "staging") ? 'https://staging.chat.ai-pro.org/chat/new' : 'https://chat.ai-pro.org';
            echo "<script> window.top.location.href = '$redirect_url'; </script>";
            exit;
          } else {
            header('Location: ' . base_url() . 'pricing');
            exit;
          }
          die;
        }
        if (!btsessionIsUserLoggedIn()) {
            header("Location: " . base_url() . "register");
            die;
        }

        if (!isset($this->plan['success']) || !isset($this->plan['res'])) {
          if(btflag('lp', '') == 'aihub') {
            $redirect_url = strpos(base_url(), "staging") ? 'https://staging.chat.ai-pro.org/chat/new' : 'https://chat.ai-pro.org/chat/new';
            echo "<script> window.top.location.href = '$redirect_url'; </script>";
          } else {
              header('Location: ' . base_url() . 'pricing');
          }

          die;
        }

        $this->plan = $this->plan['res'][0];
        $stripe_plan_id = ''; 
        $country_code = getGeoCountryCode();
        $country_code = 'us';

        //logic to sent some of the transactions to cardtransaction. 
        //depends on value assigned in env, country and currency should be USD
        //SCT_LOAD_BALANCE and SCT_PERCENT_TRANSACTION 
        if (btflag('force_pmt', '') !== '1' && getenv("SCT_LOAD_BALANCE") == 'yes' && $country_code == 'us') {
          //load balance payment
          $mode = btflag('mode', 'live');
          if (strtolower($this->plan->currency)=='usd'){
            $accountModel = new AccountModel();
            $account = $accountModel->getTransactionCount($mode);
            $transaction_count_all = 0;
            if ($account['success']==1 && count($account['res'])>0){
              $transaction_count_all = $account['res'][0]->transaction_count;
            }

            $isUseCardTransaction = isLoadBalance($transaction_count_all,getenv("SCT_PERCENT_TRANSACTION"));
            if ($isUseCardTransaction){
              btflag_set('pmt', 'sct'); //set to cardtransaction
              $this->refirectToCardTransaction();
            }
            //load balance payment
          }
        }

        if (isset($this->plan->stripe_plan_id) && $this->plan->stripe_plan_id !=''){
          $stripe_plan_id = json_decode($this->plan->stripe_plan_id);

          if (btsessionGet('USER')->mode=='test'){
            $stripe_plan_id = isset($stripe_plan_id->dl_test) ? $stripe_plan_id->dl_test : '';
          }else{
            $stripe_plan_id = isset($stripe_plan_id->dl_live) ? $stripe_plan_id->dl_live : '';
          }

        }

        if ($stripe_plan_id !=''){
          $paymenttype = new BTStripe($this->stripe_api_key, btsessionGet('USER')->mode);
          $payment = new BTPayment($paymenttype);

          $params = [
            'price_id' => $stripe_plan_id,
            'success_url' => base_url()."api/process-st-dl/".btutilGenerateUniqueId(),
            'client_reference_id' => btsessionGET("USER")->user_id,
            'customer_email' => btsessionGET("USER")->email,
          ];

          $result = $payment->generatePayLink($params);
          if ($result['status']=='1'){
            btsessionSet('checkout_sesson_id',["id"=>$result['data']->id.'|'.$this->plan->plan_id.'|'.btsessionGet('USER')->user_pid]);
            header("Location: ".$result['data']->url);
            die;
          }
        } else {
          if (btflag('force_pmt', '') !== '1') {
            $country_code = getGeoCountryCode();
            $plan = $this->plan;

            if ($plan->plan_type != 'Enterprise') {
              if ($country_code != 'us' || $plan->country != 'US' || in_array($plan->currency, ['EUR', 'BRL'])) {
                if (btflag('pmt', '') == 'rec' || btflag('pmt', '') == 'rec2' || btflag('pmt', '') == 'mpay_rec' || btflag('pmt', '') == 'paydl' || btflag('pmt', '') == '') {
                  $trial_price = $plan->trial_price;
                  $currency = $plan->currency;
                  $plan_name = $plan->plan_name;
                  $payment_interval = $plan->payment_interval;
                  $price = $plan->price;
                  $trial_days = $plan->trial_days;
                  $random_id = $this->getRandomX();
                  $email = btsessionGet('USER')->email;

                  $payment_interval = match($payment_interval) {
                    'Monthly' => 'month',
                    'Yearly' => 'year',
                    default => $payment_interval
                  };

                  $ch = curl_init();
                  $post_field = "";
                  $post_field = $post_field."line_items[0][price_data][unit_amount_decimal]=".((float)$trial_price*100);
                  $post_field = $post_field."&line_items[0][quantity]=1";
                  $post_field = $post_field."&line_items[0][price_data][currency]=".$currency;
                  $post_field = $post_field."&line_items[0][price_data][product_data][name]=Trial Amount";

                  $post_field = $post_field."&line_items[1][quantity]=1";
                  $post_field = $post_field."&line_items[1][price_data][currency]=".$currency;
                  $post_field = $post_field."&line_items[1][price_data][product_data][name]=".$plan_name;
                  $post_field = $post_field."&line_items[1][price_data][recurring][interval]=".$payment_interval;
                  $post_field = $post_field."&line_items[1][price_data][recurring][interval_count]=1";
                  $post_field = $post_field."&line_items[1][price_data][unit_amount_decimal]=".($price*100);

                  if ($trial_days>0){
                    $post_field = $post_field."&subscription_data[trial_period_days]=".$trial_days;
                  }

                  $locales = btflag('locales', "en");
                  $locales = strtolower($locales);
                  if ($locales!='' && $locales!='ar'){
                    $post_field = $post_field."&locale=".$locales;
                  }

                  $post_field = $post_field."&mode=subscription";
                  $post_field = $post_field."&client_reference_id=".$random_id;
                  $post_field = $post_field."&customer_email=".urlencode($email);
                  $post_field = $post_field."&success_url=".base_url()."api/process-st-dl/".btutilGenerateUniqueId();

                  $stripe_api_key = btsessionGet('USER')->mode=='test' ? $this->stripe_api_key['api_key_test'] : $this->stripe_api_key['api_key_live'];

                  curl_setopt($ch, CURLOPT_URL, 'https://api.stripe.com/v1/checkout/sessions');
                  curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
                  curl_setopt($ch, CURLOPT_POST, 1);
                  curl_setopt($ch, CURLOPT_POSTFIELDS, $post_field);
                  curl_setopt($ch, CURLOPT_USERPWD, $stripe_api_key . ':' . '');
                  curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);        
                  curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
            
                  $headers = array();
                  $headers[] = 'Content-Type: application/x-www-form-urlencoded';
                  curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);

                  $response = curl_exec($ch);
                  $error_msg = "";

                  if (curl_errno($ch)) {
                      $error_msg = curl_error($ch);
                  }
                  curl_close($ch);

                  if ($error_msg!==''){
                      echo  'ERROR1:'.$error_msg;
                      exit;
                  }

                  $response = json_decode($response);

                  $error = isset($response->error->message) ? $response->error->message : '';

                  if ($error!==''){

                      echo 'ERROR2:'.$error;
                      exit;
                  }

                  $redirect_url = $response->url;

                  btflag_set('pmt', 'paydl');
                  btsessionSet('checkout_sesson_id',["id"=>$response->id.'|'.$plan->plan_id.'|'.btsessionGet('USER')->user_pid]);
                  header("Location: $redirect_url");
                  exit;
                }
              }
            }
          }
        }
        
        $pricing_id = btflag('pricing', null);
        $user_plan_id = btsessionHas('PLAN') ? btsessionGet('PLAN')->plan_id : '';
        if (btsessionGet('USER')->status == 'active') {
          if(!$this->checkIfSubscriptionIsPaddleTrial()) {
            $accountModel = new AccountModel();
            $account = $accountModel->getActiveSubscription(btsessionGet('USER')->user_id);
            
            $selectedPlan = btdbFindBy('PlanModel', 'plan_id', $pricing_id);
            $plan_type = '';
            if ($selectedPlan['success'] == 1 && $selectedPlan['res']) {
              $plan_type = $selectedPlan['res'][0]->plan_type;
              btflag_set('display_txt1', $account['res'][0]->display_txt1);
            }

            if ($account['success'] == 1 && $plan_type!='Enterprise') {
              if ($account['res'][0]->expired == 'no') {
                    $pricing_id = btflag('pricing', null);
                    $user_plan_id = btsessionHas('PLAN') ? btsessionGet('PLAN')->plan_id : '';

                    if($pricing_id != $user_plan_id) {
                      header("Location: " . base_url("downgrade/") . $pricing_id );
                      die;
                    }

                    btsessionGet('USER')->expired = $account['res'][0]->expired;
                    header("Location: " . base_url() . "my-account");
                    die;
                }
            }
          }
        }

        $this->mode = btsessionGet('USER')->mode;

        // Paddle payment page is not in react therefore pageVersioning is not applicable
        if (btflag('pmt', FLAG_PMT_DEFAULT) === 'pad') {
          if ($this->plan->plan_type != 'Enterprise') {
            $this->pageVersion = null;
            btflag('vpay', '');
          }
        }
    }

    private function theme_data()
    {
      $pmt = btflag('pmt', FLAG_PMT_DEFAULT);

      if (strpos(uri_string(), 'mcWiDilmgQ') !== false) {
        $pmt = 'rec';
      }

        $this->themePageData = [
          'include_session' => [
              'ctx' => base_url(),
          ],
          'include_twitter' => true,
          'page_title' => 'AI-Pro | Payment Option',
            'meta_data' => [
                'description' => '',
                // 'robots' => 'noindex, follow', //uncomment and change as needed
            ],
            'include_vwo' => btutilIsVwoOn(),
            'include_fbmeta' => true,
            'include_quora' => true,
            'include_tiktok' => true,
            'include_mixpanel' => [
                'debug' => CONFIG_MIXPANEL_DEBUG,
                'mixpanel_name' => $this->pageSlug,
                'keyword' => btflag('keyword', ''),
                'emailid' => btflag('emailid', ''),
                'adid' => btflag('adid', ''),
                'ppg' => btflag('ppg', FLAG_PPG_DEFAULT),
                'pmt' => $pmt,
                'email' => btflag_cookie('user_email', ''),
                'user_plan' => btflag_cookie('user_plan', ''),
                'howdoiplantouse' => btflag_cookie('howdoiplantouse', ''),
                'remakemedloption' => btflag_cookie('remakemedloption', ''),
            ],
            'include_gtag_AW' => false,
            'include_gtag_GA4' => true,
            'include_bing' => true,
            'include_fullstory' => [
                'email' => btflag_cookie('user_email', ''),
            ],
        ];

        if (btflag_cookie('admin','0')=='1'){
          unset($this->themePageData['include_fullstory']);
          unset($this->themePageData['include_mixpanel']);        
      }
    }

    private function theme_pageVersion()
    {
        $v = btflag('vpay', '');

        switch ($v) {
            case 'bH0w05VJXk':
                header("Location: ". base_url() ."pay/" . $v); die;
            break;
        }
        $this->pmt_flag_redirect();
    }

    private function getRandomX(){
      $n = 16;
      $characters = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
      $randomString = '';
  
      for ($i = 0; $i < $n; $i++) {
          $index = rand(0, strlen($characters) - 1);
          $randomString .= $characters[$index];
      }
  
      return $randomString;
  }
}
