<?php

namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;

class AlterPlanTable20231908 extends Migration
{
    private $table = "plan";

    public function up()
    {
        if (!$this->db->fieldExists('display_txt4', $this->table)){
            $this->db->disableForeignKeyChecks();
            $fields = [
                'display_txt4' => [
                    'type' => 'VARCHAR',
                    'constraint' => 5000,
                    'null' => true,
                    'default' => '',
                    'after' => 'display_txt3'
                ]
            ];
            $this->forge->addColumn($this->table, $fields);
            $this->db->enableForeignKeyChecks();
        }

        if (!$this->db->fieldExists('options', $this->table)){
            $this->db->disableForeignKeyChecks();
            $fields = [
                'options' => [
                    'type' => 'VARCHAR',
                    'constraint' => 5000,
                    'null' => true,
                    'default' => '',
                    'after' => 'plan_description'
                ]
            ];
            $this->forge->addColumn($this->table, $fields);
            $this->db->enableForeignKeyChecks();
        }
    }

    public function down()
    {
        //
    }
}
