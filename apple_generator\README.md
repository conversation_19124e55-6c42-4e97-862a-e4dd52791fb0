# Apple Client Secret Generator

## Instuctions

1.  Go to https://developer.apple.com/
2.  Login the account in Apple for Developers
3.  Go to Certificates, Identifiers & Profiles
4.  Go to Keys in Side navigation
5.  Click the plus (+) button to create new key.
6.  Check the 'Sign in with apple'
    a. You might need to configure 'Sign in with apple' option by attaching a primary app id
    b. Click configure in the rightmost side of the 'Sign in with apple' option and it will show a combobox for the selection of primary app id
    c. Select the primary app id f your choice or if the expected primary app id is not in the options, then you will need to create a new one. Follow the steps below:
    
    c1. In the left side options, you will see an option for the identifiers option. Click it to open it
    c2. Click the plus (+) beside the 'Identifiers' title on the right
    c3. Select 'App Ids' then click continue
    c4. Choose 'App' as the type and click continue
    c5. Fill in the app description and bundle id. For bundle id, choose explicit.
    c6. In the 'Capabilities' options below, check 'Sign In with Apple'
    c7. Click continue then register
    c8. Then create a new identifier and this time create a Service Id. Create the service id first.
    c9. After creating the service, select the service you created and edit. Select the primary app id that you created previously and then check sign in with apple then configure to add domains and return URLS.
        Note: You can copy the domains and return urls from the previous created service Ids which are (AI-Pro or AI-Pro 2)
    c10. If register fails, it is mostly caused by invalid bundle id. The suggested format is `The unique reverse-domain string (E.g. com.ai-pro.start)`
    c11. After register, continue 'Sign in with apple' option in creating a new key and select your newly created app id then save/continue

7. After register, a page will be shown for the created key and an option to download it now. Please download the file and store it somewhere safe cause apple server won't be saving a copy of it.
8.  After creating a file will be downloaded. (.p8 file)
9.  Open `appleClientSecretGenerator.html`
10.  Upload the .p8 file, input Team ID, Client ID and Key ID
11. Click Generate Token
12. Copy the Generated Token and replace the old client secret.