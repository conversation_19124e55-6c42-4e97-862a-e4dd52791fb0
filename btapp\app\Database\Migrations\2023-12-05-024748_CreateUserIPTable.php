<?php

namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;

class CreateUserIPTable extends Migration
{
    private $table = "user_ip";
    public function up()
    {
		$this->db->disableForeignKeyChecks();

		$this->forge->addField([
			'userip_id' => [
				'type' => 'INT',
				'constraint' => 11,
				'unsigned' => false,
				'auto_increment' => true,
			],
			'userip_pid' => [
				'type' => 'CHAR',
                'constraint' => 36,
				'null' => true,
            ],
			'email' => [
				'type' => 'VARCHAR',
                'constraint' => 250,
				'null' => true,
            ],
			'ip_address' => [
				'type' => 'VARCHAR',
				'constraint' => 50,
				'null' => true,
			],
			'created_at' => [
				'type' => 'DATETIME',
				'null' => false,
			],
			'updated_at' => [
				'type' => 'DATETIME',
				'null' => false,
			],
			'deleted_at' => [
				'type' => 'DATETIME',
				'null' => false,
			],
		]);

		$this->forge->addKey('userip_id', true);

		$this->forge->createTable($this->table);
		$this->db->enableForeignKeyChecks();
    }

    public function down()
    {
        //
    }
}
