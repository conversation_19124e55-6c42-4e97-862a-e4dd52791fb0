<?php

namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;

class AlterMembersAreaBannerTable extends Migration
{
    private $table = "members_area_banner";
    public function up()
    {
        $this->db->disableForeignKeyChecks();
        if (!$this->db->fieldExists('created_by', $this->table)) {
            $fields = [
                'created_by' => [
                    'type' => 'VARCHAR',
                    'constraint' => 255,
                    'null' => true,
                    'default' => '',
                    'after' => 'active'
                ]
            ];
            $this->forge->addColumn($this->table, $fields);
        }

        if (!$this->db->fieldExists('updated_by', $this->table)) {
            $fields = [
                'updated_by' => [
                    'type' => 'VARCHAR',
                    'constraint' => 255,
                    'null' => true,
                    'default' => '',
                    'after' => 'active'
                ]
            ];
            $this->forge->addColumn($this->table, $fields);
        }
        $this->db->enableForeignKeyChecks();
    }

    public function down()
    {
        //
    }
}
