<?php

namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;

class AddSocialLoginColumnToUser extends Migration
{
    private $table = 'user';

    public function up()
    {
        $this->forge->addColumn($this->table, [
            'social_login' => [
                'type' => 'INT',
                'constraint' => 1,
                'unsigned' => true,
                'comment' => '1: Social Login Google, 2: Social Login Apple',
            ],
        ]);
    }

    public function down()
    {
        $this->forge->dropColumn($this->table, 'social_login');
    }
}
