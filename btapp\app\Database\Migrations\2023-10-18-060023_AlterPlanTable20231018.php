<?php

namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;

class AlterPlanTable20231018 extends Migration
{
    private $table = "plan";

    public function up()
    {
        //    
        if ($this->db->fieldExists('max_members', $this->table)) return;
        $this->db->disableForeignKeyChecks();
        $fields = [
            'max_members' => [
                'type' => 'VARCHAR',
                'constraint' => 10,
                'null' => true,
                'default' => '',
                'after' => 'currency'
            ]
        ];
        $this->forge->addColumn($this->table, $fields);
        $this->db->enableForeignKeyChecks();

        if ($this->db->fieldExists('price_per_member', $this->table)) return;
        $this->db->disableForeignKeyChecks();
        $fields = [
            'price_per_member' => [
                'type' => 'VARCHAR',
                'constraint' => 10,
                'null' => true,
                'default' => '',
                'after' => 'currency'
            ]
        ];
        $this->forge->addColumn($this->table, $fields);
        $this->db->enableForeignKeyChecks();

    }

    public function down()
    {
        //
    }
}
