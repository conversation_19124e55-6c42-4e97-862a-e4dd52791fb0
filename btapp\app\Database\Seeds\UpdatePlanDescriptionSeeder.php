<?php

namespace App\Database\Seeds;

use App\Enums\PlanType;
use App\Models\PlanModel;
use App\Support\PricingDescription;
use CodeIgniter\Database\Exceptions\DatabaseException;
use CodeIgniter\Database\Seeder;

class UpdatePlanDescriptionSeeder extends Seeder
{
    protected $table = 'plan';

    public function run()
    {
        $planModel = new PlanModel();
        $plans = $planModel->getAllPlans();
        $ppgWithClusterPlan = $planModel->getPPGsWithClusterPlan();
        $ppgWithAdvancedPlan = $planModel->getPPGsWithAdvancedPlan();

        if ($plans['success']) {
            $plans = $plans['res'];
            $ppgWithClusterPlan = $ppgWithClusterPlan['res'];
            $ppgWithAdvancedPlan = $ppgWithAdvancedPlan['res'];

            try {
                $this->db->transException(true)
                    ->transStart();

                foreach ($plans as $plan) {
                    $plan_id = $plan->plan_id;
                    $plan_type = PlanType::tryFrom($plan->plan_type);
                    $ppg = $plan->ppg;
                    $trial_days = $plan->trial_days;
                    $trial_price = $plan->trial_price;
                    $locales = $plan->locales;
                    $is_trial = !is_null($trial_days);
                    $display_txt2 = null;
                    $plan_description = null;
                    $pricing_description = new PricingDescription($plan_type, $plan->currency_symbol, $is_trial, $trial_price);

                    if ($is_trial) {
                        $pricing_description->setPrice($plan->price)
                            ->setTrialDays($trial_days)
                            ->setTrialPrice($trial_price);
                    }

                    if (!is_null($trial_price) && $trial_price == 0) {
                        $display_txt2 = $pricing_description->getWithFreeTrialPlan();
                    } else if (in_array($ppg, $ppgWithClusterPlan)) {
                        $display_txt2 = $pricing_description->getWithClusterPlan($plan->label);
                    } else if (in_array($ppg, $ppgWithAdvancedPlan)) {
                        $display_txt2 = $pricing_description->getWithAdvancedPlan();
                    } else {
                        $display_txt2 = $pricing_description->get();
                    }

                    switch ($ppg) {
                        case '46':
                            $plan_description = $display_txt2;
                            break;
                        case '100':
                        case '102':
                        case '103':
                        case '116':
                            $display_txt2 = 'Generate Images from Text<br>' . $display_txt2;
                            break;
                        case '104':
                        case '105':
                        case '106':
                        case '107':
                            $display_txt2 = 'ChatPDF - Talk to your documents<br>' . $display_txt2;
                            break;
                    }

                    if (!is_null($locales)) {
                        $locales = json_decode($locales, true);

                        foreach (array_keys($locales) as $locale) {
                            $locales[$locale] = $pricing_description->setLocale($locale)
                                ->get();
                        }

                        $locales = json_encode($locales);
                    }

                    $this->db->table($this->table)
                        ->where('plan_id', $plan_id)
                        ->set('display_txt2', $display_txt2)
                        ->set('plan_description', $plan_description)
                        ->set('locales', $locales)
                        ->update();
                }

                $this->db->transComplete();
            } catch (DatabaseException $e) {
                $this->db->transRollback();
            }
        }
    }
}
