DEBUG - 2025-06-17 00:27:39 --> REQ ----------->
INFO - 2025-06-17 00:27:39 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2025-06-17 00:27:40 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-17 00:27:41 --> REQ ----------->14
DEBUG - 2025-06-17 00:27:42 --> REQ ----------->
INFO - 2025-06-17 00:27:42 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-17 00:28:47 --> REQ ----------->
INFO - 2025-06-17 00:28:47 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-17 00:29:21 --> REQ ----------->
INFO - 2025-06-17 00:29:21 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-17 00:56:34 --> REQ ----------->
INFO - 2025-06-17 00:56:34 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
CRITICAL - 2025-06-17 02:52:49 --> Cannot use int as default value for property App\Database\Seeds\InsertPlanTableV2::$plan_id of type string
in APPPATH\Database\Seeds\InsertPlanTableV2.php on line 16.
 1 [internal function]: CodeIgniter\Debug\Exceptions->shutdownHandler()
DEBUG - 2025-06-17 02:59:12 --> REQ ----------->
INFO - 2025-06-17 02:59:12 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-17 02:59:12 --> REQ ----------->
INFO - 2025-06-17 02:59:12 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-17 02:59:16 --> REQ ----------->
INFO - 2025-06-17 02:59:16 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-17 02:59:17 --> REQ ----------->
INFO - 2025-06-17 02:59:17 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-17 02:59:17 --> REQ ----------->
INFO - 2025-06-17 02:59:17 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-17 02:59:28 --> REQ ----------->testuser061125_01@mailinator.com123123
DEBUG - 2025-06-17 02:59:28 --> btdbFindBy ---> 
DEBUG - 2025-06-17 02:59:28 --> btdbFindBy ---> SELECT *
FROM `start-user`
WHERE `email` = '<EMAIL>'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
INFO - 2025-06-17 02:59:28 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-17 02:59:28 --> btdbFindBy ---> SELECT `start-plan`.*, `start-account`.*, DATE_FORMAT(resumed_at , "%M %e, %Y") as "resumed_at_formatted", `start-user`.`mode`, case when DATE_ADD(end_date, INTERVAL 1 DAY) < now() then 'yes' else 'no' end as 'expired'
FROM `start-account`
JOIN `start-user` ON `start-user`.`user_id` = `start-account`.`user_id`
JOIN `start-plan` ON `start-plan`.`plan_id` = `start-account`.`plan_id`
WHERE `start-account`.`user_id` = '2'
ORDER BY `start-account`.`end_date` DESC
DEBUG - 2025-06-17 02:59:28 --> btdbFindBy ---> SELECT *
FROM `start-plan`
WHERE `plan_id` = '143'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-17 02:59:28 --> login-------> <EMAIL>
DEBUG - 2025-06-17 02:59:28 --> btdbFindBy ---> SELECT *
FROM `start-user_app`
WHERE `email` = '<EMAIL>'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
INFO - 2025-06-17 02:59:28 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-17 02:59:29 --> btdbFindBy ---> 
DEBUG - 2025-06-17 02:59:29 --> btdbFindBy ---> SELECT `start-plan`.*, `start-account`.*, DATE_FORMAT(resumed_at , "%M %e, %Y") as "resumed_at_formatted", `start-user`.`mode`, case when DATE_ADD(end_date, INTERVAL 1 DAY) < now() then 'yes' else 'no' end as 'expired'
FROM `start-account`
JOIN `start-user` ON `start-user`.`user_id` = `start-account`.`user_id`
JOIN `start-plan` ON `start-plan`.`plan_id` = `start-account`.`plan_id`
WHERE `start-account`.`user_id` = '2'
ORDER BY `start-account`.`end_date` DESC
DEBUG - 2025-06-17 02:59:29 --> btdbFindBy ---> SELECT *
FROM `start-plan`
WHERE `plan_id` = '143'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-17 02:59:29 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-17 02:59:29 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
WARNING - 2025-06-17 02:59:29 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('aiproStart', null, **********, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(401): btflag_set('aiproStart', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-17 02:59:29 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('user_email', null, **********, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(402): btflag_set('user_email', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-17 02:59:29 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('chatpro_KJLF4XgSL8wjlGm', null, **********, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(403): btflag_set('chatpro_KJLF4XgSL8wjlGm', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-06-17 02:59:30 --> REQ ----------->
INFO - 2025-06-17 02:59:30 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-17 02:59:36 --> REQ ----------->
DEBUG - 2025-06-17 02:59:37 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
DEBUG - 2025-06-17 02:59:37 --> btdbFindBy ---> 
INFO - 2025-06-17 02:59:37 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
WARNING - 2025-06-17 02:59:37 --> [DEPRECATED] strtolower(): Passing null to parameter #1 ($string) of type string is deprecated in APPPATH\Controllers\Api.php on line 1518.
 1 APPPATH\Controllers\Api.php(1518): strtolower(null)
 2 APPPATH\Controllers\Api.php(189): App\Controllers\Api->getSubscription()
 3 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('get-subscription')
 4 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 5 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 6 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 7 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-17 02:59:37 --> [DEPRECATED] strtolower(): Passing null to parameter #1 ($string) of type string is deprecated in APPPATH\Controllers\Api.php on line 1519.
 1 APPPATH\Controllers\Api.php(1519): strtolower(null)
 2 APPPATH\Controllers\Api.php(189): App\Controllers\Api->getSubscription()
 3 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('get-subscription')
 4 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 5 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 6 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 7 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
INFO - 2025-06-17 02:59:37 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-17 02:59:37 --> btdbFindBy ---> 
DEBUG - 2025-06-17 02:59:37 --> btdbFindBy ---> SELECT `start-plan`.*, `start-account`.*, DATE_FORMAT(resumed_at , "%M %e, %Y") as "resumed_at_formatted", `start-user`.`mode`, case when DATE_ADD(end_date, INTERVAL 1 DAY) < now() then 'yes' else 'no' end as 'expired'
FROM `start-account`
JOIN `start-user` ON `start-user`.`user_id` = `start-account`.`user_id`
JOIN `start-plan` ON `start-plan`.`plan_id` = `start-account`.`plan_id`
WHERE `start-account`.`user_id` = '2'
ORDER BY `start-account`.`end_date` DESC
WARNING - 2025-06-17 02:59:37 --> [DEPRECATED] strtolower(): Passing null to parameter #1 ($string) of type string is deprecated in APPPATH\Controllers\User\Settings.php on line 163.
 1 APPPATH\Controllers\User\Settings.php(163): strtolower(null)
 2 APPPATH\Controllers\User\Settings.php(59): App\Controllers\User\Settings->get_subs_data()
 3 APPPATH\Controllers\User\Settings.php(45): App\Controllers\User\Settings->getDiscountPricing()
 4 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\User\Settings->index()
 5 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\User\Settings))
 6 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 7 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 8 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-17 02:59:37 --> [DEPRECATED] strtolower(): Passing null to parameter #1 ($string) of type string is deprecated in APPPATH\Controllers\User\Settings.php on line 164.
 1 APPPATH\Controllers\User\Settings.php(164): strtolower(null)
 2 APPPATH\Controllers\User\Settings.php(59): App\Controllers\User\Settings->get_subs_data()
 3 APPPATH\Controllers\User\Settings.php(45): App\Controllers\User\Settings->getDiscountPricing()
 4 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\User\Settings->index()
 5 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\User\Settings))
 6 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 7 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 8 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-17 02:59:37 --> [DEPRECATED] strtolower(): Passing null to parameter #1 ($string) of type string is deprecated in APPPATH\Controllers\User\Settings.php on line 87.
 1 APPPATH\Controllers\User\Settings.php(87): strtolower(null)
 2 APPPATH\Controllers\User\Settings.php(45): App\Controllers\User\Settings->getDiscountPricing()
 3 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\User\Settings->index()
 4 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\User\Settings))
 5 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 6 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 7 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-06-17 02:59:38 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-17 02:59:38 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
WARNING - 2025-06-17 02:59:38 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('aiproStart', null, 1752721178, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(401): btflag_set('aiproStart', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-17 02:59:38 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('user_email', null, 1752721178, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(402): btflag_set('user_email', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-17 02:59:38 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('chatpro_KJLF4XgSL8wjlGm', null, 1752721178, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(403): btflag_set('chatpro_KJLF4XgSL8wjlGm', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-06-17 02:59:38 --> REQ ----------->
INFO - 2025-06-17 02:59:38 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-17 02:59:44 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-17 02:59:44 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-17 02:59:44 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-17 02:59:44 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-17 02:59:51 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
DEBUG - 2025-06-17 02:59:51 --> btdbFindBy ---> 
INFO - 2025-06-17 02:59:51 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-17 02:59:51 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
DEBUG - 2025-06-17 02:59:51 --> btdbFindBy ---> 
INFO - 2025-06-17 02:59:51 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
WARNING - 2025-06-17 02:59:51 --> [DEPRECATED] strtolower(): Passing null to parameter #1 ($string) of type string is deprecated in APPPATH\Controllers\Api.php on line 1518.
 1 APPPATH\Controllers\Api.php(1518): strtolower(null)
 2 APPPATH\Controllers\Api.php(189): App\Controllers\Api->getSubscription()
 3 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('get-subscription')
 4 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 5 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 6 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 7 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-17 02:59:51 --> [DEPRECATED] strtolower(): Passing null to parameter #1 ($string) of type string is deprecated in APPPATH\Controllers\Api.php on line 1519.
 1 APPPATH\Controllers\Api.php(1519): strtolower(null)
 2 APPPATH\Controllers\Api.php(189): App\Controllers\Api->getSubscription()
 3 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('get-subscription')
 4 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 5 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 6 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 7 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-06-17 02:59:51 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-17 02:59:51 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
WARNING - 2025-06-17 02:59:51 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('aiproStart', null, 1752721191, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(401): btflag_set('aiproStart', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-17 02:59:51 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('user_email', null, 1752721191, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(402): btflag_set('user_email', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-17 02:59:51 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('chatpro_KJLF4XgSL8wjlGm', null, 1752721191, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(403): btflag_set('chatpro_KJLF4XgSL8wjlGm', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-06-17 02:59:51 --> REQ ----------->
INFO - 2025-06-17 02:59:51 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-17 03:00:09 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-17 03:00:09 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
WARNING - 2025-06-17 03:00:09 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('aiproStart', null, 1752721209, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(401): btflag_set('aiproStart', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-17 03:00:09 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('user_email', null, 1752721209, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(402): btflag_set('user_email', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-17 03:00:09 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('chatpro_KJLF4XgSL8wjlGm', null, 1752721209, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(403): btflag_set('chatpro_KJLF4XgSL8wjlGm', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-06-17 03:00:09 --> REQ ----------->
INFO - 2025-06-17 03:00:09 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-17 03:00:21 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-17 03:00:21 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
WARNING - 2025-06-17 03:00:21 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('aiproStart', null, 1752721221, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(401): btflag_set('aiproStart', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-17 03:00:21 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('user_email', null, 1752721221, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(402): btflag_set('user_email', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-17 03:00:21 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('chatpro_KJLF4XgSL8wjlGm', null, 1752721221, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(403): btflag_set('chatpro_KJLF4XgSL8wjlGm', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-06-17 03:00:21 --> REQ ----------->
INFO - 2025-06-17 03:00:21 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2025-06-17 03:00:35 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-17 03:00:35 --> btdbFindBy ---> 
DEBUG - 2025-06-17 03:00:35 --> btdbFindBy ---> SELECT `start-plan`.*, `start-account`.*, DATE_FORMAT(resumed_at , "%M %e, %Y") as "resumed_at_formatted", `start-user`.`mode`, case when DATE_ADD(end_date, INTERVAL 1 DAY) < now() then 'yes' else 'no' end as 'expired'
FROM `start-account`
JOIN `start-user` ON `start-user`.`user_id` = `start-account`.`user_id`
JOIN `start-plan` ON `start-plan`.`plan_id` = `start-account`.`plan_id`
WHERE `start-account`.`user_id` = '2'
ORDER BY `start-account`.`end_date` DESC
WARNING - 2025-06-17 03:00:35 --> [DEPRECATED] strtolower(): Passing null to parameter #1 ($string) of type string is deprecated in APPPATH\Controllers\User\Settings.php on line 163.
 1 APPPATH\Controllers\User\Settings.php(163): strtolower(null)
 2 APPPATH\Controllers\User\Settings.php(59): App\Controllers\User\Settings->get_subs_data()
 3 APPPATH\Controllers\User\Settings.php(45): App\Controllers\User\Settings->getDiscountPricing()
 4 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\User\Settings->index()
 5 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\User\Settings))
 6 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 7 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 8 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-17 03:00:35 --> [DEPRECATED] strtolower(): Passing null to parameter #1 ($string) of type string is deprecated in APPPATH\Controllers\User\Settings.php on line 164.
 1 APPPATH\Controllers\User\Settings.php(164): strtolower(null)
 2 APPPATH\Controllers\User\Settings.php(59): App\Controllers\User\Settings->get_subs_data()
 3 APPPATH\Controllers\User\Settings.php(45): App\Controllers\User\Settings->getDiscountPricing()
 4 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\User\Settings->index()
 5 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\User\Settings))
 6 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 7 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 8 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-17 03:00:35 --> [DEPRECATED] strtolower(): Passing null to parameter #1 ($string) of type string is deprecated in APPPATH\Controllers\User\Settings.php on line 87.
 1 APPPATH\Controllers\User\Settings.php(87): strtolower(null)
 2 APPPATH\Controllers\User\Settings.php(45): App\Controllers\User\Settings->getDiscountPricing()
 3 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\User\Settings->index()
 4 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\User\Settings))
 5 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 6 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 7 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-06-17 03:00:37 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-17 03:00:37 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
WARNING - 2025-06-17 03:00:37 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('aiproStart', null, 1752721237, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(401): btflag_set('aiproStart', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-17 03:00:37 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('user_email', null, 1752721237, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(402): btflag_set('user_email', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-17 03:00:37 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('chatpro_KJLF4XgSL8wjlGm', null, 1752721237, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(403): btflag_set('chatpro_KJLF4XgSL8wjlGm', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-06-17 03:00:37 --> REQ ----------->
INFO - 2025-06-17 03:00:37 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-17 03:00:43 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-17 03:00:43 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-17 03:00:43 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-17 03:00:43 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-17 03:00:55 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
DEBUG - 2025-06-17 03:00:55 --> btdbFindBy ---> 
INFO - 2025-06-17 03:00:55 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2025-06-17 03:00:55 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-17 03:00:55 --> btdbFindBy ---> 
DEBUG - 2025-06-17 03:00:55 --> btdbFindBy ---> SELECT `start-plan`.*, `start-account`.*, DATE_FORMAT(resumed_at , "%M %e, %Y") as "resumed_at_formatted", `start-user`.`mode`, case when DATE_ADD(end_date, INTERVAL 1 DAY) < now() then 'yes' else 'no' end as 'expired'
FROM `start-account`
JOIN `start-user` ON `start-user`.`user_id` = `start-account`.`user_id`
JOIN `start-plan` ON `start-plan`.`plan_id` = `start-account`.`plan_id`
WHERE `start-account`.`user_id` = '2'
ORDER BY `start-account`.`end_date` DESC
WARNING - 2025-06-17 03:00:55 --> [DEPRECATED] strtolower(): Passing null to parameter #1 ($string) of type string is deprecated in APPPATH\Controllers\User\Settings.php on line 163.
 1 APPPATH\Controllers\User\Settings.php(163): strtolower(null)
 2 APPPATH\Controllers\User\Settings.php(59): App\Controllers\User\Settings->get_subs_data()
 3 APPPATH\Controllers\User\Settings.php(45): App\Controllers\User\Settings->getDiscountPricing()
 4 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\User\Settings->index()
 5 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\User\Settings))
 6 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 7 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 8 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-17 03:00:55 --> [DEPRECATED] strtolower(): Passing null to parameter #1 ($string) of type string is deprecated in APPPATH\Controllers\User\Settings.php on line 164.
 1 APPPATH\Controllers\User\Settings.php(164): strtolower(null)
 2 APPPATH\Controllers\User\Settings.php(59): App\Controllers\User\Settings->get_subs_data()
 3 APPPATH\Controllers\User\Settings.php(45): App\Controllers\User\Settings->getDiscountPricing()
 4 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\User\Settings->index()
 5 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\User\Settings))
 6 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 7 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 8 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-17 03:00:55 --> [DEPRECATED] strtolower(): Passing null to parameter #1 ($string) of type string is deprecated in APPPATH\Controllers\User\Settings.php on line 87.
 1 APPPATH\Controllers\User\Settings.php(87): strtolower(null)
 2 APPPATH\Controllers\User\Settings.php(45): App\Controllers\User\Settings->getDiscountPricing()
 3 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\User\Settings->index()
 4 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\User\Settings))
 5 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 6 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 7 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-06-17 03:00:55 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
DEBUG - 2025-06-17 03:00:55 --> btdbFindBy ---> 
INFO - 2025-06-17 03:00:55 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
WARNING - 2025-06-17 03:00:55 --> [DEPRECATED] strtolower(): Passing null to parameter #1 ($string) of type string is deprecated in APPPATH\Controllers\Api.php on line 1518.
 1 APPPATH\Controllers\Api.php(1518): strtolower(null)
 2 APPPATH\Controllers\Api.php(189): App\Controllers\Api->getSubscription()
 3 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('get-subscription')
 4 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 5 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 6 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 7 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-17 03:00:55 --> [DEPRECATED] strtolower(): Passing null to parameter #1 ($string) of type string is deprecated in APPPATH\Controllers\Api.php on line 1519.
 1 APPPATH\Controllers\Api.php(1519): strtolower(null)
 2 APPPATH\Controllers\Api.php(189): App\Controllers\Api->getSubscription()
 3 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('get-subscription')
 4 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 5 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 6 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 7 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
INFO - 2025-06-17 03:00:55 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-17 03:00:55 --> btdbFindBy ---> 
DEBUG - 2025-06-17 03:00:56 --> btdbFindBy ---> SELECT `start-plan`.*, `start-account`.*, DATE_FORMAT(resumed_at , "%M %e, %Y") as "resumed_at_formatted", `start-user`.`mode`, case when DATE_ADD(end_date, INTERVAL 1 DAY) < now() then 'yes' else 'no' end as 'expired'
FROM `start-account`
JOIN `start-user` ON `start-user`.`user_id` = `start-account`.`user_id`
JOIN `start-plan` ON `start-plan`.`plan_id` = `start-account`.`plan_id`
WHERE `start-account`.`user_id` = '2'
ORDER BY `start-account`.`end_date` DESC
WARNING - 2025-06-17 03:00:56 --> [DEPRECATED] strtolower(): Passing null to parameter #1 ($string) of type string is deprecated in APPPATH\Controllers\User\Settings.php on line 163.
 1 APPPATH\Controllers\User\Settings.php(163): strtolower(null)
 2 APPPATH\Controllers\User\Settings.php(59): App\Controllers\User\Settings->get_subs_data()
 3 APPPATH\Controllers\User\Settings.php(45): App\Controllers\User\Settings->getDiscountPricing()
 4 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\User\Settings->index()
 5 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\User\Settings))
 6 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 7 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 8 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-17 03:00:56 --> [DEPRECATED] strtolower(): Passing null to parameter #1 ($string) of type string is deprecated in APPPATH\Controllers\User\Settings.php on line 164.
 1 APPPATH\Controllers\User\Settings.php(164): strtolower(null)
 2 APPPATH\Controllers\User\Settings.php(59): App\Controllers\User\Settings->get_subs_data()
 3 APPPATH\Controllers\User\Settings.php(45): App\Controllers\User\Settings->getDiscountPricing()
 4 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\User\Settings->index()
 5 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\User\Settings))
 6 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 7 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 8 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-17 03:00:56 --> [DEPRECATED] strtolower(): Passing null to parameter #1 ($string) of type string is deprecated in APPPATH\Controllers\User\Settings.php on line 87.
 1 APPPATH\Controllers\User\Settings.php(87): strtolower(null)
 2 APPPATH\Controllers\User\Settings.php(45): App\Controllers\User\Settings->getDiscountPricing()
 3 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\User\Settings->index()
 4 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\User\Settings))
 5 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 6 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 7 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-06-17 03:00:56 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-17 03:00:56 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
WARNING - 2025-06-17 03:00:56 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('aiproStart', null, 1752721256, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(401): btflag_set('aiproStart', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-17 03:00:56 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('user_email', null, 1752721256, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(402): btflag_set('user_email', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-17 03:00:56 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('chatpro_KJLF4XgSL8wjlGm', null, 1752721256, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(403): btflag_set('chatpro_KJLF4XgSL8wjlGm', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-06-17 03:00:56 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
DEBUG - 2025-06-17 03:00:56 --> btdbFindBy ---> 
INFO - 2025-06-17 03:00:56 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
WARNING - 2025-06-17 03:00:56 --> [DEPRECATED] strtolower(): Passing null to parameter #1 ($string) of type string is deprecated in APPPATH\Controllers\Api.php on line 1518.
 1 APPPATH\Controllers\Api.php(1518): strtolower(null)
 2 APPPATH\Controllers\Api.php(189): App\Controllers\Api->getSubscription()
 3 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('get-subscription')
 4 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 5 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 6 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 7 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-17 03:00:56 --> [DEPRECATED] strtolower(): Passing null to parameter #1 ($string) of type string is deprecated in APPPATH\Controllers\Api.php on line 1519.
 1 APPPATH\Controllers\Api.php(1519): strtolower(null)
 2 APPPATH\Controllers\Api.php(189): App\Controllers\Api->getSubscription()
 3 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('get-subscription')
 4 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 5 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 6 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 7 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-06-17 03:00:56 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-17 03:00:56 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
WARNING - 2025-06-17 03:00:56 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('aiproStart', null, 1752721256, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(401): btflag_set('aiproStart', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-17 03:00:56 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('user_email', null, 1752721256, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(402): btflag_set('user_email', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-17 03:00:56 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('chatpro_KJLF4XgSL8wjlGm', null, 1752721256, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(403): btflag_set('chatpro_KJLF4XgSL8wjlGm', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-06-17 03:00:56 --> REQ ----------->
INFO - 2025-06-17 03:00:56 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-17 03:01:02 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-17 03:01:02 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-17 03:01:02 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-17 03:01:02 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-17 03:01:08 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
DEBUG - 2025-06-17 03:01:08 --> btdbFindBy ---> 
INFO - 2025-06-17 03:01:08 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-17 03:01:08 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
DEBUG - 2025-06-17 03:01:08 --> btdbFindBy ---> 
INFO - 2025-06-17 03:01:08 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
WARNING - 2025-06-17 03:01:08 --> [DEPRECATED] strtolower(): Passing null to parameter #1 ($string) of type string is deprecated in APPPATH\Controllers\Api.php on line 1493.
 1 APPPATH\Controllers\Api.php(1493): strtolower(null)
 2 APPPATH\Controllers\Api.php(189): App\Controllers\Api->getSubscription()
 3 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('get-subscription')
 4 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 5 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 6 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 7 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-17 03:01:08 --> [DEPRECATED] strtolower(): Passing null to parameter #1 ($string) of type string is deprecated in APPPATH\Controllers\Api.php on line 1494.
 1 APPPATH\Controllers\Api.php(1494): strtolower(null)
 2 APPPATH\Controllers\Api.php(189): App\Controllers\Api->getSubscription()
 3 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('get-subscription')
 4 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 5 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 6 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 7 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-06-17 03:01:09 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-17 03:01:09 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
WARNING - 2025-06-17 03:01:09 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('aiproStart', null, 1752721269, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(401): btflag_set('aiproStart', null, [...])
 3 APPPATH\Controllers\Api.php(768): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-17 03:01:09 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('user_email', null, 1752721269, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(402): btflag_set('user_email', null, [...])
 3 APPPATH\Controllers\Api.php(768): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-17 03:01:09 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('chatpro_KJLF4XgSL8wjlGm', null, 1752721269, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(403): btflag_set('chatpro_KJLF4XgSL8wjlGm', null, [...])
 3 APPPATH\Controllers\Api.php(768): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-06-17 03:01:09 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
DEBUG - 2025-06-17 03:01:09 --> btdbFindBy ---> 
INFO - 2025-06-17 03:01:09 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
WARNING - 2025-06-17 03:01:09 --> [DEPRECATED] strtolower(): Passing null to parameter #1 ($string) of type string is deprecated in APPPATH\Controllers\Api.php on line 1493.
 1 APPPATH\Controllers\Api.php(1493): strtolower(null)
 2 APPPATH\Controllers\Api.php(189): App\Controllers\Api->getSubscription()
 3 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('get-subscription')
 4 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 5 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 6 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 7 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-17 03:01:09 --> [DEPRECATED] strtolower(): Passing null to parameter #1 ($string) of type string is deprecated in APPPATH\Controllers\Api.php on line 1494.
 1 APPPATH\Controllers\Api.php(1494): strtolower(null)
 2 APPPATH\Controllers\Api.php(189): App\Controllers\Api->getSubscription()
 3 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('get-subscription')
 4 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 5 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 6 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 7 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-06-17 03:01:41 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-17 03:01:41 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
WARNING - 2025-06-17 03:01:41 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('aiproStart', null, 1752721301, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(401): btflag_set('aiproStart', null, [...])
 3 APPPATH\Controllers\Api.php(768): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-17 03:01:41 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('user_email', null, 1752721301, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(402): btflag_set('user_email', null, [...])
 3 APPPATH\Controllers\Api.php(768): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-17 03:01:41 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('chatpro_KJLF4XgSL8wjlGm', null, 1752721301, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(403): btflag_set('chatpro_KJLF4XgSL8wjlGm', null, [...])
 3 APPPATH\Controllers\Api.php(768): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-06-17 03:01:41 --> REQ ----------->
INFO - 2025-06-17 03:01:41 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-17 03:01:43 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-17 03:01:43 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
WARNING - 2025-06-17 03:01:43 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('aiproStart', null, 1752721303, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(401): btflag_set('aiproStart', null, [...])
 3 APPPATH\Controllers\Api.php(768): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-17 03:01:43 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('user_email', null, 1752721303, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(402): btflag_set('user_email', null, [...])
 3 APPPATH\Controllers\Api.php(768): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-17 03:01:43 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('chatpro_KJLF4XgSL8wjlGm', null, 1752721303, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(403): btflag_set('chatpro_KJLF4XgSL8wjlGm', null, [...])
 3 APPPATH\Controllers\Api.php(768): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
INFO - 2025-06-17 03:01:43 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-17 03:01:43 --> btdbFindBy ---> 
DEBUG - 2025-06-17 03:01:43 --> btdbFindBy ---> SELECT `start-plan`.*, `start-account`.*, DATE_FORMAT(resumed_at , "%M %e, %Y") as "resumed_at_formatted", `start-user`.`mode`, case when DATE_ADD(end_date, INTERVAL 1 DAY) < now() then 'yes' else 'no' end as 'expired'
FROM `start-account`
JOIN `start-user` ON `start-user`.`user_id` = `start-account`.`user_id`
JOIN `start-plan` ON `start-plan`.`plan_id` = `start-account`.`plan_id`
WHERE `start-account`.`user_id` = '2'
ORDER BY `start-account`.`end_date` DESC
DEBUG - 2025-06-17 03:01:44 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-17 03:01:44 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
WARNING - 2025-06-17 03:01:44 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('aiproStart', null, **********, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(401): btflag_set('aiproStart', null, [...])
 3 APPPATH\Controllers\Api.php(768): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-17 03:01:44 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('user_email', null, **********, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(402): btflag_set('user_email', null, [...])
 3 APPPATH\Controllers\Api.php(768): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-17 03:01:44 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('chatpro_KJLF4XgSL8wjlGm', null, **********, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(403): btflag_set('chatpro_KJLF4XgSL8wjlGm', null, [...])
 3 APPPATH\Controllers\Api.php(768): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-06-17 03:01:44 --> REQ ----------->
INFO - 2025-06-17 03:01:44 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-17 03:01:46 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
DEBUG - 2025-06-17 03:01:46 --> btdbFindBy ---> 
INFO - 2025-06-17 03:01:46 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-17 03:01:46 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
DEBUG - 2025-06-17 03:01:46 --> btdbFindBy ---> 
INFO - 2025-06-17 03:01:46 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
WARNING - 2025-06-17 03:01:46 --> [DEPRECATED] strtolower(): Passing null to parameter #1 ($string) of type string is deprecated in APPPATH\Controllers\Api.php on line 1493.
 1 APPPATH\Controllers\Api.php(1493): strtolower(null)
 2 APPPATH\Controllers\Api.php(189): App\Controllers\Api->getSubscription()
 3 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('get-subscription')
 4 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 5 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 6 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 7 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-17 03:01:46 --> [DEPRECATED] strtolower(): Passing null to parameter #1 ($string) of type string is deprecated in APPPATH\Controllers\Api.php on line 1494.
 1 APPPATH\Controllers\Api.php(1494): strtolower(null)
 2 APPPATH\Controllers\Api.php(189): App\Controllers\Api->getSubscription()
 3 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('get-subscription')
 4 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 5 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 6 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 7 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-06-17 03:01:46 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-17 03:01:46 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
WARNING - 2025-06-17 03:01:46 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('aiproStart', null, 1752721306, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(401): btflag_set('aiproStart', null, [...])
 3 APPPATH\Controllers\Api.php(768): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-17 03:01:46 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('user_email', null, 1752721306, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(402): btflag_set('user_email', null, [...])
 3 APPPATH\Controllers\Api.php(768): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-17 03:01:46 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('chatpro_KJLF4XgSL8wjlGm', null, 1752721306, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(403): btflag_set('chatpro_KJLF4XgSL8wjlGm', null, [...])
 3 APPPATH\Controllers\Api.php(768): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-06-17 03:01:46 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-17 03:01:46 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-17 03:01:53 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-17 03:01:53 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
WARNING - 2025-06-17 03:01:53 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('aiproStart', null, 1752721313, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(401): btflag_set('aiproStart', null, [...])
 3 APPPATH\Controllers\Api.php(768): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-17 03:01:53 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('user_email', null, 1752721313, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(402): btflag_set('user_email', null, [...])
 3 APPPATH\Controllers\Api.php(768): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-17 03:01:53 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('chatpro_KJLF4XgSL8wjlGm', null, 1752721313, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(403): btflag_set('chatpro_KJLF4XgSL8wjlGm', null, [...])
 3 APPPATH\Controllers\Api.php(768): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-06-17 03:01:53 --> REQ ----------->
INFO - 2025-06-17 03:01:53 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-17 03:02:19 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-17 03:02:19 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
WARNING - 2025-06-17 03:02:19 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('aiproStart', null, 1752721339, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(401): btflag_set('aiproStart', null, [...])
 3 APPPATH\Controllers\Api.php(768): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-17 03:02:19 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('user_email', null, 1752721339, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(402): btflag_set('user_email', null, [...])
 3 APPPATH\Controllers\Api.php(768): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-17 03:02:19 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('chatpro_KJLF4XgSL8wjlGm', null, 1752721339, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(403): btflag_set('chatpro_KJLF4XgSL8wjlGm', null, [...])
 3 APPPATH\Controllers\Api.php(768): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-06-17 03:02:19 --> REQ ----------->
INFO - 2025-06-17 03:02:19 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-17 03:02:37 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-17 03:02:37 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
WARNING - 2025-06-17 03:02:37 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('aiproStart', null, 1752721357, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(401): btflag_set('aiproStart', null, [...])
 3 APPPATH\Controllers\Api.php(768): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-17 03:02:37 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('user_email', null, 1752721357, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(402): btflag_set('user_email', null, [...])
 3 APPPATH\Controllers\Api.php(768): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-17 03:02:37 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('chatpro_KJLF4XgSL8wjlGm', null, 1752721357, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(403): btflag_set('chatpro_KJLF4XgSL8wjlGm', null, [...])
 3 APPPATH\Controllers\Api.php(768): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-06-17 03:02:37 --> REQ ----------->
INFO - 2025-06-17 03:02:37 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-17 03:05:21 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-17 03:05:21 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
WARNING - 2025-06-17 03:05:21 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('aiproStart', null, 1752721521, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(401): btflag_set('aiproStart', null, [...])
 3 APPPATH\Controllers\Api.php(768): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-17 03:05:21 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('user_email', null, 1752721521, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(402): btflag_set('user_email', null, [...])
 3 APPPATH\Controllers\Api.php(768): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-17 03:05:21 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('chatpro_KJLF4XgSL8wjlGm', null, 1752721521, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(403): btflag_set('chatpro_KJLF4XgSL8wjlGm', null, [...])
 3 APPPATH\Controllers\Api.php(768): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-06-17 03:05:21 --> REQ ----------->
INFO - 2025-06-17 03:05:21 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-17 03:05:41 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-17 03:05:41 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
WARNING - 2025-06-17 03:05:41 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('aiproStart', null, 1752721541, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(401): btflag_set('aiproStart', null, [...])
 3 APPPATH\Controllers\Api.php(768): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-17 03:05:41 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('user_email', null, 1752721541, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(402): btflag_set('user_email', null, [...])
 3 APPPATH\Controllers\Api.php(768): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-17 03:05:41 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('chatpro_KJLF4XgSL8wjlGm', null, 1752721541, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(403): btflag_set('chatpro_KJLF4XgSL8wjlGm', null, [...])
 3 APPPATH\Controllers\Api.php(768): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-06-17 03:05:42 --> REQ ----------->
INFO - 2025-06-17 03:05:42 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-17 03:48:43 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-17 03:48:43 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
WARNING - 2025-06-17 03:48:43 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('aiproStart', null, 1752724123, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(401): btflag_set('aiproStart', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-17 03:48:43 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('user_email', null, 1752724123, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(402): btflag_set('user_email', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-17 03:48:43 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('chatpro_KJLF4XgSL8wjlGm', null, 1752724123, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(403): btflag_set('chatpro_KJLF4XgSL8wjlGm', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-06-17 03:48:44 --> REQ ----------->
INFO - 2025-06-17 03:48:44 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-17 04:04:58 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-17 04:04:58 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
WARNING - 2025-06-17 04:04:58 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('aiproStart', null, 1752725098, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(401): btflag_set('aiproStart', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-17 04:04:58 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('user_email', null, 1752725098, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(402): btflag_set('user_email', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-17 04:04:58 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('chatpro_KJLF4XgSL8wjlGm', null, 1752725098, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(403): btflag_set('chatpro_KJLF4XgSL8wjlGm', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-06-17 04:04:58 --> REQ ----------->
INFO - 2025-06-17 04:04:58 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-17 04:05:58 --> REQ ----------->$2y$09$BbfIo8oGFnc7VzwgRlFNKOBqD
INFO - 2025-06-17 04:05:58 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
WARNING - 2025-06-17 04:05:58 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('aiproStart', null, 1752725158, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(401): btflag_set('aiproStart', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-17 04:05:58 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('user_email', null, 1752725158, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(402): btflag_set('user_email', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-17 04:05:58 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('chatpro_KJLF4XgSL8wjlGm', null, 1752725158, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(403): btflag_set('chatpro_KJLF4XgSL8wjlGm', null, [...])
 3 APPPATH\Controllers\Api.php(773): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(142): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\laragon\\www\\app.ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-06-17 04:05:58 --> REQ ----------->
INFO - 2025-06-17 04:05:58 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2025-06-17 04:06:05 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-17 04:06:05 --> btdbFindBy ---> 
DEBUG - 2025-06-17 04:06:06 --> REQ ----------->
INFO - 2025-06-17 04:06:06 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-17 04:06:06 --> REQ ----------->
INFO - 2025-06-17 04:06:06 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
