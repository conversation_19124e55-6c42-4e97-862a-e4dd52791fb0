<?php

namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;

class Alterusertable05132024 extends Migration
{

    private $table = "user";

    public function up()
    {
        if ($this->db->fieldExists('is_fraud', $this->table)) return;
        $this->db->disableForeignKeyChecks();

        $fields = [
            'is_fraud' => [
                'type' => 'VARCHAR',
                'constraint' => 100,
                'null' => true,
                'default' => '',
                'after' => 'flags'
            ]
        ];
        $this->forge->addColumn($this->table, $fields);

        $this->db->enableForeignKeyChecks();

    }

    public function down()
    {
        //
    }
}
