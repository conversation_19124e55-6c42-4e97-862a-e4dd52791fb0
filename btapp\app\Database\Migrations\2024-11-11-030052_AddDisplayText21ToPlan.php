<?php

namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;

class AddDisplayText21ToPlan extends Migration
{
    private $table = "plan";
    public function up()
    {
        if (!$this->db->fieldExists('display_txt2_1', $this->table)){
            $this->db->disableForeignKeyChecks();
            $fields = [
                'display_txt2_1' => [
                    'type'       => 'TEXT',
                    'null'       => true,
                    'default'    => '',
                    'after'      => 'display_txt2',
                ],
            ];
            $this->forge->addColumn($this->table, $fields);
        }
    }

    public function down()
    {
        // $this->forge->dropColumn($this->table, 'display_txt2');

    }
}
