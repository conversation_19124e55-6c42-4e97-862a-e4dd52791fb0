<?php

namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;

class AlterPlanTable extends Migration
{
    private $table = "plan";

    public function up()
    {
        if ($this->db->fieldExists('sct_plan_id', $this->table)) return;
        $this->db->disableForeignKeyChecks();
        $fields = [
            'sct_plan_id' => [
                'type' => 'VARCHAR',
                'constraint' => 40,
                'null' => true,
                'default' => '',
                'after' => 'fs_plan_id'
            ]
        ];
        $this->forge->addColumn($this->table, $fields);
        $this->db->enableForeignKeyChecks();
    }

    public function down()
    {
        //
    }
}
