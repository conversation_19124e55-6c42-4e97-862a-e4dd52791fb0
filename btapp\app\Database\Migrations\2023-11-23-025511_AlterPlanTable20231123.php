<?php

namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;

class AlterPlanTable20231123 extends Migration
{
    private $table = "plan";

    public function up()
    {
        //    
        if ($this->db->fieldExists('sort_order', $this->table)) return;
        $this->db->disableForeignKeyChecks();
        $fields = [
            'sort_order' => [
                'type' => 'VARCHAR',
                'constraint' => 10,
                'null' => true,
                'default' => '',
                'after' => 'currency'
            ]
        ];
        $this->forge->addColumn($this->table, $fields);
        $this->db->enableForeignKeyChecks();
    }

    public function down()
    {
        //
    }
}
