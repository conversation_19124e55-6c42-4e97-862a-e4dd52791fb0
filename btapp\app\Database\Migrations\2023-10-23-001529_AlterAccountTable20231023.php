<?php

namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;

class AlterAccountTable20231023 extends Migration
{
    private $table = "account";

    public function up()
    {
        if (!$this->db->fieldExists('members', $this->table)) {
            $this->db->disableForeignKeyChecks();
            $fields = [
                'members' => [
                    'type' => 'VARCHAR',
                    'constraint' => 10,
                    'null' => true,
                    'default' => '',
                    'after' => 'cc_number_encrypted'
                ]
            ];
            $this->forge->addColumn($this->table, $fields);
        }
        $this->db->enableForeignKeyChecks();
    }

    public function down()
    {
        //
    }
}
