<?php

namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;

class AlterUserTable20231024 extends Migration
{
    private $table = "user";

    public function up()
    {
        if (!$this->db->fieldExists('ent_parent_user_id', $this->table)) {
            $this->db->disableForeignKeyChecks();
            $fields = [
                'ent_parent_user_id' => [
                    'type' => 'INT',
                    'constraint' => 11,
                    'unsigned' => false,
                    'auto_increment' => false,
                    'after' => 'status'
                ]
            ];
            $this->forge->addColumn($this->table, $fields);
            $this->db->enableForeignKeyChecks();
        }
    }

    public function down()
    {
        //
    }
}
